/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dian.common.annotation.BillNo;
import com.dian.common.entity.BaseEntity;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
/**
 * 送样单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-02 16:07:40
 */
@Data
@ApiModel("送样单")
@TableName("base_sample")
@BillNo(prefix="XX",type=BillNo.Type.YYYYMMDD,leng = 4)
@KeySequence(value = "SEQ_BASE_SAMPLE", clazz = Long.class)
public class SampleEntity extends BaseEntity {
	private static final long serialVersionUID = 1L;

    /**
	 * 关联组织ID，默认为0
	 */
    @ApiModelProperty("关联组织ID，默认为0")
    private Long tenantPId;

    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private Long tenantId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String tenantName;

    /**
     * 来源ID
     */
    @ApiModelProperty("来源ID")
    private Long sourceId;

    /**
     * 来源单号
     */
    @ApiModelProperty("来源单号")
    private String sourceNo;

    /**
     * 来源类型
     */
    private Integer sourceType;

    /**
     * 是否需要上传文件
     */
    private Integer isNeedUpFile;

    /**
     * 机构信息
     */
    private Long deptId;
    private String deptCode;
    private String deptName;

    /**
	 * 供应商表id:来源于:scm_bas_vendor.id
	 */
    @ApiModelProperty("供应商表id")
    private Long vendorId;



    /**
	 * 冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_name
	 */
    @ApiModelProperty("供应商表编码")
    private String vendorCode;

    /**
     * 冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_name
     */
    @ApiModelProperty("供应商表编码")
    private String vendorErpCode;

    /**
	 * 冗余字段-供应商表名称:来源于:scm_bas_vendor.vendor_name
	 */
    @ApiModelProperty("供应商表名称")
    private String vendorName;



    /**
	 * 送样单号
	 */
    @ApiModelProperty("送样单号")
    private String sampleNo;



    /**
	 * 送样日期
	 */
    @ApiModelProperty("送样日期")
    @JsonFormat( pattern="yyyy-MM-dd")
    private Date sampleDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 退回原因
     */
    private String returnRemark;

    /**
     * 退回时间
     */
    private Date returnDate;

    /**
	 * 送样单状态 1-待审核;2-待送样;3-待质检;4-质检退回;5-已审核;9-已作废
	 */
    @ApiModelProperty("送样单状态 1-待审核;2-待送样;3-待质检;4-质检退回;5-已审核;9-已作废")
    private Integer sampleStat;

    /**
	 * 是否有效 0-无效;1-有效
	 */
    @ApiModelProperty("是否有效 0-无效;1-有效")
    private Integer isValid;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型 1-内部打样 2-采购打样")
    private Integer demandClassType;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 申请部门
     */
    @ApiModelProperty("申请部门")
    private String applyDeptName;

    /**
     * 申请日期
     */
    @ApiModelProperty("申请日期")
    private Date applyDate;


    /**
	 * 删除标识 0-正常;1-删除
	 */
    @ApiModelProperty("删除标识 0-未删除;1-已删除")
    private Integer deleteFlag;

    /**
     * 送样单料品
     */
    @ApiModelProperty("送样单料品")
    @TableField(exist = false)
    private List<SampleItemEntity> sampleItemEntityList;
}
