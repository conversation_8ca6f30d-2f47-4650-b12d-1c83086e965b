/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dian.common.entity.BaseEntity;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 送样单料品实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-02 16:07:40
 */
@Data
@ApiModel("送样单料品")
@TableName("base_sample_item")
@KeySequence(value = "SEQ_BASE_SAMPLE_ITEM", clazz = Long.class)
public class SampleItemEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 关联组织ID，默认为0
     */
    @ApiModelProperty("关联组织ID，默认为0")
    @NotNull(message = "关联组织ID，默认为0不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long tenantPId;


    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private Long tenantId;

    /**
     * 来源ID
     */
    @ApiModelProperty("来源ID")
    private Long sourceId;

    /**
     * 来源明细ID
     */
    @ApiModelProperty("来源明细ID")
    private Long sourceItemId;

    /**
     * 来源单供应商明细ID
     */
    @ApiModelProperty("来源单供应商明细ID")
    private Long sourceVendorId;

    /**
     * 来源ID
     */
    @ApiModelProperty("来源单号")
    private String sourceNo;

    /**
     * 送样单id:来源于:base_sample.id
     */
    @ApiModelProperty("送样单id:来源于:base_sample.id")
    private Long sampleId;


    /**
     * 料品id:来源于:scm_bas_goods.id
     */
    @ApiModelProperty("料品id")
    private Long goodsId;


    /**
     * 冗余字段-料品ERP品号:来源于:scm_bas_goods.goods_erp_code
     */
    @ApiModelProperty("料品ERP品号")
    @NotBlank(message = "料品ERP品号不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String goodsErpCode;


    /**
     * 冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_code
     */
    @ApiModelProperty("料品供应商品号")
    private String goodsCode;


    /**
     * 冗余字段-料品供应商品名:来源于:scm_bas_goods.goods_name
     */
    @ApiModelProperty("料品供应商品名")
    private String goodsName;


    /**
     * 冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_model
     */
    @ApiModelProperty("物料规格")
    private String goodsModel;


    /**
     * 送样数量
     */
    @ApiModelProperty("送样数量")
    @NotNull(message = "送样数量不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private BigDecimal goodsNum;

    /**
     * 处理状态
     */
    @ApiModelProperty("处理状态")
    private int itemStat;

    /**
     * 供方备注
     */
    @ApiModelProperty("供方备注")
    private String vendorRemark;

    /**
     * 是否退回标识 0-否 1-是
     */
    @ApiModelProperty("是否退回标识 0-否 1-是")
    private Integer isReturn;

    /**
     * 判定结果说明
     */
    @ApiModelProperty("判定结果说明")
    private String remark;

    /**
     * 采购方明细行文件地址
     */
    private String tenantFilePath;

    /**
     * 采购方明细行文件名称
     */
    private String tenantFileName;

    /**
     * 供应商明细行文件地址
     */
    private String vendorFilePath;

    /**
     * 供应商明细行文件名称
     */
    private String vendorFileName;

    /**
     * 检验报告文件名称
     */
    private String inspectionReportFileName;

    /**
     * 检验报告文件路径
     */
    private String inspectionReportFilePath;

    /**
     * 承认书文件名称
     */
    private String recognizeBookFileName;

    /**
     * 承认书文件地址
     */
    private String recognizeBookFilePath;

    /**
     * 质检文件名称
     */
    private String quaFileName;
    /**
     * 质检文件地址
     */
    private String quaFilePath;

    /**
     * 需求数量
     */
    private BigDecimal demandQty;

    /**
     * 送样需求日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    /**
     * 结案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date caseDate;

    /**
     * 结案状态
     */
    private Integer caseStat;

    /**
     * 回复数量
     */
    private BigDecimal replyQuantity;

    /**
     * 回复交期
     */
    private Date replyDeliveryDate;

    /**
     * 答交状态
     */
    private Integer replyState;

    /**
     * 采购员名称
     */
    private String purName;

    /**
     * 采购员编码
     */
    private String purCode;

    /**
     * 采购员ID
     */
    private Long purId;


    private String model;

    private String purpose;

    private Integer isValid;

    private Integer deleteFlag;
}
