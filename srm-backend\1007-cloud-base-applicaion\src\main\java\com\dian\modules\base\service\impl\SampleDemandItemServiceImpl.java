/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 * <p>
 * http://www.9dyun.cn
 * <p>
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.service.impl;
//import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dian.common.utils.BeanConverter;
import com.dian.enums.WhetherEnum;
import com.dian.modules.base.vo.SampleDemandVo;
import com.dian.modules.enums.base.CaseStatEnum;
import com.dian.modules.enums.base.DemandClassTypeEnum;
import com.dian.modules.enums.base.SampleEnums;
import io.seata.spring.annotation.GlobalTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.common.utils.MapUtils;
import com.dian.modules.base.dao.SampleDemandItemDao;
import com.dian.modules.base.entity.SampleDemandEntity;
import com.dian.modules.base.entity.SampleDemandItemEntity;
import com.dian.modules.base.service.SampleDemandItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import org.slf4j.Logger;
import com.dian.common.log.TraceLoggerFactory;

/**
 * 样品需求单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-26 14:25:26
 */
@Service("SampleDemandItemService")
public class SampleDemandItemServiceImpl extends ServiceImpl<SampleDemandItemDao, SampleDemandItemEntity> implements SampleDemandItemService {

    @Autowired
    public SampleDemandItemService sampleDemandItemService;
    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean updateInfo(SampleDemandEntity sampleDemandEntity) {
        List<SampleDemandItemEntity> lineList = sampleDemandEntity.getSampleDemandItemEntityList();
        if (CollectionUtil.isNotEmpty(lineList)) {
            lineList.stream().forEach(item -> {
                if (item.getId() == null) {
                    item.setTenantId(sampleDemandEntity.getTenantId());
                    item.setTenantPId(0L);
                    item.setDemandId(sampleDemandEntity.getId());
                    item.setCaseStat(CaseStatEnum.STAT1.getValue()); // 待分配
                    sampleDemandItemService.save(item);
                } else {
                    sampleDemandItemService.updateById(item);
                }
            });
        }
        return true;

    }

    /**
     * 根据PLM的数据来批量保存
     * @param sampleDemandEntity
     * @param sampleDemandVo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean batchSaveByPlm(SampleDemandEntity sampleDemandEntity, SampleDemandVo sampleDemandVo) {
        sampleDemandVo.getSampleDemandItemEntityList().forEach(item -> {
            SampleDemandItemEntity saveItemData = BeanConverter.convert(item, SampleDemandItemEntity.class);
            SampleDemandItemEntity demandItem = this.getOne(new LambdaQueryWrapper<SampleDemandItemEntity>()
                            .eq(SampleDemandItemEntity::getTenantId, sampleDemandEntity.getTenantId())
                            .eq(SampleDemandItemEntity::getSourceItemId, item.getSourceItemId())
                            .eq(SampleDemandItemEntity::getGoodsId, item.getGoodsId())
                            .eq(SampleDemandItemEntity::getDemandId, sampleDemandEntity.getId()));
            if (ObjectUtil.isEmpty(demandItem)) {
                saveItemData.setTenantId(sampleDemandEntity.getTenantId());
                saveItemData.setTenantPId(0L);
                saveItemData.setDemandId(sampleDemandEntity.getId());
                if (DemandClassTypeEnum.RESEARCH_DEMAND.getValue().equals(sampleDemandEntity.getDemandClassType())) {
                    saveItemData.setCaseStat(CaseStatEnum.STAT2.getValue()); // 内部打样-->已分配
                }else{
                    saveItemData.setCaseStat(CaseStatEnum.STAT1.getValue()); // 采购打样-->待分配
                }
                saveItemData.setIsValid(sampleDemandEntity.getIsValid());
                saveItemData.setDeleteFlag(sampleDemandEntity.getDeleteFlag());
                this.save(saveItemData);
            } else {
                saveItemData.setTenantId(sampleDemandEntity.getTenantId());
                saveItemData.setTenantPId(0L);
                saveItemData.setId(demandItem.getId());
                if (CaseStatEnum.STAT2.getValue().equals(demandItem.getCaseStat()) && WhetherEnum.YES.getCode().equals(sampleDemandEntity.getDeleteFlag())){
                    throw new RuntimeException(StrUtil.format("当前物料{}已被分配，无法关闭", item.getGoodsErpCode()));
                }
                saveItemData.setIsValid(sampleDemandEntity.getIsValid());
                saveItemData.setDeleteFlag(sampleDemandEntity.getDeleteFlag());
                this.updateById(saveItemData);
            }
        });
        return true;
    }

    @Override
    //@Transactional(rollbackFor = Throwable.class) //单体事务控制
    @GlobalTransactional(rollbackFor = Exception.class)//微服务事务控制
    public boolean deleteInfo(Long id) {
        return this.remove(new QueryWrapper<SampleDemandItemEntity>().eq("demand_id", id));

    }

    @Override
    public List<SampleDemandItemEntity> queryLineList(Long id) {
        return this.list(new QueryWrapper<SampleDemandItemEntity>().eq("demand_id", id));

    }

}
