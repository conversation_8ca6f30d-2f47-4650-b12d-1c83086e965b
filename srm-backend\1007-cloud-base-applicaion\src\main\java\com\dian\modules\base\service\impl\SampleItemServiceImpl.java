/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.sys.SysClient;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.utils.MapUtils;
import com.dian.common.validator.Assert;
import com.dian.enums.InspectionResultStatEnum;
import com.dian.enums.WhetherEnum;
import com.dian.modules.base.dao.SampleItemDao;
import com.dian.modules.base.entity.SampleDemandItemEntity;
import com.dian.modules.base.entity.SampleDemandVendorEntity;
import com.dian.modules.base.entity.SampleEntity;
import com.dian.modules.base.entity.SampleItemEntity;
import com.dian.modules.base.service.SampleItemService;
import com.dian.modules.base.service.SampleService;
import com.dian.modules.base.vo.SampleInsResItemVo;
import com.dian.modules.dm.vo.InspectionSheetItemVo;
import com.dian.modules.enums.base.ReplyStateEnum;
import com.dian.modules.enums.base.SampleEnums;
import com.dian.modules.enums.base.SampleItemEnums;
import com.dian.modules.sys.vo.SysOssVO;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 送样单料品服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-02 16:07:40
 */
@Service("SampleItemService")
public class SampleItemServiceImpl extends ServiceImpl<SampleItemDao, SampleItemEntity> implements SampleItemService {

    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Autowired
    public SampleItemService sampleItemService;

    @Autowired
    public SampleService sampleService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysClient sysClient;

    /**
     * 送样单明细新增
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean saveInfo(SampleEntity sampleEntity) {
        //判断送样单中的明细数据是否为空
        if (CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())){
            //送样单明细数据
            List<SampleItemEntity> lineList = sampleEntity.getSampleItemEntityList();
            this.removeByMap(new MapUtils().put("sample_id", sampleEntity.getId()));
            for (SampleItemEntity sampleItem:lineList) {
                //采购方组织id
                sampleItem.setTenantId(commonService.getTenantId());
                //送样单主表id
                sampleItem.setSampleId(sampleEntity.getId());
                //保存
                this.save(sampleItem);
            }
        }
        return true;
    }

    /**
     * 送样单明细更新
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateInfo(SampleEntity sampleEntity) {
//        //判断送样单中的明细数据是否为空
//        if (CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())){
//            //送样单明细数据
//            List<SampleItemEntity> lineList=sampleEntity.getSampleItemEntityList();
//            this.removeByMap(new MapUtils().put("sample_id", sampleEntity.getId()));
//            for (SampleItemEntity sampleItem:lineList) {
//                //采购方组织id
//                sampleItem.setTenantId(commonService.getTenantId());
//                //送样单主表id
//                sampleItem.setSampleId(sampleEntity.getId());
//                //修改
//                this.save(sampleItem);
//            }
        List<SampleItemEntity> sampleItemEntityList = sampleEntity.getSampleItemEntityList();
        if(CollectionUtil.isNotEmpty(sampleItemEntityList)){
            sampleItemEntityList.stream().forEach(item -> {
                if (item.getId() == null) {
                    item.setTenantId(commonService.getTenantId());
                    item.setTenantPId(0L);
                    item.setSampleId(sampleEntity.getId());
                    sampleItemService.save(item);
                } else {
                    sampleItemService.updateById(item);
                }
            });
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean deleteInfo(Long id) {
        return this.remove(new QueryWrapper<SampleItemEntity>().eq("sample_id",id));

    }

    @Override
    public List<SampleItemEntity> queryLineList(Long id) {
        return this.list(new QueryWrapper<SampleItemEntity>().eq("sample_id",id));

    }

    /**
     * 供应商确认送样
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean confirmSampleItem(SampleEntity sampleEntity) {
        if (CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())){
            for (SampleItemEntity sampleItem:sampleEntity.getSampleItemEntityList()) {
                this.updateById(sampleItem);
            }
        }
        return true;
    }

    /**
     * 获取送样单主表信息 + 明细表信息
     * @param id
     * @return
     */
    @Override
    public HashMap<String, Object> getSampleAndItemInfo(Long id) {
        HashMap<String,Object> map = new HashMap<>();
        SampleEntity sample = sampleService.getInfo(id);
        List<SampleItemEntity> sampleItemEntities = this.queryLineList(id);
        map.put("sample",sample);
        map.put("sampleItemEntities",sampleItemEntities);
        return map;
    }

    /**
     * 采购方 送样单质检
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long qualitySampleItem(SampleEntity sampleEntity) {
        if (CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())){
            for (SampleItemEntity sampleItem:sampleEntity.getSampleItemEntityList()) {
                if(StrUtil.isEmptyIfStr(sampleItem.getItemStat())){
                    throw new RRException(String.format("判断结果不能为空"));
                }
                if (StrUtil.isBlankIfStr(sampleItem.getRemark())){
                    throw new RRException(String.format("说明不能为空"));
                }
                this.updateById(sampleItem);
            }
        }
        return sampleEntity.getId();
    }

    /**
     * 根据检验单明细行数据更改送样单质检状态
     * @param inspectionSheetItemVo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean qualityItemByInspection(InspectionSheetItemVo inspectionSheetItemVo) {
        if (SampleItemEnums.NOT.getValue().equals(inspectionSheetItemVo.getInspectionResults())){
            throw new RRException(String.format("当前检验单明细行中有存在检验状态为无的，请重新选择"));
        }
        if (SampleItemEnums.UNQUALIIFIED.getValue().equals(inspectionSheetItemVo.getInspectionResults())){
            if (StrUtil.isEmptyIfStr(inspectionSheetItemVo.getUnQualifiedDescribe())){
                throw new RRException(String.format("当前检验单明细行来源单号[%s]物料编码[%s]的检验状态为不合格，且未填写不合格描述，请填写相关信息",inspectionSheetItemVo.getSourceNo(),inspectionSheetItemVo.getGoodsErpCode()));
            }
        }
        //根据检验单中的来源明细id查询送样单信息
        SampleItemEntity sampleItem = this.getById(inspectionSheetItemVo.getSourceItemId());
        //判断送样单明细数据是否为空
        if (sampleItem == null){
            throw new RRException(String.format("当前检验单明细行来源单号[%s]物料编码[%s]查找不到对应的送样单信息，来源明细id[%s]"
                    ,inspectionSheetItemVo.getSourceNo(),inspectionSheetItemVo.getGoodsErpCode(),inspectionSheetItemVo.getSourceItemId()));
        }
        //将检验单的判定结果赋值到送样单对应明细行中
        sampleItem.setItemStat(inspectionSheetItemVo.getInspectionResults());
        //明细行状态为不合格
        if (SampleItemEnums.UNQUALIIFIED.getValue().equals(sampleItem.getItemStat())){
            //不合格描述
            sampleItem.setRemark(inspectionSheetItemVo.getUnQualifiedDescribe());
        }
        sampleItem.setInspectionReportFileName(inspectionSheetItemVo.getInspectionReportFileName());
        sampleItem.setInspectionReportFilePath(inspectionSheetItemVo.getInspectionReportFilePath());
        this.updateById(sampleItem);
        //查询出检验单中的附件列表信息
        List<SysOssVO> inspectionFlieList = sysClient.queryByOssTableId("dm_inspection_sheet", inspectionSheetItemVo.getInspectionSheetId(), 0L);
        //查询出当前送样单的附件列表信息
        List<SysOssVO> sampleFileList = sysClient.queryByOssTableId("base_sample", sampleItem.getSampleId(), 0L);
        //根据当前行的主表id查询出该主表id下所有的送样明细行数据
        List<SampleItemEntity> sampleItemList = this.queryLineList(sampleItem.getSampleId());
        //根据当前行的主表id查询出该主表id下所有检验合格的送样明细行数据
        List<SampleItemEntity> quaSampleItemList = this.list(new LambdaQueryWrapper<SampleItemEntity>()
                .eq(SampleItemEntity::getSampleId, sampleItem.getSampleId())
                .eq(SampleItemEntity::getItemStat,InspectionResultStatEnum.QUALIFIED.getValue())
        );
        //根据当前行的主表id查询出该主表id下所有检验不合格的送样明细行数据
        List<SampleItemEntity> unQuaList = this.list(new LambdaQueryWrapper<SampleItemEntity>()
                .eq(SampleItemEntity::getSampleId, sampleItem.getSampleId())
                .eq(SampleItemEntity::getItemStat,InspectionResultStatEnum.UNQUALIFIED.getValue())
        );
        //根据当前行的主表id查询出该主表id下所有检验不合格的送样明细行数据
        List<SampleItemEntity> concessionList = this.list(new LambdaQueryWrapper<SampleItemEntity>()
                .eq(SampleItemEntity::getSampleId, sampleItem.getSampleId())
                .eq(SampleItemEntity::getItemStat,InspectionResultStatEnum.CONCESSION.getValue())
        );
        //查询出主表
        SampleEntity sampleEntity = sampleService.getById(sampleItem.getSampleId());
        //当前送样单所有明细行数据 等于 当前送样单检验合格的所有明细行数据时
        if (sampleItemList.size() == quaSampleItemList.size()){
            //更改主表状态4-已质检
            sampleEntity.setSampleStat(SampleEnums.STAT5.getValue());
        }
        //不合格数据大于0
        if(unQuaList.size() > 0){
            //更改主表状态4-已质检
            sampleEntity.setSampleStat(SampleEnums.STAT5.getValue());
        }
        //让步接收数据大于0
        if(concessionList.size() > 0){
            //更改主表状态4-已质检
            sampleEntity.setSampleStat(SampleEnums.STAT5.getValue());
        }
        sampleService.updateById(sampleEntity);
        return false;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean qualitySampleItemData(List<SampleItemEntity> sampleItemEntityList) {
        if (CollectionUtils.isNotEmpty(sampleItemEntityList)){
            for (SampleItemEntity sampleItemEntity:sampleItemEntityList){
                if (StrUtil.isBlankIfStr(sampleItemEntity.getRemark())){
                    throw new RRException(String.format("判定结果说明不能为空"));
                }
                sampleItemEntity.setItemStat(SampleEnums.STAT4.getValue());
                this.updateById(sampleItemEntity);
            }
        }
        return false;
    }

    /**
     * 发布送样单明细数据
     * @param sampleId
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean releaseSampleItem(Long sampleId) {
        //根据送样单id查询
        List<SampleItemEntity> itemList = this.queryLineList(sampleId);
        //明细数据不为空
        if (CollectionUtil.isNotEmpty(itemList)){
            for (SampleItemEntity sampleItemEntity:itemList){
                this.updateById(sampleItemEntity);
            }
        }
        return true;
    }

    /**
     * 批量插入送样单明细数据
     * @param sample
     * @param demandItemList
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchInsertSampleByPlm(SampleEntity sample, List<SampleDemandItemEntity> demandItemList) {
        demandItemList.forEach(demandItem -> {
            SampleItemEntity sampleItem = this.getOne(new LambdaQueryWrapper<SampleItemEntity>()
                    .eq(SampleItemEntity::getTenantId, sample.getTenantId())
                    .eq(SampleItemEntity::getSourceId, sample.getSourceId())
                    .eq(SampleItemEntity::getSourceItemId, demandItem.getId())
                    .eq(SampleItemEntity::getGoodsId, demandItem.getGoodsId())
                    .eq(SampleItemEntity::getDeleteFlag, WhetherEnum.NO.getCode())
                    .eq(SampleItemEntity::getIsValid, WhetherEnum.YES.getCode())
            );
            if (ObjectUtil.isEmpty(sampleItem)) {
                sampleItem = new SampleItemEntity();
                sampleItem.setTenantId(sample.getTenantId());
                sampleItem.setTenantPId(sample.getTenantPId());
                sampleItem.setSampleId(sample.getId());
                sampleItem.setSourceId(sample.getSourceId());
                sampleItem.setSourceItemId(demandItem.getId());
                sampleItem.setSourceNo(sample.getSourceNo());
                sampleItem.setGoodsId(demandItem.getGoodsId());
                sampleItem.setGoodsErpCode(demandItem.getGoodsErpCode());
                sampleItem.setGoodsCode(demandItem.getGoodsCode());
                sampleItem.setGoodsName(demandItem.getGoodsName());
                sampleItem.setGoodsModel(demandItem.getGoodsModel());
                sampleItem.setGoodsNum(demandItem.getDemandQty());
                sampleItem.setDemandDate(demandItem.getDemandDate());
                sampleItem.setDemandQty(demandItem.getDemandQty());
                sampleItem.setCaseDate(demandItem.getDemandDate());
                sampleItem.setModel(demandItem.getModel());
                sampleItem.setPurpose(demandItem.getPurpose());
                sampleItem.setRemark(demandItem.getRemark());
                sampleItem.setCaseStat(SampleEnums.STAT3.getValue());  // 待送样
                sampleItem.setReplyState(ReplyStateEnum.STAT1.getValue()); // 待答交
                sampleItem.setPurName(demandItem.getPurName()); // 采购员名称
                this.save(sampleItem);
            } else {
                sampleItem.setGoodsNum(demandItem.getDemandQty());
                sampleItem.setDemandDate(demandItem.getDemandDate());
                sampleItem.setDemandQty(demandItem.getDemandQty());
                sampleItem.setDemandDate(demandItem.getDemandDate());
                sampleItem.setModel(demandItem.getModel());
                sampleItem.setPurpose(demandItem.getPurpose());
                this.updateById(sampleItem);
            }
        });
        return true;
    }

    /**
     * 批量插入送样单明细数据
     * @param sample
     * @param vendorList
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchInsertSample(SampleEntity sample, List<SampleDemandVendorEntity> vendorList,List<SampleDemandItemEntity> sampleDemandItemList) {
        vendorList.forEach(demandVendor -> {
            SampleDemandItemEntity sampleDemandItem = sampleDemandItemList.stream().filter(goods -> goods.getGoodsId().equals(demandVendor.getGoodsId())).findFirst().orElse(null);
            Assert.isNull(sampleDemandItem, String.format("供应商%s数据查找不到对应的物料", demandVendor.getVendorName()));
            SampleItemEntity sampleItem = this.getOne(new LambdaQueryWrapper<SampleItemEntity>()
                    .eq(SampleItemEntity::getTenantId, sample.getTenantId())
                    .eq(SampleItemEntity::getSourceId, sample.getSourceId())
                    .eq(SampleItemEntity::getSampleId, sample.getId()) // 待定
                    .eq(SampleItemEntity::getSourceItemId, demandVendor.getDemandItemId())
                    .eq(SampleItemEntity::getGoodsId, demandVendor.getGoodsId())
                    .eq(SampleItemEntity::getDeleteFlag, WhetherEnum.NO.getCode())
                    .eq(SampleItemEntity::getIsValid, WhetherEnum.YES.getCode())
            );
            if (ObjectUtil.isEmpty(sampleItem)) {
                sampleItem = new SampleItemEntity();
                sampleItem.setTenantId(sample.getTenantId());
                sampleItem.setTenantPId(sample.getTenantPId());
                sampleItem.setSampleId(sample.getId());
                sampleItem.setSourceId(sample.getSourceId());
                sampleItem.setSourceItemId(demandVendor.getDemandItemId());
                sampleItem.setSourceNo(sample.getSourceNo());
                sampleItem.setGoodsId(sampleDemandItem.getGoodsId());
                sampleItem.setGoodsErpCode(sampleDemandItem.getGoodsErpCode());
                sampleItem.setGoodsCode(sampleDemandItem.getGoodsCode());
                sampleItem.setGoodsName(sampleDemandItem.getGoodsName());
                sampleItem.setGoodsModel(sampleDemandItem.getGoodsModel());
                sampleItem.setGoodsNum(sampleDemandItem.getDemandQty());
                sampleItem.setDemandDate(sampleDemandItem.getDemandDate());
                sampleItem.setDemandQty(sampleDemandItem.getDemandQty());
//                sampleItem.setCaseDate(sampleDemandItem.getDemandDate());
                sampleItem.setModel(sampleDemandItem.getModel());
                sampleItem.setPurpose(sampleDemandItem.getPurpose());
                sampleItem.setCaseStat(SampleEnums.STAT3.getValue()); // 待送样
                sampleItem.setReplyState(ReplyStateEnum.STAT1.getValue()); // 待答交
                sampleItem.setPurName(sampleDemandItem.getPurName()); // 采购员名称
                sampleItem.setPurCode(sampleDemandItem.getPurCode()); // 采购员编码
                sampleItem.setPurId(sampleDemandItem.getPurId()); // 采购员ID
                this.save(sampleItem);
            } else {
                sampleItem.setGoodsNum(sampleDemandItem.getDemandQty());
                sampleItem.setDemandDate(sampleDemandItem.getDemandDate());
                sampleItem.setDemandQty(sampleDemandItem.getDemandQty());
                sampleItem.setDemandDate(sampleDemandItem.getDemandDate());
                sampleItem.setModel(sampleDemandItem.getModel());
                sampleItem.setPurpose(sampleDemandItem.getPurpose());
                this.updateById(sampleItem);
            }
        });
        return true;
    }

    /**
     * 根据PLM同步送样单结果
     * @param detailList
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean syncSampleInsResByPlm(List<SampleInsResItemVo> detailList) {
        // 使用HashSet自动去重
        Set<Long> sampleIdSet = new HashSet<>(detailList.size());

        // 批量查询所有sampleItems
        List<Long> sampleItemIds = detailList.stream()
                .map(SampleInsResItemVo::getSampleItemId)
                .collect(Collectors.toList());

        List<SampleItemEntity> sampleItems = this.listByIds(sampleItemIds);
        Map<Long, SampleItemEntity> itemMap = sampleItems.stream()
                .collect(Collectors.toMap(SampleItemEntity::getId, item -> item));

        // 批量更新样本项
        detailList.forEach(detail -> {
            SampleItemEntity sampleItem = itemMap.get(detail.getSampleItemId());
            Assert.isNull(sampleItem, "送样单明细行数据不存在，id="+detail.getSampleItemId());

            if (ObjectUtil.isNotEmpty(sampleItem)) {
                sampleItem.setItemStat(detail.getInsStatus());
                sampleItem.setRemark(detail.getRemark());
                sampleIdSet.add(sampleItem.getSampleId());
            }
            this.updateById(sampleItem);
        });

        // 批量更新操作
        if (!sampleIdSet.isEmpty()) {
            List<Long> sampleIds = new ArrayList<>(sampleIdSet);
            List<SampleEntity> samples = sampleService.listByIds(sampleIds);
            Map<Long, List<SampleItemEntity>> itemGroupMap = this.list(new LambdaQueryWrapper<SampleItemEntity>().in(SampleItemEntity::getSampleId))
                    .stream()
                    .collect(Collectors.groupingBy(SampleItemEntity::getSampleId));

            // 批量更新样本状态
            samples.forEach(sample -> {
                List<SampleItemEntity> sampleItemList = itemGroupMap.get(sample.getId());
                if (sampleItemList != null && !sampleItemList.isEmpty()) {
                    int itemTotalNum = sampleItemList.size();
                    int itemDoneNum = (int) sampleItemList.stream()
                            .filter(item -> ObjectUtil.isNotEmpty(item.getItemStat()))
                            .count();

                    sample.setSampleStat(itemTotalNum == itemDoneNum ?
                            SampleEnums.STAT7.getValue() :
                            SampleEnums.STAT8.getValue());
                }
            });

            // 批量更新数据库
            sampleService.updateBatchById(samples);
        }
        return true;
    }


    /**
     *  ************************************************* 私有方法 *************************************************
     */
    //转为hashmap
    private  List<HashMap<String, Object>> handleJSONArray(JSONArray jsonArray){
        List list = new ArrayList();
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            HashMap map = new HashMap<String, Object>();
            for (Map.Entry entry : jsonObject.entrySet()) {
                if(entry.getValue() instanceof  JSONArray){
                    map.put((String)entry.getKey(), handleJSONArray((JSONArray)entry.getValue()));
                }else{
                    map.put((String)entry.getKey(), entry.getValue());
                }
            }
            list.add(map);
        }
        return list;
    }
}
