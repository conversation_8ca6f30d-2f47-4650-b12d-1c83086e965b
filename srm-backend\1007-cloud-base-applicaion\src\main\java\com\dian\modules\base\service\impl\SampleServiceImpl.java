/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 * <p>
 * http://www.9dyun.cn
 * <p>
 * 版权所有，侵权必究！
 */
package com.dian.modules.base.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.sys.SysClient;
import com.dian.common.annotation.HandleDoc;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.utils.BeanConverter;
import com.dian.common.utils.ListUtils;
import com.dian.common.utils.PageUtils;
import com.dian.common.utils.Query;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.vo.UserSimpleVO;
import com.dian.enums.WhetherEnum;
import com.dian.modules.base.dao.SampleDao;
import com.dian.modules.base.entity.*;
import com.dian.modules.base.service.*;
import com.dian.modules.base.vo.PlmSampleVO;
import com.dian.modules.base.vo.SampleExportVO;
import com.dian.modules.base.vo.SampleInsResVo;
import com.dian.modules.enums.base.CaseStatEnum;
import com.dian.modules.enums.base.ReplyStateEnum;
import com.dian.modules.enums.base.SampleEnums;
import com.dian.modules.enums.dm.AbnormalTypeEnum;
import com.dian.modules.enums.sys.Ent_EntTypeEnum;
import com.dian.modules.plm.service.PlmApiService;
import com.dian.modules.sys.entity.SysEntEntity;
import com.dian.modules.sys.vo.DeptVO;
import com.dian.modules.sys.vo.SysUserVO;
import com.dian.vo.EmailMessageVo;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.record.PageBreakRecord;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 送样单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-02 16:07:40
 */
@Service("sampleService")
public class SampleServiceImpl extends ServiceImpl<SampleDao, SampleEntity> implements SampleService {

    @Autowired
    public CommonService commonService;
    @Autowired
    public SampleItemService sampleItemService;
    @Autowired
    private SampleService sampleService;
    @Autowired
    public SampleDemandService sampleDemandService;
    @Autowired
    public SampleDemandItemService sampleDemandItemService;
    @Autowired
    public PlmApiService plmApiService;
    protected Logger logger = TraceLoggerFactory.getLogger(getClass());
    @Autowired
    private SysClient sysClient;
    @Autowired
    private SampleDemandVendorServiceImpl sampleDemandVendorService;
    @Autowired
    private VendorServiceImpl vendorService;
    @Autowired
    private EmailService emailService;

    /**
     * 送样单分页
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        //采购方进入列表页面
        if (!StrUtil.isEmptyIfStr(params.get("ifTenant"))) {
            params.put("tenantId", commonService.getTenantId());
        }
        //供应商进入列表页面
        if (!StrUtil.isEmptyIfStr(params.get("ifVendor"))) {
            params.put("vendorId", commonService.getTenantId());
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().querySampleList(page, params);

        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 送样单不分页
     *
     * @param params
     * @return
     */
    @Override
    public List<HashMap<String, Object>> querySampleNoPage(Map<String, Object> params) {
        //采购方进入列表页面
        if (!StrUtil.isEmptyIfStr(params.get("ifTenant"))) {
            params.put("tenantId", commonService.getTenantId());
        }
        //供应商进入列表页面
        if (!StrUtil.isEmptyIfStr(params.get("ifVendor"))) {
            params.put("vendorId", commonService.getTenantId());
        }
        List<HashMap<String, Object>> list = getBaseMapper().querySampleListNoPage(params);
        return list;
    }

    /**
     * 送样单新增
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @HandleDoc
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long saveInfo(SampleEntity sampleEntity) {

        //设置编码，等基础默然初始化数据设置
        sampleEntity.setSampleNo(sysClient.getBillNo("base_sample"));
        sampleEntity.setTenantId(commonService.getTenantId());
        sampleEntity.setTenantName(commonService.getUser().getEnt().getEntName());//客户名称
        sampleEntity.setSampleStat(SampleEnums.STAT1.getValue());
        DeptVO deptVo = sysClient.getByDeptId(sampleEntity.getDeptId());
        sampleEntity.setDeptCode(deptVo.getDeptCode());
        sampleEntity.setDeptName(deptVo.getDeptName());
        //保存时校验
        this.saveCheck(sampleEntity);
        //保存主表
        this.save(sampleEntity);
        //保存从表
        this.sampleItemService.saveInfo(sampleEntity);
        return sampleEntity.getId();
    }

    /**
     * 送样单更新
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long updateInfo(SampleEntity sampleEntity) {

        //修改状态校验
        this.updateCheck(sampleEntity.getId());
        DeptVO deptVo = sysClient.getByDeptId(sampleEntity.getDeptId());
        sampleEntity.setDeptCode(deptVo.getDeptCode());
        sampleEntity.setDeptName(deptVo.getDeptName());

        //更新主表
        this.updateById(sampleEntity);

        //更新从表
        sampleItemService.updateInfo(sampleEntity);
//        sampleItemService.updateBatchById(sampleEntity.getSampleItemEntityList());

        return sampleEntity.getId();
    }

    /**
     * 送样单删除
     *
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean deleteInfo(Long id) {

        //删除状态校验
        this.deleteCheck(id);

        //更新从表
        sampleItemService.deleteInfo(id);

        //更新主表
        this.remove(new QueryWrapper<SampleEntity>().eq("id", id));

        return true;
    }

    /**
     * 送样单详情
     *
     * @param id
     * @return
     */
    @Override
    public SampleEntity getInfo(Long id) {
        SampleEntity sampleEntity = getById(id);
//        List<SampleItemEntity> lineList = sampleItemService.queryLineList(id);
        // 直接查询数据库，不使用缓存
        List<SampleItemEntity> lineList = sampleItemService.getBaseMapper().selectList(
                new QueryWrapper<SampleItemEntity>().eq("sample_id", id)
        );
        sampleEntity.setSampleItemEntityList(lineList);
        return sampleEntity;
    }

    /**
     * 送样单审核
     *
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean checkInfo(Long id) {
        SampleEntity sampleEntity = this.getById(id);
        if (sampleEntity.getSampleStat() == 5) {
            throw new RRException(String.format("送样单不允许重复审核"));
        }
        //已审核
        sampleEntity.setSampleStat(5);
        return this.updateById(sampleEntity);
    }

    /**
     * 质检退回
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long returnQua(SampleEntity sampleEntity) {
        //查询当前送样单数据
        SampleEntity sample = this.getById(sampleEntity.getId());
        this.checkSample(sample);
        //更改主表状态为待送样
        sample.setSampleStat(SampleEnums.STAT2.getValue());
        List<SampleItemEntity> sampleItemEntities = sampleItemService.queryLineList(sampleEntity.getId());
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 供应商拒绝送样通知单
     *
     * @param ids 物料明细行ID数组
     * @return
     */
    @Override
    public Long reject(Long[] ids) {
        SampleItemEntity firstSampleItem = sampleItemService.getById(ids[0]);
        SampleEntity sample = this.getInfo(firstSampleItem.getSampleId());
        if (sample == null) {
            throw new RRException("送样单不存在");
        }
        SampleDemandEntity sampleDemand = sampleDemandService.getInfo(sample.getSourceId());
        if (sampleDemand == null) {
            throw new RRException("送样需求单不存在");
        }
        List<SampleItemEntity> rejectedItems = new ArrayList<>();
        // 更新选中的物料明细行状态为已拒绝
        for (Long id : ids) {
            SampleItemEntity sampleItemEntity = sampleItemService.getById(id);
            // 更新物料明细行状态为已拒绝
            sampleItemEntity.setCaseStat(SampleEnums.STAT11.getValue()); // 已拒绝
            sampleItemService.updateById(sampleItemEntity);
            rejectedItems.add(sampleItemEntity);
            // 更新对应的送样需求单物料明细行状态为已拒绝
            if (sampleItemEntity.getSourceItemId() != null) {
                SampleDemandItemEntity sampleDemandItemEntity = sampleDemandItemService.getById(sampleItemEntity.getSourceItemId());
                if (sampleDemandItemEntity != null) {
                    sampleDemandItemEntity.setCaseStat(CaseStatEnum.STAT3.getValue()); // 已拒绝
                    sampleDemandItemService.updateById(sampleDemandItemEntity);
                }
            }
            // 更新对应的送样需求单供应商明细行状态为已拒绝
            SampleDemandVendorEntity sampleDemandVendorEntity = sampleDemandVendorService.getBaseMapper().selectOne(
                new QueryWrapper<SampleDemandVendorEntity>()
                    .eq("demand_id", sampleDemand.getId())
                    .eq("vendor_id", sample.getVendorId())
                    .eq("goods_id", sampleItemEntity.getGoodsId())
            );
            if (sampleDemandVendorEntity != null) {
                sampleDemandVendorEntity.setCaseStat(CaseStatEnum.STAT3.getValue()); // 已拒绝
                sampleDemandVendorService.updateById(sampleDemandVendorEntity);
            }
            this.sendOperationTypeMsgToPur(sampleItemEntity,"拒绝");
        }
        String shareStatus = "";
        if(sample.getSampleItemEntityList().stream().allMatch(sampleItem -> Objects.equals(sampleItem.getCaseStat(), SampleEnums.STAT11.getValue()))){ // 全部为已拒绝才为true
            shareStatus = "已拒绝";
        }else{
            shareStatus = "已分配";
        }
        List<SampleItemEntity> plmItems = rejectedItems.stream()
            .filter(item -> {
                if (item.getSourceItemId() != null) {
                    SampleDemandItemEntity demandItem = sampleDemandItemService.getById(item.getSourceItemId());
                    return demandItem != null && demandItem.getSourceItemId() != null;
                }
                return false;
            })
            .collect(Collectors.toList());
        if (!plmItems.isEmpty()) {
            this.syncSampleToPlm(sample, plmItems, shareStatus, "待收样", "", shareStatus, "拒绝送样通知单");
        }
        return sample.getId();
    }

    /**
     * 送样单作废
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long doCancel(SampleEntity sampleEntity) {
        SampleEntity sample = this.getById(sampleEntity.getId());
        this.checkSample(sample);
        sample.setSampleStat(SampleEnums.STAT10.getValue());
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 送样单审核
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long doExamine(SampleEntity sampleEntity) {
        SampleEntity sample = this.getById(sampleEntity.getId());
        this.checkSample(sample);
        sample.setSampleStat(SampleEnums.STAT7.getValue());
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 发布送样单
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long releaseSample(SampleEntity sampleEntity) {
        SampleEntity sample = this.getById(sampleEntity.getId());
        //校验数据
        this.checkSample(sample);
        if (!SampleEnums.STAT1.getValue().equals(sample.getSampleStat())) {
            throw new RRException(String.format("当前送样单[%s]的单据状态不为空待发布状态，无法发布", sample.getSampleNo()));
        }
        //将送样单状态更改为待确认
        sample.setSampleStat(SampleEnums.STAT2.getValue());
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 供应商确认需要送样单
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long acceptSample(SampleEntity sampleEntity) {
        SampleEntity sample = this.getById(sampleEntity.getId());
        //校验数据
        this.checkSample(sample);
        if (!SampleEnums.STAT2.getValue().equals(sample.getSampleStat())) {
            throw new RRException(String.format("当前送样单[%s]的单据状态不为空待确认状态，无法确认接受送样", sample.getSampleNo()));
        }
        //将送样单状态更改为待送样
        sample.setSampleStat(SampleEnums.STAT3.getValue());
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 供应商确认送样
     *
     * @param ids
     * @return
     */
    @Override
    public Long confirmSample(Long[] ids) {
        SampleItemEntity firstSampleItem = sampleItemService.getById(ids[0]);
        SampleEntity sample = this.getInfo(firstSampleItem.getSampleId());
        if (sample == null) {
            throw new RRException("送样单不存在");
        }
        List<SampleItemEntity> confirmedItems = new ArrayList<>();
        for(Long id : ids){
            SampleItemEntity sampleItemEntity = sampleItemService.getById(id);
            // 送样单物料明细结案状态更新为待收样
            sampleItemEntity.setCaseDate(new Date()); // 送样时间
            sampleItemEntity.setCaseStat(SampleEnums.STAT4.getValue()); // 待收样
            sampleItemService.updateById(sampleItemEntity);
            confirmedItems.add(sampleItemEntity);
            // 更新送样需求单对应的供应商明细行结案状态为待收样
            SampleDemandVendorEntity sampleDemandVendorEntity = sampleDemandVendorService.getBaseMapper().selectOne(
                new QueryWrapper<SampleDemandVendorEntity>()
                    .eq("demand_id", sampleItemEntity.getSourceId())
                    .eq("vendor_id", sample.getVendorId())
                    .eq("goods_id", sampleItemEntity.getGoodsId())
            );
            if (sampleDemandVendorEntity != null) {
                sampleDemandVendorEntity.setCaseStat(CaseStatEnum.STAT7.getValue()); // 待收样
                sampleDemandVendorService.updateById(sampleDemandVendorEntity);
            }
            this.sendOperationTypeMsgToPur(sampleItemEntity,"确认送样");
        }
        // 回传PLM（本地创建的送样需求单不回传）
        List<SampleItemEntity> plmItems = confirmedItems.stream()
            .filter(item -> {
                if (item.getSourceItemId() != null) {
                    SampleDemandItemEntity demandItem = sampleDemandItemService.getById(item.getSourceItemId());
                    return demandItem != null && demandItem.getSourceItemId() != null;
                }
                return false;
            })
            .collect(Collectors.toList());
        if (!plmItems.isEmpty()) {
            this.syncSampleToPlm(sample, plmItems, "待收样", "待收样", "", "已分配", "确认送样通知单");
        }
        return sample.getId();
    }

    /**
     * 供应商答交送样
     *
     * @param ids
     * @return
     */
    @Override
    public Long replySample(Long[] ids) {
        SampleItemEntity firstSampleItem = sampleItemService.getById(ids[0]);
        SampleEntity sampleEntity = this.getInfo(firstSampleItem.getSampleId());
        if (sampleEntity == null) {
            throw new RRException("送样单不存在");
        }
        List<SampleItemEntity> repliedItems = new ArrayList<>();
        for (Long id : ids) {
            SampleItemEntity sampleItemEntity = sampleItemService.getById(id);
            sampleItemEntity.setReplyState(ReplyStateEnum.STAT2.getValue()); // 已答交
            sampleItemEntity.setCaseStat(SampleEnums.STAT3.getValue()); // 待送样
            sampleItemService.updateById(sampleItemEntity);
            repliedItems.add(sampleItemEntity);
            // 更新送样需求单对应的供应商明细行结案状态为待送样
            SampleDemandVendorEntity sampleDemandVendorEntity = sampleDemandVendorService.getBaseMapper().selectOne(
                new QueryWrapper<SampleDemandVendorEntity>()
                    .eq("demand_id", sampleItemEntity.getSourceId())
                    .eq("vendor_id", sampleEntity.getVendorId())
                    .eq("goods_id", sampleItemEntity.getGoodsId())
            );
            if (sampleDemandVendorEntity != null) {
                sampleDemandVendorEntity.setCaseStat(CaseStatEnum.STAT6.getValue()); // 待送样
                sampleDemandVendorService.updateById(sampleDemandVendorEntity);
            }
            this.sendOperationTypeMsgToPur(sampleItemEntity,"答交");
        }
        List<SampleItemEntity> plmItems = repliedItems.stream()
            .filter(item -> {
                if (item.getSourceItemId() != null) {
                    SampleDemandItemEntity demandItem = sampleDemandItemService.getById(item.getSourceItemId());
                    return demandItem != null && demandItem.getSourceItemId() != null;
                }
                return false;
            })
            .collect(Collectors.toList());
        if (!plmItems.isEmpty()) {
            this.syncSampleToPlm(sampleEntity, plmItems, "待送样", "待收样", "", "已分配", "答交送样通知单");
        }
        return sampleEntity.getId();
    }


    /**
     * 确认收样
     *
     * @param ids
     * @return
     */
    @Override
    public Long confirmRecSample(Long[] ids) {
        SampleItemEntity firstSampleItem = sampleItemService.getById(ids[0]);
        SampleEntity sample = this.getInfo(firstSampleItem.getSampleId());
        if (sample == null) {
            throw new RRException("送样单不存在");
        }
        List<SampleItemEntity> receivedItems = new ArrayList<>();
        String receiptDate = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"); // 收货日期
        for(Long id : ids){
            SampleItemEntity sampleItemEntity = sampleItemService.getById(id);
            //将送样单物料明细结案状态更新为待检验
            sampleItemEntity.setCaseStat(SampleEnums.STAT5.getValue()); // 待检验
            sampleItemService.updateById(sampleItemEntity);
            receivedItems.add(sampleItemEntity);
            // 更新送样需求单对应的供应商明细行结案状态为已收样
            SampleDemandVendorEntity sampleDemandVendorEntity = sampleDemandVendorService.getBaseMapper().selectOne(
                new QueryWrapper<SampleDemandVendorEntity>()
                    .eq("demand_id", sampleItemEntity.getSourceId())
                    .eq("vendor_id", sample.getVendorId())
                    .eq("goods_id", sampleItemEntity.getGoodsId())
            );
            if (sampleDemandVendorEntity != null) {
                sampleDemandVendorEntity.setCaseStat(CaseStatEnum.STAT8.getValue()); // 已收样
                sampleDemandVendorService.updateById(sampleDemandVendorEntity);
            }
        }
        List<SampleItemEntity> plmItems = receivedItems.stream()
            .filter(item -> {
                if (item.getSourceItemId() != null) {
                    SampleDemandItemEntity demandItem = sampleDemandItemService.getById(item.getSourceItemId());
                    return demandItem != null && demandItem.getSourceItemId() != null;
                }
                return false;
            })
            .collect(Collectors.toList());
        if (!plmItems.isEmpty()) {
            this.syncSampleToPlm(sample, plmItems, "已收样", "已收样", receiptDate, "已分配", "确认收样送样通知单");
        }
        return sample.getId();
    }

    /**
     * 根据供应商编码查询供应商送样单
     *
     * @param vendorCode
     * @return
     */
    @Override
    public SampleEntity findVendorSampleDeliveryByCode(String vendorCode) {
        SampleEntity sampleEntity = getOne(new QueryWrapper<SampleEntity>()
                .eq("vendor_code", vendorCode)
                .eq("tenant_id", commonService.getTenantId())
        );
        if (sampleEntity != null) {
            List<SampleItemEntity> lineList = sampleItemService.queryLineList(sampleEntity.getId());
            sampleEntity.setSampleItemEntityList(lineList);
            return sampleEntity;
        }
        return null;
    }

    /**
     * 质检供应商送样单
     *
     * @param sampleEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long qualitySampleData(SampleEntity sampleEntity) {
        SampleEntity sample = this.getById(sampleEntity.getId());
        this.checkSample(sample);
        if (!SampleEnums.STAT4.getValue().equals(sample.getSampleStat())) {
            throw new RRException(String.format("当前送样单[%s]的单据状态不为空待质检状态，无法确认送样", sample.getSampleNo()));
        }
        //将送样单状态更改为已质检
        sample.setSampleStat(SampleEnums.STAT5.getValue());
        sampleItemService.qualitySampleItem(sampleEntity);
        this.updateById(sample);
        return sample.getId();
    }

    /**
     * 根据采购方组织id+供应商来源id查询送样单信息
     *
     * @param tenantId
     * @param vendorId
     * @return
     */
    @Override
    public SampleEntity getSampleByTenantAndVendorId(Long tenantId, Long vendorId) {
        SampleEntity sampleEntity = this.getOne(new LambdaQueryWrapper<SampleEntity>()
                .eq(SampleEntity::getTenantId, tenantId)
                .eq(SampleEntity::getVendorId, vendorId)
                .lt(SampleEntity::getSampleStat, SampleEnums.STAT10.getValue())
        );
        if (sampleEntity != null) {
            List<SampleItemEntity> sampleItemEntityList = sampleItemService.queryLineList(sampleEntity.getId());
            sampleEntity.setSampleItemEntityList(sampleItemEntityList);
        }
        return sampleEntity;
    }

    /**
     * 根据采购方组织id+供应商来源id查询未审核送样单信息
     */
    @Override
    public List<SampleEntity> querySampleList(Long tenantId, Long vendorId) {
        List<SampleEntity> list = this.list(new LambdaQueryWrapper<SampleEntity>()
                .eq(SampleEntity::getTenantId, tenantId)
                .eq(SampleEntity::getVendorId, vendorId)
                .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
                .lt(SampleEntity::getSampleStat, SampleEnums.STAT10.getValue()));
        return list;
    }

    /**
     * 根据采购方组织id+供应商来源id查询送样单信息(只查询已审核数据)
     */
    @Override
    public List<SampleEntity> querySampleReviewedList(Long tenantId, Long vendorId) {
        List<SampleEntity> list = this.list(new LambdaQueryWrapper<SampleEntity>()
                .eq(SampleEntity::getTenantId, tenantId)
                .eq(SampleEntity::getVendorId, vendorId)
                .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
                .eq(SampleEntity::getSampleStat, SampleEnums.STAT7.getValue()));
        return list;
    }

    /**
     * 获取送样单汇总数
     *
     * @param params
     * @return
     */
    @Override
    public HashMap getSampleCountMap(Map<String, Object> params) {
        HashMap sampleCountMap = new HashMap();
        Map querySampleMap = new HashMap();
        Map querySampleItemMap = new HashMap();
        SysEntEntity sysEnt = commonService.getTenantInfo();
        //组织类型为采购方
        if (Ent_EntTypeEnum.PURCHASER.getValue().equals(sysEnt.getEntType())) {
            querySampleMap.put("tenantId", commonService.getTenantId());
            querySampleItemMap.put("tenantId", commonService.getTenantId());
        } else {
            querySampleMap.put("vendorId", commonService.getTenantId());
            querySampleItemMap.put("vendorId", commonService.getTenantId());
        }
        querySampleMap.put("whereType", 1);
        //待确认数
        sampleCountMap.put("toBeConfirmedCount", this.getBaseMapper().getSampleCount(querySampleMap));
        querySampleMap.remove("whereType");
        querySampleMap.put("whereType", 2);
        //已确认数
        sampleCountMap.put("confirmedCount", this.getBaseMapper().getSampleCount(querySampleMap));
        querySampleMap.remove("whereType");
        querySampleMap.put("whereType", 3);
        //待送样数
        sampleCountMap.put("toBeDeliveredCount", this.getBaseMapper().getSampleCount(querySampleMap));
        querySampleMap.remove("whereType");
        //待检验数
        querySampleMap.put("whereType", 4);
        sampleCountMap.put("toBeInspectedCount", this.getBaseMapper().getSampleCount(querySampleMap));
        querySampleItemMap.put("whereType", 1);
        //检验合格数
        sampleCountMap.put("qualifiedCount", this.getBaseMapper().getSampleItemCount(querySampleItemMap));
        querySampleItemMap.remove("whereType");
        querySampleItemMap.put("whereType", 2);
        //检验不合格数
        sampleCountMap.put("unQualifiedCount", this.getBaseMapper().getSampleItemCount(querySampleItemMap));
        querySampleItemMap.remove("whereType");
        querySampleItemMap.put("whereType", 3);
        //检验部分合格数
        sampleCountMap.put("partQualifiedCount", this.getBaseMapper().getSampleItemCount(querySampleItemMap));
        //异常单待确认数量
        List<HashMap<String, Object>> abnormalCount = this.getBaseMapper().getAbnormalCount(querySampleItemMap);
        Integer otherCount = 0;
        Integer otherActive = 0;
        if (CollectionUtils.isNotEmpty(abnormalCount)) {
            for (HashMap<String, Object> stringObjectHashMap : abnormalCount) {
                if (stringObjectHashMap.get("abnormalType").equals(AbnormalTypeEnum.AFTER_SALE.getValue())) {
                    sampleCountMap.put("afterSaleAbnormalCount", stringObjectHashMap.get("totalCount"));
                    sampleCountMap.put("afterSaleAbnormalActive", stringObjectHashMap.get("activeCount"));
                } else if (stringObjectHashMap.get("abnormalType").equals(AbnormalTypeEnum.PROD.getValue())) {
                    sampleCountMap.put("productionAbnormalCount", stringObjectHashMap.get("totalCount"));
                    sampleCountMap.put("productionAbnormalActive", stringObjectHashMap.get("activeCount"));
                } else if (stringObjectHashMap.get("abnormalType").equals(AbnormalTypeEnum.PROCESS.getValue())) {
                    sampleCountMap.put("processQualityAbnormalCount", stringObjectHashMap.get("totalCount"));
                    sampleCountMap.put("processQualityAbnormalActive", stringObjectHashMap.get("activeCount"));
                } else {
                    Object totalCount = stringObjectHashMap.get("totalCount");
                    Object activeCount = stringObjectHashMap.get("activeCount");
                    if (totalCount != null && activeCount != null) {
                        otherCount += new Integer(String.valueOf(totalCount));
                        otherActive += new Integer(String.valueOf(activeCount));
                    }
                }
            }
        }
        sampleCountMap.put("otherAbnormalCount", otherCount);
        sampleCountMap.put("otherAbnormalActive", otherActive);
        return sampleCountMap;
    }

    /**
     * 根据PLM数据批量插入送样单
     *
     * @param sampleDemand
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchInsertSampleByPlm(SampleDemandEntity sampleDemand) {
        List<SampleDemandItemEntity> sampleDemandItemList = sampleDemandItemService.queryLineList(sampleDemand.getId());
        if (CollectionUtils.isEmpty(sampleDemandItemList)) {
            throw new RRException("未查询到送样需求单明细");
        }
        // 根据供应商分组
        Map<Long, List<SampleDemandItemEntity>> vendorGroup = sampleDemandItemList.stream().collect(Collectors.groupingBy(SampleDemandItemEntity::getVendorId));
        vendorGroup.forEach((vendorId, demandItemList) -> {
            // 查询送样单通知是否存在
            SampleEntity sample = this.getOne(new LambdaQueryWrapper<SampleEntity>()
                    .eq(SampleEntity::getTenantId, commonService.getTenantId())
                    .eq(SampleEntity::getSourceId, sampleDemand.getId())
                    .eq(SampleEntity::getVendorId, vendorId)
                    .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
            );
            if (ObjectUtil.isEmpty(sample)) {
                sample = new SampleEntity();
                sample.setTenantId(commonService.getTenantId());
                sample.setSourceId(sampleDemand.getId());
                sample.setSourceType(sampleDemand.getDemandType());
                sample.setDemandClassType(sampleDemand.getDemandClassType());
                sample.setSourceNo(sampleDemand.getDemandNo());
                sample.setSampleNo(sysClient.getBillNo("base_sample"));
                sample.setDeptId(sampleDemand.getOrgId());
                sample.setDeptCode(sampleDemand.getOrgCode());
                sample.setDeptName(sampleDemand.getOrgName());
                sample.setSampleStat(SampleEnums.STAT3.getValue());
                sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
                sample.setVendorId(demandItemList.get(0).getVendorId());
                sample.setVendorCode(demandItemList.get(0).getVendorCode());
                sample.setVendorName(demandItemList.get(0).getVendorName());
                sample.setIsValid(WhetherEnum.YES.getCode());
                sample.setDeleteFlag(WhetherEnum.NO.getCode());
                sample.setApplicant(sampleDemand.getApplicant()); // 申请人
                sample.setApplyDeptName(sampleDemand.getApplyDeptName());
                sample.setApplyDate(sampleDemand.getApplyDate());
                this.save(sample);
                sampleItemService.batchInsertSampleByPlm(sample, demandItemList);
            } else {
                sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
                sampleItemService.batchInsertSampleByPlm(sample, demandItemList);
                this.updateInfo(sample);
            }
        });
        return true;
    }

    /**
     * 根据PLM数据批量插入送样单（内部打样自动生成送样单）
     *
     * @param sampleDemand
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchAutoInsertSampleByPlm(SampleDemandEntity sampleDemand) {
        List<SampleDemandItemEntity> sampleDemandItemList = sampleDemandItemService.queryLineList(sampleDemand.getId());
        if (CollectionUtils.isEmpty(sampleDemandItemList)) {
            throw new RRException("未查询到送样需求单明细");
        }
        // 查询送样单通知是否已存在
        SampleEntity sample = this.getOne(new LambdaQueryWrapper<SampleEntity>()
                .eq(SampleEntity::getTenantId, commonService.getTenantId())
                .eq(SampleEntity::getSourceId, sampleDemand.getId())
                .eq(SampleEntity::getDemandClassType, sampleDemand.getDemandClassType())
                .eq(SampleEntity::getSourceNo, sampleDemand.getDemandNo())
                .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
        );
        VendorEntity vendor = vendorService.getOne(new LambdaQueryWrapper<VendorEntity>()
                .eq(VendorEntity::getTenantId, commonService.getTenantId())
                .eq(VendorEntity::getId, 25155L) // 内部打样供应商默认是线材车间
                .eq(VendorEntity::getDeleteFlag, WhetherEnum.NO.getCode())
        );
        if (ObjectUtil.isEmpty(sample)) {
            sample = new SampleEntity();
            sample.setTenantId(commonService.getTenantId());
            sample.setSourceId(sampleDemand.getId());
            sample.setSourceType(sampleDemand.getDemandType());
            sample.setDemandClassType(sampleDemand.getDemandClassType());
            sample.setSourceNo(sampleDemand.getDemandNo());
            sample.setSampleNo(sysClient.getBillNo("base_sample"));
            sample.setDeptId(sampleDemand.getOrgId());
            sample.setDeptCode(sampleDemand.getOrgCode());
            sample.setDeptName(sampleDemand.getOrgName());
            sample.setSampleStat(SampleEnums.STAT3.getValue()); // 待送样
            sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
            sample.setVendorId(25155L);
            sample.setVendorCode(vendor.getVendorErpCode());
            sample.setVendorName(vendor.getVendorName());
            sample.setIsValid(WhetherEnum.YES.getCode());
            sample.setDeleteFlag(WhetherEnum.NO.getCode());
            sample.setApplicant(sampleDemand.getApplicant()); // 申请人
            sample.setApplyDeptName(sampleDemand.getApplyDeptName());
            sample.setApplyDate(sampleDemand.getApplyDate());
            this.save(sample);
            sampleItemService.batchInsertSampleByPlm(sample, sampleDemandItemList);
        } else {
            sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
            sampleItemService.batchInsertSampleByPlm(sample, sampleDemandItemList);
            this.updateInfo(sample);
        }
        return true;
    }

    /**
     * 分配下发生成供应商送样单
     *
     * @param sampleDemand
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean issuedSample(SampleDemandEntity sampleDemand) {
        Assert.isNull(sampleDemand, "送样需求单信息不能为空");
        if (CollectionUtil.isEmpty(sampleDemand.getSampleDemandItemEntityList())) {
            throw new RRException("未查询到送样需求单明细");
        }
        if (CollectionUtil.isEmpty(sampleDemand.getSampleDemandVendorEntityList())) {
            throw new RRException("未查询到送样需求单供应商明细");
        }
        Map<Long, List<SampleDemandVendorEntity>> vendorGroup = sampleDemand.getSampleDemandVendorEntityList().stream().collect(Collectors.groupingBy(SampleDemandVendorEntity::getVendorId));
        vendorGroup.forEach((vendorId, demandVendorList) -> {
            // 查询送样单通知是否存在
            SampleEntity sample = this.getOne(new LambdaQueryWrapper<SampleEntity>()
                    .eq(SampleEntity::getTenantId, commonService.getTenantId())
                    .eq(SampleEntity::getSourceId, sampleDemand.getId())
                    .eq(SampleEntity::getVendorId, vendorId)
                    .eq(SampleEntity::getDeleteFlag, WhetherEnum.NO.getCode())
            );
            if (ObjectUtil.isEmpty(sample)) {
                sample = new SampleEntity();
                sample.setTenantId(commonService.getTenantId());
                sample.setSourceId(sampleDemand.getId());
                sample.setSourceType(sampleDemand.getDemandType());
                sample.setDemandClassType(sampleDemand.getDemandClassType());
                sample.setSourceNo(sampleDemand.getDemandNo());
                sample.setSampleNo(sysClient.getBillNo("base_sample"));
                sample.setDeptId(sampleDemand.getOrgId());
                sample.setDeptCode(sampleDemand.getOrgCode());
                sample.setDeptName(sampleDemand.getOrgName());
                sample.setSampleStat(SampleEnums.STAT3.getValue());
                sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
                sample.setVendorId(demandVendorList.get(0).getVendorId());
                sample.setVendorCode(demandVendorList.get(0).getVendorCode());
                sample.setVendorName(demandVendorList.get(0).getVendorName());
                sample.setIsValid(WhetherEnum.YES.getCode());
                sample.setDeleteFlag(WhetherEnum.NO.getCode());
                sample.setApplicant(sampleDemand.getApplicant()); // 申请人
                sample.setApplyDeptName(sampleDemand.getApplyDeptName());
                sample.setApplyDate(sampleDemand.getApplyDate());
                this.save(sample);
                sampleItemService.batchInsertSample(sample, demandVendorList, sampleDemand.getSampleDemandItemEntityList());

//                if (sampleDemand.getSampleDemandItemEntityList().get(0).getSourceItemId() != null) {
                if(sampleDemand.getSampleDemandItemEntityList().stream().allMatch(item -> item.getSourceItemId() != null)){
                    // 生成送样通知单后将信息同步至PLM
                    SampleEntity sampleEntity = this.getInfo(sample.getId());
                    if (sampleEntity != null && CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())) {
                        syncSampleToPlm(sampleEntity, sampleEntity.getSampleItemEntityList(), "待送样", "待收样", "", "已分配", "生成送样通知单");
                    }
                }
            } else {
                sample.setIsNeedUpFile(sampleDemand.getIsNeedUpFile());
                sampleItemService.batchInsertSample(sample, demandVendorList, sampleDemand.getSampleDemandItemEntityList());
                this.updateInfo(sample);

//                if (sampleDemand.getSampleDemandItemEntityList().get(0).getSourceItemId() != null) {
                if(sampleDemand.getSampleDemandItemEntityList().stream().allMatch(item -> item.getSourceItemId() != null)){
                    // 更新送样通知单后将信息同步至PLM
                    SampleEntity sampleEntity = this.getInfo(sample.getId());
                    if (sampleEntity != null && CollectionUtil.isNotEmpty(sampleEntity.getSampleItemEntityList())) {
                        syncSampleToPlm(sampleEntity, sampleEntity.getSampleItemEntityList(), "待送样", "待收样", "", "已分配", "更新送样通知单");
                    }
                }
            }
        });
        return false;
    }

    /**
     * 根据PLM传入的信息将送样单退回
     *
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean returnSampleByPlm(List<SampleInsResVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            throw new RRException("送样单退回信息不能为空");
        }
        list.forEach(sampleInsResVo -> {
            // 查询送样通知单信息
            SampleEntity sampleEntity = this.getOne(new LambdaQueryWrapper<SampleEntity>().eq(SampleEntity::getSampleNo, sampleInsResVo.getSampleNo()));
            Assert.isNull(sampleEntity, "送样单信息不存在");
            sampleEntity.setSampleStat(SampleEnums.STAT6.getValue());
            sampleEntity.setReturnRemark(sampleInsResVo.getRemark());
            sampleEntity.setReturnDate(new Date());
            this.updateInfo(sampleEntity);
            try {
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId", sampleEntity.getVendorId());
                mailJson.put("userId", 0L);
                mailJson.put("content", sampleEntity.getDeptName() + "整单退回了送样单:" + sampleEntity.getSampleNo());
                mailJson.put("menuTitle", "送样单详情");//菜单标题
                mailJson.put("url", "base/sample/vendor");//菜单地址
                // 发送站内信信息
                sysClient.sendMail(mailJson);
            } catch (Exception e) {
                log.error("送样单退回发送站内信信息异常", e);
            }
        });
        return true;
    }

    /**
     * 送样单当前页or全部导出
     *
     * @param params
     * @return
     */
    @Override
    public List<SampleExportVO> exportList(Map<String, Object> params) {
        params.put("tenantId", commonService.getTenantId());
        List<HashMap<String, Object>> list;
        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
        } else {
            params.put("page", 1);
            params.put("limit", Long.MAX_VALUE);
        }
        list = this.queryPage(params).getList();
        List<SampleExportVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, SampleExportVO.class);
//            // 封装明细
//            if(CollectionUtils.isNotEmpty(resultList)){
//                for(SampleExportVO sampleEntity : resultList){
//                    List<SampleItemEntity>  sampleItemEntityList = sampleItemService.queryLineList(sampleEntity.getId());
//                    sampleEntity.setSampleItemExportVOList(BeanConverter.convertList( sampleItemEntityList, SampleItemExportVO.class));
//                }
//            }
        }
        return resultList;
    }


    /**
     * 供应商回复送样通知单（拒绝、答交、确认送样）时要发系统消息和邮件给采购员
     * @param sampleItem
     * @param operationType
     */
    private void sendOperationTypeMsgToPur(SampleItemEntity sampleItem,String operationType){
        try {
            SampleEntity sample = sampleService.getInfo(sampleItem.getSampleId());
            if (ObjectUtil.isNotEmpty(sampleItem.getPurName())){
                List<SysUserVO> sysUserVOS = sysClient.queryUserByIds(Collections.singletonList(sampleItem.getPurId()));
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId", sampleItem.getTenantId());
                mailJson.put("userId", sampleItem.getPurId());
                mailJson.put("content",String.format("%s%s了物料编码为%s的送样通知单%s",sample.getVendorName(),operationType,sampleItem.getGoodsErpCode(),sample.getSampleNo()));
                mailJson.put("menuTitle","送样通知单详情");//菜单标题
                mailJson.put("url","base/sample/tenant?id="+sample.getId());//菜单地址
                sysClient.sendMail(mailJson);
                // 采购员邮箱地址不为空
                if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())){
                    EmailMessageVo emailMessageVo = new EmailMessageVo();
                    emailMessageVo.setTenantId(sampleItem.getTenantId());
                    emailMessageVo.setTitle("您有一封新的邮件信息");
                    String content = sampleItem.getPurName()+",您好：\n" +
                            "<br>\n" +
                            "<br>供应商:"+sample.getVendorName()+operationType+"了物料编码("+sampleItem.getGoodsErpCode()+")的送样通知单("+sample.getSampleNo()+")，请知悉！ \n" +
                            "<br>\n" +
                            "<br>请您及时查阅、处理！ 如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                            "<br>\n";
                    emailMessageVo.setContent(content);
                    emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                    emailService.customSendEmail(emailMessageVo);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        SampleEntity sampleEntity = this.getById(id);
        //if(sampleEntity.getOrderState().equals(Sample_OrderStateEnum.AUDITED.getValue())){
        //     throw new RRException(String.format("[%s] 当前订单非未审核状态,禁止修改",sampleEntity.getProbNo()));
        //}
    }

    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkSample(SampleEntity sampleEntity) {
        if (sampleEntity == null) {
            throw new RRException("当前送样单数据不存在");
        }
    }

    /**
     * 删除状态校验
     *
     * @param
     */
    private void deleteCheck(Long id) {
        SampleEntity sampleEntity = this.getById(id);
        //if(!sampleEntity.getOrderState().equals(Sample_OrderStateEnum.WAITCHECK.getValue())){
        //    throw new RRException(String.format("[%s] 当前订单非未审核状态,禁止删除",SampleEntity.getProbNo()));
        //}
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(SampleEntity record, Class<?> cls) {

        //设置明细的主表ID
        ListUtils.setPropertyValue(record.getSampleItemEntityList(), "sample_id", record.getId());

        ValidatorUtils.validateEntity(record, cls);

        if (CollectionUtils.isEmpty(record.getSampleItemEntityList())) {
            throw new RRException("送样单明细数据不能为空");
        }
        for (SampleItemEntity sampleItemEntity : record.getSampleItemEntityList()) {
            int index = record.getSampleItemEntityList().indexOf(sampleItemEntity) + 1;
            try {
                ValidatorUtils.validateEntity(sampleItemEntity, cls);
            } catch (RRException e) {
                throw new RRException(String.format("第%s行 预订单明细校验失败<br/>" + e.getMsg(), index));
            }
        }
    }

    /**
     * 获取查询条件
     *
     * @param
     */
    private QueryWrapper<SampleEntity> getQueryWrapper(Map<String, Object> params) {
        QueryWrapper<SampleEntity> queryWrapper = new QueryWrapper<>();
        if (!StrUtil.isEmptyIfStr(params.get("propKey"))) {
        }
        if (!StrUtil.isEmptyIfStr(params.get("tenantId"))) {
            queryWrapper.eq("tenant_id", commonService.getTenantId());
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendorId"))) {
            queryWrapper.eq("vendor_id", commonService.getTenantId());
        }
        if (!StrUtil.isEmptyIfStr(params.get("sampleDate"))) {
            queryWrapper.eq("sample_date", params.get("sampleDate"));
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendor"))) {
            queryWrapper.lambda().and(wrapper -> wrapper
                    .eq(SampleEntity::getVendorCode, params.get("vendor").toString())
                    .or()
                    .eq(SampleEntity::getVendorErpCode, params.get("vendor").toString())
                    .or()
                    .eq(SampleEntity::getVendorName, params.get("vendor").toString())
            );
        }
        if (!StrUtil.isEmptyIfStr(params.get("sampleNo"))) {
            queryWrapper.like("sample_no", params.get("sampleNo"));
        }
        queryWrapper.orderByDesc("id");
        return queryWrapper;
    }


    public String getSerialNo(String tableNames, String outBillNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableNames", tableNames);
        map.put("outBillNo", outBillNo);
        getBaseMapper().getSerialNo(map);
        return map.get("outBillNo") + "";
    }

    //转为hashmap
    private List<HashMap<String, Object>> handleJSONArray(JSONArray jsonArray) {
        List list = new ArrayList();
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            HashMap map = new HashMap<String, Object>();
            for (Map.Entry entry : jsonObject.entrySet()) {
                if (entry.getValue() instanceof JSONArray) {
                    map.put((String) entry.getKey(), handleJSONArray((JSONArray) entry.getValue()));
                } else {
                    map.put((String) entry.getKey(), entry.getValue());
                }
            }
            list.add(map);
        }
        return list;
    }

    /**
     * 新增保存校验
     *
     * @param sampleEntity
     */
    private void saveCheck(SampleEntity sampleEntity) {
    }


    /**
     * 通用PLM同步方法
     *
     * @param sample        送样单信息
     * @param sampleItems   需要同步的物料明细行列表
     * @param orderStatus   订单状态
     * @param receiptStatus 收货状态
     * @param receiptDate   收货日期
     * @param shareStatus   分配状态
     * @param operationDesc 操作描述
     */
    private void syncSampleToPlm(SampleEntity sample, List<SampleItemEntity> sampleItems,
                                String orderStatus, String receiptStatus, String receiptDate,
                                String shareStatus, String operationDesc) {
        SampleEntity sampleEntity = this.getInfo(sample.getId());
        Assert.isNull(sampleEntity, "单据不存在");
        if (CollectionUtil.isEmpty(sampleEntity.getSampleItemEntityList())) {
            throw new RRException("送样单明细不能为空");
        }
        List<PlmSampleVO> plmSampleVOList = new ArrayList<>();
        for (SampleItemEntity item : sampleItems) {
            // 获取对应的送样需求单明细
            List<SampleDemandItemEntity> demandItemList = sampleDemandItemService.list(
                new LambdaQueryWrapper<SampleDemandItemEntity>().eq(SampleDemandItemEntity::getId, item.getSourceItemId())
            );
            if (CollectionUtil.isEmpty(demandItemList)) {
                throw new RRException(String.format("送样单[%s]的明细[%s]的关联需求单明细不存在", sample.getSampleNo(), item.getId()));
            }
            Assert.isNull(demandItemList.get(0).getSourceItemId(), "送样单明细关联的送样需求单来源明细ID为空");
            PlmSampleVO plmSampleVO = new PlmSampleVO();
            plmSampleVO.setKeyid(item.getId().toString());
            plmSampleVO.setSourceitemid(demandItemList.get(0).getSourceItemId().toString());
//            plmSampleVO.setShtinsid(demandItemList.get(0).getSourceItemId().toString());
            plmSampleVO.setSharestatus(shareStatus);
            plmSampleVO.setSupplier_abbr(sample.getVendorName());
            plmSampleVO.setSupplier_code(sample.getVendorCode());
            plmSampleVO.setOrder_no(sample.getSourceNo());
            plmSampleVO.setOrder_status(orderStatus);
            plmSampleVO.setOrder_quantity(ObjectUtil.isEmpty(item.getDemandQty()) ? "" : item.getDemandQty().toString());
            plmSampleVO.setDelivered_quantity(ObjectUtil.isEmpty(item.getGoodsNum()) ? "" : item.getGoodsNum().toString());
            plmSampleVO.setDelivery_date(ObjectUtil.isEmpty(sample.getSampleDate()) ? "" : DateUtil.format(sample.getSampleDate(), "yyyy-MM-dd HH:mm:ss"));
            plmSampleVO.setReceipt_status(receiptStatus);
            plmSampleVO.setReceipt_date(receiptDate);
            plmSampleVO.setReceiver(commonService.getUserName());
            plmSampleVO.setStatus(orderStatus);
            plmSampleVO.setUrl(item.getVendorFilePath());
            plmSampleVO.setConfirm_date(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            plmSampleVO.setConfirmer(commonService.getUserName());
            plmSampleVO.setReply_quantity(ObjectUtil.isEmpty(item.getReplyQuantity()) ? "" : item.getReplyQuantity().toString());
            plmSampleVO.setReply_delivery_date(ObjectUtil.isEmpty(item.getReplyDeliveryDate()) ? "" : DateUtil.format(item.getReplyDeliveryDate(), "yyyy-MM-dd HH:mm:ss"));
            plmSampleVOList.add(plmSampleVO);
        }
        if (!plmSampleVOList.isEmpty()) {
            JSONObject jsonData = new JSONObject();
            jsonData.put("DATAINFOS", plmSampleVOList);
            logger.info("SRM推送{}至PLM接口传参信息={}", operationDesc, jsonData.toJSONString());
            plmApiService.sendPlmV2("/api/intf/service/gyd_cpcsrm_supplyAndOrder", jsonData.toJSONString(), operationDesc + "同步至PLM", sample.getTenantId());
        }
    }
}
