<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dian.modules.base.dao.SampleDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dian.modules.base.entity.SampleEntity" id="sampleMap">
            <result property="tenantPId" column="tenant_p_id"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="id" column="id"/>
            <result property="vendorId" column="vendor_id"/>
            <result property="vendorCode" column="vendor_code"/>
            <result property="vendorName" column="vendor_name"/>
            <result property="sampleNo" column="sample_no"/>
            <result property="sampleDate" column="sample_date"/>
            <result property="sampleStat" column="sample_stat"/>
            <result property="remark" column="remark"/>
            <result property="createId" column="create_id"/>
            <result property="creater" column="creater"/>
            <result property="createDate" column="create_date"/>
            <result property="modifiId" column="modifi_id"/>
            <result property="modifier" column="modifier"/>
            <result property="modifyDate" column="modify_date"/>
    </resultMap>

        <select id="getSerialNo" parameterType="java.util.HashMap" statementType="CALLABLE">
                call getSerialNo(
                #{params.tableNames,mode=IN,jdbcType=VARCHAR},
                #{params.outBillNo,mode=OUT,jdbcType=VARCHAR}
                )
        </select>

    <!-- 联合主从表查询列表数据 -->
    <select id="querySampleList" resultType="java.util.HashMap">
        SELECT
          bs.id,/*主表id*/
          bs.tenant_id,/*组织id*/
          bs.vendor_id,/*供应商id*/
          bs.vendor_code,/*供应商编码*/
          bs.vendor_erp_code,/*供应商erp编码*/
          bs.vendor_name,/*供应商名称*/
          bs.dept_id,/*机构id*/
          bs.dept_code,/*机构编码*/
          bs.dept_name,/*机构名称*/
          bs.sample_no,/*送样单号*/
          bs.sample_date,/*要求送样日期*/
          bs.sample_stat,/*主表状态*/
          bs.source_id,
          bs.source_no,
          bs.source_type,
          bs.demand_class_type,
          bs.is_need_up_file,
          bs.is_valid,
          bs.delete_flag,
          bs.return_remark,
          bs.is_return,
          bs.return_date,
          bs.remark,/*备注*/
          bs.create_id,/*创建人id*/
          bs.creater,/*创建人*/
          bs.create_date,/*创建时间*/
          bs.modifi_id,/*修改人id*/
          bs.modifier,/*修改人*/
          bs.modify_date,/*修改时间*/
          bs.tenant_name,/*客户名称*/
          bs.applicant, -- 申请人
          bs.apply_dept_name, -- 申请部门
          bs.apply_date, -- 申请日期
          bsi.id item_id,/*明细表id*/
          bsi.goods_id,/*物料id*/
          bsi.goods_code,/*物料编码*/
          bsi.goods_erp_code,/*物料erp编码*/
          bsi.goods_name,/*物料名称*/
          bsi.goods_model,/*物料型号*/
          bsi.demand_date,/*送样需求日期*/
          bsi.demand_qty,/*需求数量*/
          bsi.reply_quantity,/*回复数量*/
          bsi.reply_delivery_date,/*回复交期*/
          bsi.reply_state,/*回复状态*/
          bsi.pur_name,/*采购员*/
          bsi.goods_num,/*送样数量*/
          bsi.item_stat,/*明细行状态*/
          bsi.case_stat,/*结案状态*/
          bsi.case_date,/*送样时间*/
          bsi.vendor_remark,/*供应商说明*/
          bsi.remark item_remark,/*品质判断结果*/
          (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) if_inspection
        FROM
          base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                and bs.tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                and bs.vendor_id = #{params.vendorId}
                and bs.sample_stat > 1
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                and (
                bs.vendor_code like CONCAT(#{params.vendor},'%') or
                bs.vendor_name like CONCAT(#{params.vendor},'%') or
                bs.vendor_erp_code like CONCAT(#{params.vendor},'%')
                )
            </if>
            <if test="params.goods != null and params.goods != ''">
                and (
                bsi.goods_code like CONCAT(#{params.goods},'%') or
                bsi.goods_erp_code like CONCAT(#{params.goods},'%') or
                bsi.goods_name like CONCAT(#{params.goods},'%') or
                bsi.goods_model like CONCAT(#{params.goods},'%')
                )
            </if>
            <if test="params.dept != null and params.dept != ''">
                and (
                bs.dept_code like CONCAT(#{params.dept},'%') or
                bs.dept_name like CONCAT(#{params.dept},'%')
                )
            </if>
            <!-- 采购员名称 -->
            <if test="params.pur != null and params.pur != ''">
                and (
                bsi.pur_name like CONCAT(#{params.pur},'%')
                )
            </if>

            <if test="params.sampleNo != null and params.sampleNo != ''">
                and bs.sample_no like CONCAT('%',#{params.sampleNo})
            </if>
            <if test="params.sourceNo != null and params.sourceNo != ''">
                and bs.source_no like CONCAT('%',#{params.sourceNo},'%')
            </if>
            <if test="params.demandClassType != null and params.demandClassType != ''">
                and bs.demand_class_type = #{params.demandClassType}
            </if>
            <if test="params.sampleStat != null and params.sampleStat != ''">
                and bsi.case_stat = #{params.sampleStat}
            </if>
            <if test="params.itemStat != null and params.itemStat != ''">
                and bsi.item_stat = #{params.itemStat}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                <![CDATA[ and bs.sample_date >= STR_TO_DATE(#{params.startDate},'%Y-%m-%d') ]]>
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                <![CDATA[ and bs.sample_date <= STR_TO_DATE(#{params.endDate},'%Y-%m-%d') ]]>
            </if>
            <!-- 需求日期查询 -->
            <if test="params.demandDateStart != null and params.demandDateStart != ''">
                <![CDATA[ and bsi.demand_date >= STR_TO_DATE(#{params.demandDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="params.demandDateEnd != null and params.demandDateEnd != ''">
                <![CDATA[ and bsi.demand_date <= STR_TO_DATE(#{params.demandDateEnd},'%Y-%m-%d') ]]>
            </if>
            <!-- 回复日期查询 -->
            <if test="params.replyDateStart != null and params.replyDateStart != ''">
                <![CDATA[ and bsi.reply_delivery_date >= STR_TO_DATE(#{params.replyDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="params.replyDateEnd != null and params.replyDateEnd != ''">
                <![CDATA[ and bsi.reply_delivery_date <= STR_TO_DATE(#{params.replyDateEnd},'%Y-%m-%d') ]]>
            </if>
        </where>
        order by bs.id desc
    </select>

    <select id="querySampleListNoPage" resultType="java.util.HashMap">
        SELECT
        bs.id,/*主表id*/
        bs.tenant_id,/*组织id*/
        bs.vendor_id,/*供应商id*/
        bs.vendor_code,/*供应商编码*/
        bs.vendor_erp_code,/*供应商erp编码*/
        bs.vendor_name,/*供应商名称*/
        bs.sample_no,/*送样单号*/
        bs.sample_date,/*要求送样日期*/
        bs.sample_stat,/*主表状态*/
        bs.remark,/*备注*/
        bs.create_id,/*创建人id*/
        bs.creater,/*创建人*/
        bs.create_date,/*创建时间*/
        bs.modifi_id,/*修改人id*/
        bs.modifier,/*修改人*/
        bs.modify_date,/*修改时间*/
        bs.tenant_name,/*客户名称*/
        bsi.id item_id,/*明细表id*/
        bsi.goods_id,/*物料id*/
        bsi.goods_code,/*物料编码*/
        bsi.goods_erp_code,/*物料erp编码*/
        bsi.goods_name,/*物料名称*/
        bsi.goods_model,/*物料型号*/
        bsi.goods_num,/*送样数量*/
        bsi.item_stat,/*明细行状态*/
        bsi.vendor_remark,/*供应商说明*/
        bsi.remark item_remark,/*品质判断结果*/
        (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) ifInspection
        FROM
        base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                and bs.tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorOfTenant != null and params.vendorOfTenant != ''">
                and bs.vendor_id = #{params.vendorOfTenant}
            </if>

            <if test="params.vendorId != null and params.vendorId != ''">
                and bs.vendor_id = #{params.vendorId}
                and bs.sample_stat > 1
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                and (
                bs.vendor_code like CONCAT('%',#{params.vendor}) or
                bs.vendor_name like CONCAT('%',#{params.vendor}) or
                bs.vendor_erp_code like CONCAT('%',#{params.vendor})
                )
            </if>
            <if test="params.goods != null and params.goods != ''">
                and (
                bsi.goods_code like CONCAT('%',#{params.goods}) or
                bsi.goods_erp_code like CONCAT('%',#{params.goods}) or
                bsi.goods_name like CONCAT('%',#{params.goods}) or
                bsi.goods_model like CONCAT('%',#{params.goods})
                )
            </if>
            <if test="params.sampleNo != null and params.sampleNo != ''">
                and bs.sample_no like CONCAT('%',#{params.sampleNo})
            </if>
            <!-- 需求日期查询 -->
            <if test="params.demandDateStart != null and params.demandDateStart != ''">
                <![CDATA[ and bsi.demand_date >= STR_TO_DATE(#{params.demandDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="params.demandDateEnd != null and params.demandDateEnd != ''">
                <![CDATA[ and bsi.demand_date <= STR_TO_DATE(#{params.demandDateEnd},'%Y-%m-%d') ]]>
            </if>
            <!-- 回复日期查询 -->
            <if test="params.replyDateStart != null and params.replyDateStart != ''">
                <![CDATA[ and bsi.reply_delivery_date >= STR_TO_DATE(#{params.replyDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="params.replyDateEnd != null and params.replyDateEnd != ''">
                <![CDATA[ and bsi.reply_delivery_date <= STR_TO_DATE(#{params.replyDateEnd},'%Y-%m-%d') ]]>
            </if>
        </where>
        order by bs.id desc
    </select>

    <!--  获取送样单主表数量  -->
    <select id="getSampleCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*)
        from base_sample bs
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                and bs.tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                and bs.vendor_id = #{params.vendorId}
            </if>
            <if test="params.whereType != null and params.whereType != ''">
                <choose>
                    <when test="params.whereType == 1 or params.whereType == '1' ">
                        and bs.sample_stat = 1
                    </when>
                    <when test="params.whereType == 2 or params.whereType == '2' ">
                        and bs.sample_stat = 2
                    </when>
                    <when test="params.whereType == 3 or params.whereType == '4' ">
                        and bs.sample_stat = 3
                    </when>
                    <when test="params.whereType == 4 or params.whereType == '4' ">
                        and bs.sample_stat = 4
                    </when>
                    <when test="params.whereType == 5 or params.whereType == '5' ">
                        and bs.sample_stat = 5
                    </when>
                    <when test="params.whereType == 6 or params.whereType == '6' ">
                        and bs.sample_stat = 6
                    </when>
                    <when test="params.whereType == 7 or params.whereType == '7' ">
                        and bs.sample_stat = 7
                    </when>
                    <when test="params.whereType == 10 or params.whereType == '10' ">
                        and bs.sample_stat = 10
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <!--  获取送样单明细表数量  -->
    <select id="getSampleItemCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*)
        from base_sample bs,base_sample_item bsi
        <where>
            bs.id = bsi.sample_id
            <if test="params.tenantId != null and params.tenantId != ''">
                and bs.tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                and bs.vendor_id = #{params.vendorId}
            </if>
            <if test="params.whereType != null and params.whereType != ''">
                <choose>
                    <when test="params.whereType == 0 or params.whereType == '0'">
                        and bsi.item_stat = 0
                    </when>
                    <when test="params.whereType == 1 or params.whereType == '1'">
                        and bsi.item_stat = 1
                    </when>
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        and bsi.item_stat = 2
                    </when>
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and bsi.item_stat = 3
                    </when>
                </choose>
            </if>
        </where>
    </select>
    <select id="getAbnormalCount" resultType="java.util.HashMap" parameterType="java.util.Map">
        SELECT
            abnormal_type as abnormalType,
            COUNT(*) AS totalCount,
            SUM(CASE WHEN is_confirm = 1 THEN 1 ELSE 0 END) AS activeCount
        FROM
            qua_abnormal
        WHERE
            abnormal_type IN (1, 2, 3, 4,5)
            AND vendor_id = #{params.vendorId}
            AND is_vendor_show = 1
        GROUP BY
            abnormal_type;
    </select>
</mapper>
