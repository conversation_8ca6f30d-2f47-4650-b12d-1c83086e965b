/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.base.BaseClient;
import com.dian.client.im.ImClient;
import com.dian.client.sys.SysClient;
import com.dian.common.dto.UserDto;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.utils.*;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.config.AppConfig;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.common.K3CloudResultVo;
import com.dian.k3cloud.vo.purchaseOrder.ErpPurOrderReq;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.modules.base.query.GoodsReqVo;
import com.dian.modules.base.service.ConfigService;
import com.dian.modules.base.vo.GoodsVO;
import com.dian.modules.base.vo.VendorVO;
import com.dian.modules.enums.common.JinDieOrderTypeEnum;
import com.dian.modules.enums.common.OperBillEnum;
import com.dian.modules.enums.common.OperTypeEnum;
import com.dian.modules.enums.order.*;
import com.dian.modules.im.request.PriceTranRequest;
import com.dian.modules.im.vo.PriceTranVO;
import com.dian.modules.k3cloud.service.PurchaseOrderJoggleService;
import com.dian.modules.order.dao.SaleDao;
import com.dian.modules.order.entity.PurEntity;
import com.dian.modules.order.entity.PurItemEntity;
import com.dian.modules.order.service.PurItemService;
import com.dian.modules.order.service.PurService;
import com.dian.modules.order.service.SaleItemService;
import com.dian.modules.order.service.SaleService;
import com.dian.modules.order.vo.*;
import com.dian.modules.sys.vo.SysUserVO;
import com.dian.sap.zif042.request.Srm010InputMessage;
import com.dian.sap.zif042.response.Srm010outputRoot;
import com.dian.util.SapDateUtils;
import com.dian.vo.EmailMessageVo;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 销售订单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-24 09:40:06
 */
@Service("saleService")
public class SaleServiceImpl extends ServiceImpl<SaleDao, PurEntity> implements SaleService {

    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Autowired
    public CommonService commonService;

    @Autowired
    private BaseClient baseClient;

    @Autowired
    private SysClient sysClient;

    @Autowired
    private ConfigService configService;


    @Autowired
    public SaleItemService saleItemService;

    @Autowired
    public PurService purService;


    @Autowired
    public PurItemService purItemService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ImClient imClient;

    @Autowired
    private PurchaseOrderJoggleService purchaseOrderJoggleService;

    /**
     * 销售订单分页
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());

        //新单
        if(params.get("whereType") != null){
            //新单
            String whereType = params.get("whereType") + "";
            if(whereType.equals("77")){
                params.put("itemStat",1);
            }
            //答交异常
            if(whereType.equals("66")){
                params.put("itemStat",2);
            }
        }

        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().getSaleList(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     *  销售订单新增
     * @param purEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean saveInfo(PurEntity purEntity) {

        //设置编码，等基础默然初始化数据设置
        //PurEntity.setOrderDate(new Date());


        //数据完整性校验
        this.paramsCheck(purEntity,AddGroup.class);

        //保存主表
        this.save(purEntity);

        //保存从表
        if (CollectionUtils.isNotEmpty(purEntity.getPurItemEntityList())) {
            List<PurItemEntity> detailList = purEntity.getPurItemEntityList();
            for (PurItemEntity detail : detailList) {
                detail.setPurId(purEntity.getId());
            }
            saleItemService.saveBatch(purEntity.getPurItemEntityList());
        }
        return true;
    }

    /**
     *销售订单更新
     * @param purEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateInfo(PurEntity purEntity) {

        //主表数据完整性校验
        this.paramsCheck(purEntity, UpdateGroup.class);

        //更新主表
        this.updateById(purEntity);

        //更新从表
        saleItemService.updateInfo(purEntity);


        return true;
    }

    /**
     *销售订单删除
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteInfo(Long id) {

        //更新从表
        saleItemService.deleteInfo(id);

        //更新主表
        this.remove(new QueryWrapper<PurEntity>().eq("id",id));

        return true;
    }

    /**
     * 销售订单详情
     * @param id
     * @return
     */
    @Override
    public PurEntity getInfo(Long id) {
        PurEntity purEntity = getById(id);
        JSONObject companyinfo = baseClient.getCompanyInfo().getJSONObject("data");
        if (companyinfo != null) {
            purEntity.setCompanyName(companyinfo.getString("companyContact"));
            purEntity.setCompanyTel(companyinfo.getString("companyTel"));
        }

        List<PurItemEntity> lineList=saleItemService.queryLineList(id);
        BigDecimal goodsSum = new BigDecimal(0);
        String modelRate = "";
        for (PurItemEntity pur : lineList) {
            if (pur.getGstAmount()!=null) {
                goodsSum = goodsSum.add(pur.getGstAmount());
            }
            // 模板税率不为空时
            if (pur.getRateVal() != null && modelRate.equals("")) {
                modelRate = pur.getRateVal().intValue()+"%"; // intValue转换为int
            }
            String incomingInterface = configService.getValueByKeyAndTenantId("isSapSend", purEntity.getTenantId().toString());
            if (!StrUtil.isEmptyIfStr(incomingInterface) && !StrUtil.isEmptyIfStr(pur.getAddress())) {
                purEntity.setAddress(pur.getAddress());
            }
            // 如果 incomingInterface 不为空，则属于创维
            if (pur.getGoodsErpCode() != null && StrUtil.isEmptyIfStr(incomingInterface)) {
                pur.setModelDatas( pur.getGoodsErpCode()
                        +"<br>" + pur.getGoodsName()+ "<br>"
                        +"采购类型:" + BsartTypeEnum.getDesc(purEntity.getBsart()) // 字典匹配
                        +" 库位: ");
            } else {
                pur.setModelDatas( pur.getGoodsErpCode() +"<br>" + pur.getGoodsName()+ "<br>" +"采购类型:" + BsartTypeEnum.getDesc(purEntity.getBsart()) +" 工厂:" +pur.getWerks()+" 库位: ");
            }
        }
        /**
         * 供应商前端获取不到系统参数，由后台获取
         * 是否允许修改订单答交 系统参数(融方)
         * 0 - 不允许
         * 1 - 允许
         */
        String isReplyOrder = configService.getValueByKeyAndTenantId("isReplyOrder", purEntity.getTenantId().toString());
        if (!StrUtil.isEmptyIfStr(isReplyOrder)) {
            purEntity.setIsReplyOrder(isReplyOrder);
        }
        /**
         * 供应商前端获取不到系统参数，由后台获取
         * 是否启用条码功能
         * 0 - 否
         * 1 - 是
         */
        String isEnableBarCode = configService.getValueByKeyAndTenantId("isEnableBarCode", purEntity.getTenantId().toString());
        if (!StrUtil.isEmptyIfStr(isEnableBarCode)) {
            purEntity.setIsEnableBarCode(isEnableBarCode);
        }
        purEntity.setDeptName(DepCodeEnum.getDesc(purEntity.getDeptName())); // 采购组
        purEntity.setGoodsSum(goodsSum); // 总金额
        purEntity.setModelRate(modelRate); // 模板税率
        /**
         * 系统参数
         * 1 - 整单答交
         * 2 - 明细答交
         */
        purEntity.setOrderReply(sysClient.getValueByKeyAndTenantId("orderReply", purEntity.getTenantId()));

        purEntity.setPurItemEntityList(lineList);
        JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.INFO.getValue(), purEntity.getId(), "查看销售订单" + purEntity.getPurNo(), purEntity.getPurNo());
        int i= Integer.valueOf(jsonObject.get("code")+"");
        if(i==500){
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        return purEntity;
    }

    @Override
//    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public JSONObject saveOperMsg(int operBill,int operType,Long soureHeadId,String remark,String attr1 ){
        /**
         * 保存业务操作日志
         *   oper_bill int
         生成类型 1-询价单；2-报价单;3-采购订单;4-销售订单;5-送货单;6-收货单;7-退货单;8-对账单;9-发票单
         oper_type 操作类型 1-创建；2-发布;3-接受;4-提交;具体以单据类型确定
         soure_head_id 来源单据主id
         remark :操作说明 查看采购订单PO20210323000003
         */
        JSONObject jsonObject1 = null;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantId",commonService.getTenantId());
//        jsonObject.put("tenantName",commonService.get());
            jsonObject.put("operBill",operBill);
            jsonObject.put("operType",operType);
            jsonObject.put("soureHeadId",soureHeadId);
            jsonObject.put("remark",remark);
            jsonObject.put("attr1",attr1);
            jsonObject.put("attr2",commonService.getUserName());
            jsonObject1 = baseClient.saveOperMsg(jsonObject);
            int i = Integer.valueOf(jsonObject1.get("code")+"");
        } catch (NumberFormatException e) {
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put("code",500);
            jsonObject2.put("msg","保存业务操作信息失败"+e.getMessage());
            return jsonObject2;
        }
        return jsonObject1;
    }


//            //供方拒收订单以邮箱消息和站内业务消息的方式通知采购方
//            JSONObject mailJson = new JSONObject();
//            mailJson.put("tenantId",purEntity.getTenantId());
//            mailJson.put("userId",0L);
//            mailJson.put("content",purEntity.getVendorName()+"拒收采购订单"+purEntity.getPurNo()+"[拒收原因:"+purEntity.getOrderRemark()+"]");
//            mailJson.put("menuTitle","采购订单详情");//菜单标题
//            mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
//            sysClient.sendMail(mailJson);
//            replyOrder(purEntity);
//            sendConfirmOrRefuseEmail(purEntity);

    /**
     * 销售订单审核
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean checkInfo(Long id) {
        PurEntity purEntity =this.getById(id);
//        checkCheck(purEntity);
        //PurEntity.setOrderState(OrderHead_OrderStateEnum.AUDITED.getValue());
        //PurEntity.setOrderDate(new Date());
        ///PurEntity.setCheckUserId(commonService.getUserId());
        //PurEntity.setCheckUserName(commonService.getUserName());
        return this.updateById(purEntity);
    }

    /**
     * 确认或拒收订单
     * @param purEntity
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void replyOrder(PurEntity purEntity) {
        //答交异常不处理,确认需要每一行订单明细的答交日期和订单货期一致或采购方确认答交日期没问题
        //供方拒收需要填写一个总的意见说明(长度不能超过100)，每一行明细都可以填写意见说明
        if(PurStatEnum.WAITREPLY.getValue().equals(purEntity.getStat())){ // 待答交
            //更新订单明细为待答交
            for(PurItemEntity purItemEntity:purEntity.getPurItemEntityList()){
                purItemEntity.setItemStat(PurlineStatEnum.WAITREPLY.getValue());
            }
            saleItemService.updateBatchById(purEntity.getPurItemEntityList());
            return;
        }else if(PurStatEnum.REPLYEXCEPTION.getValue().equals(purEntity.getStat())) {//答交异常
            //更新订单明细为答交异常
            saleItemService.updateBatchById(purEntity.getPurItemEntityList());
            return;
        }
        Date date = new Date();
        purEntity.setConfirmDate(date);//确认时间为当前时间

        if(PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())){//确认接受
            purEntity.setOrderConfirmSynchronize(1);//同步成功
            List<PurItemEntity> purItemEntityList = purEntity.getPurItemEntityList();
            for (PurItemEntity purItem:purItemEntityList){
                if (StrUtil.isEmptyIfStr(purItem.getReplyDate())){
                    purItem.setReplyDate(purItem.getDeliveryDate());
                }
            }
            // 保存明细，确定明细是否全部为全部答交
            purItemService.updateBatchById(purItemEntityList);
            List<PurItemEntity> purItemEntities = saleItemService.queryLineList(purEntity.getId());
            List<PurItemEntity> purItemEntitiesStat = saleItemService.queryLineStatList(purEntity.getId(), PurlineStatEnum.CONFIRM.getValue());
            purEntity.setStat(PurStatEnum.WAITREPLY.getValue()); // 待答交
            purEntity.setReplyStat(PurReplyStatEnum.w.getValue()); // 部分答交
            if (purItemEntities.size() == purItemEntitiesStat.size()) {
                purEntity.setStat(PurStatEnum.CONFIRM.getValue());
                purEntity.setReplyStat(PurReplyStatEnum.e.getValue()); // 全部答交
            } else {
                purEntity.setStat(PurStatEnum.CONFIRM.getValue());
                purEntity.setReplyStat(PurReplyStatEnum.e.getValue()); // 全部答交
            }
            this.updateById(purEntity);
            // 如果全部答交，则发送信息通知供应商创建单据
            if (PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())) {
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId",purEntity.getTenantId());
                mailJson.put("userId",0L);
                mailJson.put("content", purEntity.getVendorName()+"确认采购订单"+purEntity.getPurNo());
                mailJson.put("menuTitle","采购订单详情");//菜单标题
//                mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
                mailJson.put("url","order/produce/vendor?id="+purEntity.getId());//菜单地址
                sysClient.sendMail(mailJson);
//                purService.sendConfirmOrRefuseEmail(purEntity);
            }
        }else if(PurStatEnum.REFUSE.getValue().equals(purEntity.getStat())){ // 供方拒绝
            //更新订单明细和表头状态为待作废状态
            for(PurItemEntity purItemEntity:purEntity.getPurItemEntityList()){
                purItemEntity.setItemStat(PurlineStatEnum.REFUSE.getValue());
            }
            purEntity.setOrderConfirmSynchronize(1);//同步成功
            // 保存主从表
            this.updateById(purEntity);
            saleItemService.updateBatchById(purEntity.getPurItemEntityList());
        }

    }

    /**
     * 根据退货数量及采购订单行号+采购订单号回写订单行退货数量
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateMasterOrRefundNum(Map<String, Object> params) {
        Assert.isNull(params,"更新收退数据不能为空");
        Assert.isNull(params.get("masterType"),"收退类型不能为空");

        String masterType = params.get("masterType")+"";
        JSONArray jsonArray =JSONObject.parseObject(params+"").getJSONArray("purItemList");
        for (Object arr : jsonArray) {
            JSONObject jsonObject = JSONObject.parseObject(arr + "");
            PurItemEntity purItemEntity = saleItemService.getById(jsonObject.getLong("saleItemId"));

            if(purItemEntity!=null) {
                //获取系统参数退货后更新订单应送数量,默认为0,为1更新订单和送货应送数量
                String isUpdateWaitNum = sysClient.getValueByKeyAndTenantId("isUpdateWaitNum", Long.parseLong(params.get("tenantId")+""));

                //ERP退货
                if (purItemEntity.getErpRejectNum() == null) {
                    purItemEntity.setErpRejectNum(BigDecimal.ZERO);
                }

                // ERP实收
                if (purItemEntity.getErpMasterNum() == null) {
                    purItemEntity.setErpMasterNum(BigDecimal.ZERO);
                }

                // ERP已制单量
                if (purItemEntity.getMakeNum() == null) {
                    purItemEntity.setMakeNum(BigDecimal.ZERO);
                }

                // 待送货量
                if (purItemEntity.getWaitNum() == null) {
                    purItemEntity.setWaitNum(BigDecimal.ZERO);
                }

                // 已送货量
                if (purItemEntity.getFixNum() == null) {
                    purItemEntity.setFixNum(BigDecimal.ZERO);
                }

                if ("1".equals(masterType)) {  //ERP收货
                    //增加ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().add(jsonObject.getBigDecimal("masterNum")));
                } else if ("2".equals(masterType)) {//ERP退货
                    purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().add(jsonObject.getBigDecimal("masterNum")));
                    //if("1".equals(isUpdateWaitNum)) {
                    // 增加待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(jsonObject.getBigDecimal("masterNum")));
                    // 减少订单行已送货量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(jsonObject.getBigDecimal("masterNum")));
                    //减少已制单量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(jsonObject.getBigDecimal("masterNum")));
                    //}
                }else if("5".equals(masterType)){ // ERP手工入库
                    // 增加ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().add(jsonObject.getBigDecimal("masterNum")));
                    // 减去待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().subtract(jsonObject.getBigDecimal("masterNum")));
                    // 增加已送货数量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().add(jsonObject.getBigDecimal("masterNum")));
                    //增加已制送货单数量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().add(jsonObject.getBigDecimal("masterNum")));
                }else if("6".equals(masterType)){//ERP手工退货

                    //增加去ERP实退量
                     purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().add(jsonObject.getBigDecimal("masterNum")));
                    // 增加订单行待送货量
                     purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(jsonObject.getBigDecimal("masterNum")));
                    // 减少订单行已送货量
                     purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(jsonObject.getBigDecimal("masterNum")));
                    //减少订单行已送货量
                     purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(jsonObject.getBigDecimal("masterNum")));
                }
                purItemService.updateById(purItemEntity);
            }
        }
    }



    public Srm010outputRoot srm010Input(String resource,String param,Long tenantId) {
        Srm010outputRoot result = JSON.parseObject(sysClient.send(resource, param,"SRM采购订单确认同步到SAP",tenantId), Srm010outputRoot.class);
//        try {
//            result = JSON.parseObject(sysClient.send(resource, param,"SRM采购订单确认同步到SAP",tenantId), Srm010outputRoot.class);
//            if(!"S".equals(result.getMESSAGE().getITEMS().getTYPE())){//处理失败
//                throw new RRException("SRM采购订单确认同步到SAP处理失败"+result.getMESSAGE().getITEMS().getMESSAGE());
//            }
//        }catch (Exception e){
//            throw new RRException("SRM采购订单确认同步到SAP失败");
//        }
        return result;
    }

    public Srm010InputMessage getSrm010Input(PurEntity purEntity) {
        Srm010InputMessage srm010InputMessage = new Srm010InputMessage();
        //sap采购凭证最大10位
        if(purEntity.getPurNo()!=null && purEntity.getPurNo().length()>10) {
            srm010InputMessage.setEBELN(purEntity.getPurNo().substring(0,10));
        }else{
            srm010InputMessage.setEBELN(purEntity.getPurNo());
        }
        srm010InputMessage.setBUKRS(purEntity.getTenantId()+"");
        srm010InputMessage.setLIFNR(purEntity.getVendorCode());
        srm010InputMessage.setEKGRP(purEntity.getDeptName());
        if(PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())){
            srm010InputMessage.setZFLAG("2");
        }else{
            srm010InputMessage.setZFLAG("3");
        }
        srm010InputMessage.setZRDATE(SapDateUtils.getDateStr(purEntity.getConfirmDate()));
        srm010InputMessage.setZRTIME(SapDateUtils.getTimeStr(purEntity.getConfirmDate()));
        srm010InputMessage.setZRESA(purEntity.getOrderRemark());
        return srm010InputMessage;
    }




    /**
     * 供应商确认或拒收采购订单
     * @param list 采购订单明细
     * @param type 操作类型 2-确认 3-拒收
     * @param purId 采购订单Id
     * @param orderRemark 意见说明
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateStatus(JSONArray list, int type,Long purId,String orderRemark) {
        PurEntity purEntity = this.getById(purId);
        if(StringUtils.isNotEmpty(orderRemark)){
            purEntity.setOrderRemark(orderRemark);//供应商意见说明,拒收情况下不能为空
        }else{
            purEntity.setOrderRemark("");
        }
        List<HashMap<String, Object>> hashMaps = this.handleJSONArray(list);
        boolean isAutoConfirm=true;//是否自动确认
        List<PurItemEntity> purItemList = new ArrayList<>();
        for (HashMap<String,Object> map: hashMaps) {
            Long id = Long.parseLong(map.get("id") + "");
            PurItemEntity purItemEntity = purItemService.getById(id);
            String replyDate= map.get("replyDate")+"";
            String vendorRemark = map.get("vendorRemark") + "";
            if (map.get("vendorRemark") == null || ("") .equals(map.get("vendorRemark")+"")){

            }else {
                purItemEntity.setVendorRemark(vendorRemark);
            }
            if(type==2) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                if (purItemEntity.getDeliveryDate() == null) {
                    throw new RRException("交货日期为空,答复失败");
                }
                String deliveryDate = sdf.format(purItemEntity.getDeliveryDate());
                try {
                    if (replyDate != null && !"".equals(replyDate) && !"''".equals(replyDate) && !"-".equals(replyDate) && !replyDate.equals("null")) {
                        purItemEntity.setReplyDate(sdf.parse(replyDate));
                        replyDate = sdf.format(purItemEntity.getReplyDate());
                    }
                } catch (Exception e) {
                    throw new RRException(String.format("答交日期有误,答复失败"));
                }
                if (!deliveryDate.equals(replyDate)) {//答交异常
                    isAutoConfirm = false;
                }
            }
        }
        purEntity.setPurItemEntityList(purItemList);
        // 供应商拒收订单
        if(type==3) {
            //保存操作记录
            JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.REFUSEREPLY.getValue(), purEntity.getId(), "拒收采购订单"+purEntity.getPurNo(), purEntity.getPurNo());
            int i= Integer.valueOf(jsonObject.get("code")+"");
            if(i==500){
                throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
            }
            purEntity.setStat(PurStatEnum.REFUSE.getValue());
            purEntity.setPurItemEntityList(purItemList);
            //供方拒收订单以邮箱消息和站内业务消息的方式通知采购方
            JSONObject mailJson = new JSONObject();
            mailJson.put("tenantId",purEntity.getTenantId());
            mailJson.put("userId",0L);
            mailJson.put("content",purEntity.getVendorName()+"拒收采购订单"+purEntity.getPurNo()+"[拒收原因:"+purEntity.getOrderRemark()+"]");
            mailJson.put("menuTitle","采购订单详情");//菜单标题
            mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
            replyOrder(purEntity);
            sysClient.sendMail(mailJson);
//            sendConfirmOrRefuseEmail(purEntity);
//            replyOrder(purEntity);
        }else if(type==2){
            if(isAutoConfirm){//自动确认
                //保存操作记录
                JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CONFIRM.getValue(), purEntity.getId(), "确认答交采购订单"+purEntity.getPurNo(), purEntity.getPurNo());
                int i= Integer.valueOf(jsonObject.get("code")+"");
                if(i==500){
                    throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
                }

                purEntity.setStat(PurStatEnum.CONFIRM.getValue());
                purEntity.setPurItemEntityList(purItemList);

                //供方确认订单以邮箱消息和站内业务消息的方式通知采购方
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId",purEntity.getTenantId());
                mailJson.put("userId",0L);
                mailJson.put("content",purEntity.getVendorName()+"确认采购订单"+purEntity.getPurNo());
                mailJson.put("menuTitle","采购订单详情");//菜单标题
                mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
                replyOrder(purEntity);
                sysClient.sendMail(mailJson);
//                sendConfirmOrRefuseEmail(purEntity);
            }else{//答交异常
                //保存操作记录
                JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CONFIRM.getValue(), purEntity.getId(), "答交异常采购订单"+purEntity.getPurNo(), purEntity.getPurNo());
                int i= Integer.valueOf(jsonObject.get("code")+"");
                if(i==500){
                    throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
                }

                purEntity.setStat(PurStatEnum.REPLYEXCEPTION.getValue());

                //供方答交异常订单以邮箱消息和站内业务消息的方式通知采购方
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId",purEntity.getTenantId());
                mailJson.put("userId",0L);
                mailJson.put("content",purEntity.getVendorName()+"采购订单"+purEntity.getPurNo()+"答交异常");
                mailJson.put("menuTitle","采购订单详情");//菜单标题
                mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
                sysClient.sendMail(mailJson);
//                sendConfirmOrRefuseEmail(purEntity);
            }
        }

    }

    /**
     * 供应商确认/拒收采购订单
     * @param purConfirmVO
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public String updateOrderStatus(PurConfirmVO purConfirmVO) {
        Assert.isNull(purConfirmVO, "传参不能为空");
        PurEntity purEntity = this.getById(purConfirmVO.getId());
        Assert.isNull(purEntity, "采购订单不存在");
        if (ObjectUtil.isEmpty(purConfirmVO.getOperType())){
            throw new RRException("请选择操作类型");
        }
        if (CollectionUtils.isEmpty(purConfirmVO.getPurList())){
            throw new RRException("请选择采购订单明细");
        }
        // 供应商正常答交
        if (purConfirmVO.getOperType() == 2){
            purConfirmVO.getPurList().forEach(item -> {
                PurItemEntity orderItem = purItemService.getById(item.getId());
                if (ObjectUtil.isNotEmpty(item.getReplyDate())){
                    orderItem.setReplyDate(item.getReplyDate());
                    orderItem.setVendorRemark(item.getVendorRemark());
                    orderItem.setItemStat(4);
                }
                purItemService.updateById(orderItem);
            });
            List<PurItemEntity> itemList = purItemService.list(new LambdaQueryWrapper<PurItemEntity>().eq(PurItemEntity::getPurId, purConfirmVO.getId()));
            List<PurItemEntity> waitReplyList = itemList.stream().filter(item -> item.getItemStat() == 1).collect(Collectors.toList());
            if (itemList.size() != waitReplyList.size() && !waitReplyList.isEmpty()){
                // 部分答交
                purEntity.setStat(PurStatEnum.PART_REPLY.getValue());
                purEntity.setReplyStat(PurReplyStatEnum.w.getValue());
            }
            if (waitReplyList.isEmpty()){
                // 全部答交
                purEntity.setStat(PurStatEnum.CONFIRM.getValue());
                purEntity.setReplyStat(PurReplyStatEnum.e.getValue());
                purEntity.setConfirmDate(new Date());
                purEntity.setReplyDate(new Date());
            }
            purEntity.setPurItemEntityList(itemList);
        }
        // 供应商拒收
        if (purConfirmVO.getOperType() == 3){
            if (StrUtil.isEmptyIfStr(purConfirmVO.getVendorRemark())){
                throw new RRException("请填写拒绝原因");
            }
            purEntity.setStat(PurStatEnum.REFUSE.getValue());
            purEntity.setOrderRemark(purConfirmVO.getVendorRemark());
            purEntity.setRejectionDate(new Date());
            List<PurItemEntity> itemList = purItemService.list(new LambdaQueryWrapper<PurItemEntity>().eq(PurItemEntity::getPurId, purConfirmVO.getId()));
            itemList.stream().forEach(item -> {
                item.setItemStat(3);
                item.setReplyDate(null);
                item.setVendorRemark("");
            });
            purItemService.updateBatchById(itemList);
            purEntity.setPurItemEntityList(itemList);
        }

        purService.updateById(purEntity);
        PurEntityVO purEntityVO = BeanConverter.convert(purEntity, PurEntityVO.class);
        List<PurItemLineVO> purItemLineList = BeanConverter.convertList(purEntity.getPurItemEntityList(), PurItemLineVO.class);
        purEntityVO.setPurItemEntityList(purItemLineList);
        // 调用金蝶ERP修改采购订单保存接口
        purchaseOrderJoggleService.updateOrder(purEntityVO);
        if (PurStatEnum.REFUSE.getValue().equals(purEntity.getStat())){
            // 供方拒收订单以邮箱消息和站内业务消息的方式通知采购方
            this.sendRejectionMsgToPur(purEntity);
        }
        return "";
    }

    @Override
    public Map<String, Object> countMap(Map<String, Object> params) {
        params.put("tenantId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());
        params.remove("itemStat");
        params.put("whereType",1);
        Long waitOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",3);
        Long errorOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",12);
        Long sendOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",5);
        Long quickOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",6);
        Long myOrderNum= getBaseMapper().countMap(params);
        params.put("whereType",2);
        Long overOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",4);
        Long allOrderNum = getBaseMapper().countMap(params);
        params.put("whereType",9);
        Long noFininshNum = getBaseMapper().countMap(params);
        params.put("whereType",10);
        Long fininshNum = getBaseMapper().countMap(params);
        params.put("whereType",8);
        Long overSendNum = getBaseMapper().countMap(params);

        //销售订单 -- 我关注的
        params.put("whereType",13);
        Long followNum = getBaseMapper().countMap(params);

        //销售订单 -- 急单
        params.put("whereType",14);
        Long busyNum = getBaseMapper().countMap(params);

        //已确认
        params.put("whereType",15);
        Long confirmedOrderNum = getBaseMapper().countMap(params);

        //销售订单应答  -- 新单气泡
        params.put("whereType",100);
        params.put("itemStat",1);
        Long newNum = getBaseMapper().countMap(params);

        //销售订单应答  -- 答交异常
        params.put("itemStat",2);
        Long excNum = getBaseMapper().countMap(params);

        Map<String,Object> map = new HashMap<>();
        map.put("count2",overSendNum);
        map.put("count3",noFininshNum);
        map.put("count4",fininshNum);
        map.put("count5",allOrderNum);
        map.put("overOrderNum",overOrderNum);
        map.put("waitOrderNum",waitOrderNum);
        map.put("errorOrderNum",errorOrderNum);
        map.put("count1",sendOrderNum);
        map.put("count6",quickOrderNum);
        map.put("count7",myOrderNum);
        map.put("count8",newNum);
        map.put("count9",excNum);
        //往map中添加 急单和我关注的
        map.put("count13",followNum);
        map.put("count14",busyNum);
        map.put("count15",confirmedOrderNum);
        logger.info("急单="+map.get("count14"));
        logger.info("我的关注="+map.get("count13"));
        logger.info("已确认的="+map.get("count15"));
        return map;
    }

    @Override
    public Map<String, Object> countMap3(Map<String, Object> params) {
        params.put("tenantId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());


        params.put("whereType",9);
        Long overSendNum = getBaseMapper().countMap(params);


        Map<String,Object> map = new HashMap<>();
        map.put("count",overSendNum);

        return map;
    }

    @Override
    public Map<String, Object> countMap2(Map<String, Object> params) {
        params.put("tenantId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());
//        params.remove("itemStat");

        params.put("whereType",1);
        Long overOrderNum = getBaseMapper().countMap(params);


        Map<String,Object> map = new HashMap<>();

        map.put("count",overOrderNum);

        return map;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateDeliveryType(Long id, Long deliveryType) {
        PurItemEntity purItemEntity = purItemService.getById(id);//订单明细Id
        if (purItemEntity != null){
            purItemEntity.setDeliveryType(Integer.parseInt(deliveryType+""));
            purItemService.updateById(purItemEntity);
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public JSONObject updateMakenum(Long id, BigDecimal devNum,BigDecimal planNum) {
        JSONObject jsonObject1 = new JSONObject();
        PurItemEntity purItemEntity =purItemService.getById(id);//订单明细Id
        if (purItemEntity != null){
            if(purItemEntity.getMakeNum() == null){
                purItemEntity.setMakeNum(BigDecimal.ZERO);
            }
            //销售明细更新已制送货单数量
            if(purItemEntity.getWaitNum() == null){
                purItemEntity.setWaitNum(BigDecimal.ZERO);
            }
            BigDecimal canMakeNum = purItemEntity.getOrderNum().subtract(purItemEntity.getMakeNum());
            if(devNum.compareTo(canMakeNum) == 1){
                throw new RRException("创建送货数量大于订单"+purItemEntity.getSoureNo()+"/"+purItemEntity.getSeq()+"物料编码:"+purItemEntity.getGoodsErpCode()+"的可制单数："+canMakeNum);
            }
            purItemEntity.setMakeNum(purItemEntity.getMakeNum().add(devNum));
            purItemService.updateById(purItemEntity);
        }
        jsonObject1.put("code","200");
        jsonObject1.put("msg","操作成功");
        return jsonObject1;
    }

//    private HashMap<String, Object> fromJson2Map(String jsonString) {
//        HashMap jsonMap = JSON.parseObject(jsonString, HashMap.class);
//
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        for(Iterator iter = jsonMap.keySet().iterator(); iter.hasNext();){
//            String key = (String)iter.next();
//            if(jsonMap.get(key) instanceof JSONArray){
//                JSONArray jsonArray = (JSONArray)jsonMap.get(key);
//                List list = handleJSONArray(jsonArray);
//                resultMap.put(key, list);
//            }else{
//                resultMap.put(key, jsonMap.get(key));
//            }
//        }
//        return resultMap;
//    }

    //转为hashmap
    private  List<HashMap<String, Object>> handleJSONArray(JSONArray jsonArray){
        List list = new ArrayList();
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            HashMap map = new HashMap<String, Object>();
            for (Map.Entry entry : jsonObject.entrySet()) {
                if(entry.getValue() instanceof  JSONArray){
                    map.put((String)entry.getKey(), handleJSONArray((JSONArray)entry.getValue()));
                }else{
                    map.put((String)entry.getKey(), entry.getValue());
                }
            }
            list.add(map);
        }
        return list;
    }

    /**
     *销售订单当前页or全部导出
     * @param params
     * @return  queryPage
     */
    @Override
    public List<SaleExportVO> exportList(Map<String, Object> params) {
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findSaleList(page, params);
        List<SaleExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, SaleExportVO.class);
        }
        return resultList;
    }

    /**
     * 销售订单进度列表导出
     * @param params
     * @return
     */
    @Override
    public List<SaleProgressExportVO> saleProgressExport(Map<String, Object> params) {
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());
        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "1".equals(params.get("exportType").toString())) {
            params.put("page", 1);
            params.put("limit", params.get("total"));
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findSaleProgressList(page, params);
        List<SaleProgressExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, SaleProgressExportVO.class);
        }
        return resultList;
    }


    /**
     *销售订单列表模式当前页or全部导出
     * @param params
     * @return  queryPage
     */
    @Override
    public List<SaleDetailExportVO> exporDetail(Map<String, Object> params) {
        List<PurEntity> list;
        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
            list = queryMainSaleList(params).getList();
        }else{
            params.remove("limit");
            params.remove("page");
            list = queryMainSaleList(params).getList();
        }
        List<SaleDetailExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, SaleDetailExportVO.class);

        }
        return resultList;
    }

    /**
     * 销售订单打印
     * 2021-4-26
     * meng
     * @param map
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public PurEntity updateOrderPrintState(Map<String, Object> map) {
        if (!StrUtil.isEmptyIfStr(map.get("id"))){
            Long id=Long.parseLong(map.get("id").toString().trim());
            PurEntity purEntity=this.getById(id);
            if(purEntity != null){
                purEntity.setIsPrint(1);//销售订单是否已打印;0-否;1-是
                if(purEntity.getPrintCount() == null || purEntity.getPrintCount() ==0 ){
                    purEntity.setPrintCount(1);
                }else{
                    purEntity.setPrintCount(purEntity.getPrintCount()+1);
                }
                purService.updateById(purEntity);

                //保存日志
                JSONObject saveOperMsg=new JSONObject();
                saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
                saveOperMsg.put("operType", OperTypeEnum.PRINT.getValue());//类型打印
                saveOperMsg.put("soureHeadId", purEntity.getId());
                saveOperMsg.put("remark", "销售订单已打印:"+purEntity.getPurNo());
                saveOperMsg.put("attr1", purEntity.getPurNo());
                saveOperMsg.put("attr2", commonService.getUserName());
                saveOperMsg.put("tenantId", commonService.getTenantId());
                saveOperMsg.put("vendorId", purEntity.getVendorId());
                baseClient.saveOperMsg(saveOperMsg);

                return purEntity;
            }
        }
        return null;
    }

    /**
     * 销售订单列表查询
     *  -- hzy
     * @param params
     * @return
     */
    @Override
    public PageUtils querySaleList(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());

        //答交超时时间 临时使用
//        params.put("replyMinute",24);
        params.put("replyMinute",1440);

        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findSaleList(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryMainSaleList(Map<String, Object> params) {
        UserDto userInfo = commonService.getUser();
        List<String> lookPricePermsList = userInfo.getPermsList().stream().filter(perms -> perms.equals("order:pur:lookPrice")).collect(Collectors.toList());
        Integer ifLookPricePerms = WhetherEnum.NO.getCode();
        if (CollectionUtil.isNotEmpty(lookPricePermsList)){
            ifLookPricePerms = WhetherEnum.YES.getCode();
        }
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());

        //答交超时时间 临时使用
//        params.put("replyMinute",24);
        params.put("replyMinute",1440);

        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findMainSaleList(page, params);

        BigDecimal goodsSum = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(list)){
            List<Long> goodsIds = list.stream().map(item -> Long.parseLong(item.get("goodsId").toString())).collect(Collectors.toList());
            Map<Long, GoodsVO> goodsMap = queryGoodsList(goodsIds);
            for (HashMap<String, Object> item:list) {
                if (CollectionUtil.isNotEmpty(goodsMap)){
                    GoodsVO goodsVo = goodsMap.get(Long.parseLong(item.get("goodsId").toString()));
                    item.put("warehouseCode", goodsVo.getWarehouseCode());
                    item.put("warehouseName", goodsVo.getWarehouseName());
                }
                item.put("ifLookPrice",ifLookPricePerms);
                goodsSum = goodsSum.add(ObjectUtil.isNotEmpty(item.get("gstAmount")) ? new BigDecimal(item.get("gstAmount").toString()) : BigDecimal.ZERO);
                // 订单的未制单数量 = 订单数量 - 已制单数量
                item.put("unMakeNum", new BigDecimal(item.get("orderNum").toString()).subtract(new BigDecimal(item.get("makeNum").toString())));
                item.put("deliveryStatus", "未开始送货");
                // 订单行已送数量大于0 且 订单行已送数量小于订单行数量
                if (new BigDecimal(item.get("fixNum").toString()).compareTo(BigDecimal.ZERO) > 0 && new BigDecimal(item.get("fixNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) < 0) {
                    item.put("deliveryStatus", "送货进行中");
                }
                // 订单行已送数量等于订单行数量
                if (new BigDecimal(item.get("fixNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) == 0) {
                    item.put("deliveryStatus", "已完成送货");
                }
                // 订单行已收数量等于订单行数量
                if (new BigDecimal(item.get("erpMasterNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) == 0) {
                    item.put("deliveryStatus", "已完成入库");
                }
            }
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 销售订单列表的气泡查询
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> confirmCountSaleMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
//        params.put("replyMinute",24);
        params.put("replyMinute",1440);
        try {
            resultMap = new HashMap<>();
            /**
             * i = 1 查询新单的气泡数 ；
             * 2 => 超时未交单
             * 3 => 答交异常
             * 4 => 已确认
             * 5 => 全部
             * 6 => 急单
             * 7 => 我关注的
             */
            for (int i = 1; i <= 7; i++) {
                params.put("whereType",i);
                resultMap.put("count"+i,getBaseMapper().confirmCountSaleMap(params));
            }

        } catch (Exception e) {
            return null;
        }
        return resultMap;
    }

    /**
     * 销售订单列表的气泡查询
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> mainConfirmCountSaleMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
//        try {
            resultMap = new HashMap<>();
            /**
             * i = 1 查询新单的气泡数 ；
             * 2 => 超时未交单
             * 3 => 答交异常
             * 4 => 已确认
             * 5 => 全部
             */
            for (int i = 1; i <= 5; i++) {
                params.put("whereType",i);
                resultMap.put("count"+i,getBaseMapper().mainConfirmCountSaleMap(params));
            }
//        } catch (Exception e) {
//            return null;
//        }
        return resultMap;
    }

    /**
     * 查询采购订单的信息
     * @param saleItemId
     * @return
     */
    @Override
    public List<Map<String, Object>> findOrderBySaleId(Long saleItemId) {
        List<Map<String, Object>> orderBySaleId = getBaseMapper().findOrderBySaleId(saleItemId);
        return orderBySaleId;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public JSONObject updateOrderItemMakeNum(Long id, BigDecimal devNum,Integer deStat) {
        PurItemEntity purItemEntity =purItemService.getById(id);//订单明细Id
        JSONObject jsonObject = new JSONObject();
        if (purItemEntity != null){
            if(purItemEntity.getFixNum() == null){
                purItemEntity.setFixNum(BigDecimal.ZERO);
            }
            if(purItemEntity.getWaitNum() == null){
                purItemEntity.setWaitNum(BigDecimal.ZERO);
            }
            if(purItemEntity.getMakeNum() == null){
                purItemEntity.setMakeNum(BigDecimal.ZERO);
            }
            if (purItemEntity.getMakeNum().subtract(devNum).compareTo(new BigDecimal(0)) < 1){//删除后计算出的已制单数量 小于等于 0时
                purItemEntity.setMakeNum(new BigDecimal("0.00000"));//默认设置为0
            } else {//删除后计算出的已制单数量 没有小于等于 0时
                purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(devNum));//删除后的已制单数量 = 原已制单数量 - 送货单明细的送货数量
            }

            if (deStat.equals(2)){//送货单状态为2（已发出）时，才能进行更改已送货量
                //现的已送货量 = 原已送货量 - 送货单明细的送货数量
                purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(devNum));
                if (purItemEntity.getFixNum().compareTo(BigDecimal.ZERO) < 0){
                    purItemEntity.setFixNum(BigDecimal.ZERO);
                }
                if (purItemEntity.getFixNum().compareTo(purItemEntity.getOrderNum()) > 0){
                    purItemEntity.setFixNum(purItemEntity.getOrderNum());
                }
                //删除后的待送货量 = 原待送货量 + 送货单明细的送货数量
                purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(devNum));
                if (purItemEntity.getWaitNum().compareTo(BigDecimal.ZERO) < 0){
                    purItemEntity.setWaitNum(BigDecimal.ZERO);
                }
            }

            boolean update = purItemService.updateById(purItemEntity);
            if (update == false){
                throw new RRException("操作失败");
            }
            jsonObject.put("code","200");
            jsonObject.put("msg","操作成功");
        }
        return jsonObject;
    }

    /**
     * 更新订单明细的已送货数量和待送货数量
     * @param id 订单明细id
     * @param devNum 送货数量/入库数量
     * @param deStat 状态
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateOrderItemNum(Long id, BigDecimal devNum, Integer deStat) {
        PurItemEntity purItem = purItemService.getById(id);
        if (Objects.isNull(purItem)){
            throw new RRException(String.format("订单明细不存在,id为：{%s}",id));
        }
        BigDecimal makeNum = purItem.getMakeNum() == null? BigDecimal.ZERO:purItem.getMakeNum();
        BigDecimal waitNum = purItem.getWaitNum() == null? BigDecimal.ZERO:purItem.getWaitNum();
        BigDecimal fixNum = purItem.getFixNum() == null? BigDecimal.ZERO:purItem.getFixNum();
        BigDecimal erpMasterNum = purItem.getErpMasterNum() == null? BigDecimal.ZERO:purItem.getErpMasterNum();
        BigDecimal erpRejectNum = purItem.getErpRejectNum() == null? BigDecimal.ZERO:purItem.getErpRejectNum();
        // devNum > 0
        if (devNum.compareTo(BigDecimal.ZERO) > 0){
        }
        // devNum < 0
        if (devNum.compareTo(BigDecimal.ZERO) > 0){

        }
        return true;
    }

    @Override
    public Map<String, Object> findLastOneByGoodsErpCode(Map<String, Object> params) {
        return getBaseMapper().findLastOneByGoodsErpCode(params);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateMasterQuantity(List<PurItemLineVO> list) {
        if(CollectionUtils.isNotEmpty(list)) {
            for (PurItemLineVO purItemLineVO : list) {
                this.updateMasterNum(purItemLineVO);
            }
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean updateMasterNum(PurItemLineVO purItemLineVO) {
        PurItemEntity purItemEntity = saleItemService.getById(purItemLineVO.getId());
        if(purItemEntity!=null) {
            //获取系统参数退货后更新订单应送数量,默认为0,为1更新订单和送货应送数量
            String isUpdateWaitNum = sysClient.getValueByKeyAndTenantId("isUpdateWaitNum", commonService.getTenantId());
            //ERP退货
            if (purItemEntity.getErpRejectNum() == null) {
                purItemEntity.setErpRejectNum(BigDecimal.ZERO);
            }

            // ERP实收
            if (purItemEntity.getErpMasterNum() == null) {
                purItemEntity.setErpMasterNum(BigDecimal.ZERO);
            }

            // ERP已制单量
            if (purItemEntity.getMakeNum() == null) {
                purItemEntity.setMakeNum(BigDecimal.ZERO);
            }

            // 待送货量
            if (purItemEntity.getWaitNum() == null) {
                purItemEntity.setWaitNum(BigDecimal.ZERO);
            }

            // 已送货量
            if (purItemEntity.getFixNum() == null) {
                purItemEntity.setFixNum(BigDecimal.ZERO);
            }
            if (purItemLineVO.getIsDelete() == 0) {
                if ("1".equals(purItemLineVO.getMasterType())) {  //ERP收货
                    //增加ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().add(purItemLineVO.getMasterNum()));
                } else if ("2".equals(purItemLineVO.getMasterType())) {//ERP退货
                    purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().add(purItemLineVO.getMasterNum()));
                    // 增加待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(purItemLineVO.getMasterNum()));
                    // 减少订单行已送货量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(purItemLineVO.getMasterNum()));
                    //减少已制单量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(purItemLineVO.getMasterNum()));
                } else if ("5".equals(purItemLineVO.getMasterType())) { // ERP手工入库
                    // 增加ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().add(purItemLineVO.getMasterNum()));
                    // 减去待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().subtract(purItemLineVO.getMasterNum()));
                    // 增加已送货数量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().add(purItemLineVO.getMasterNum()));
                    //增加已制送货单数量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().add(purItemLineVO.getMasterNum()));
                } else if ("6".equals(purItemLineVO.getMasterType())) {//ERP手工退货
                    //增加去ERP实退量
                    purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().add(purItemLineVO.getMasterNum()));
                    // 增加订单行待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(purItemLineVO.getMasterNum()));
                    // 减少订单行已送货量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(purItemLineVO.getMasterNum()));
                    //减少订单行已送货量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(purItemLineVO.getMasterNum()));
                }
            } else {
                if ("1".equals(purItemLineVO.getMasterType())) {  //ERP收货
                    //减少ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().subtract(purItemLineVO.getMasterNum()));
                } else if ("2".equals(purItemLineVO.getMasterType())) {//ERP退货
                    //减少退货数量
                    purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().subtract(purItemLineVO.getMasterNum()));
                    //if("1".equals(isUpdateWaitNum)) {
                    // 减少待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().subtract(purItemLineVO.getMasterNum()));
                    // 增加订单行已送货量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().add(purItemLineVO.getMasterNum()));
                    //增加已制单量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().add(purItemLineVO.getMasterNum()));
                    //}
                } else if ("5".equals(purItemLineVO.getMasterType())) { // ERP手工入库
                    // 减去ERP收货数量
                    purItemEntity.setErpMasterNum(purItemEntity.getErpMasterNum().subtract(purItemLineVO.getMasterNum()));
                    // 增加待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().add(purItemLineVO.getMasterNum()));
                    // 减去已送货数量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().subtract(purItemLineVO.getMasterNum()));
                    //减去已制送货单数量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().subtract(purItemLineVO.getMasterNum()));
                } else if ("6".equals(purItemLineVO.getMasterType())) {//ERP手工退货
                    //减去去ERP实退量
                    purItemEntity.setErpRejectNum(purItemEntity.getErpRejectNum().subtract(purItemLineVO.getMasterNum()));
                    // 减去订单行待送货量
                    purItemEntity.setWaitNum(purItemEntity.getWaitNum().subtract(purItemLineVO.getMasterNum()));
                    // 增加订单行已送货量
                    purItemEntity.setFixNum(purItemEntity.getFixNum().add(purItemLineVO.getMasterNum()));
                    //增加订单行已送货量
                    purItemEntity.setMakeNum(purItemEntity.getMakeNum().add(purItemLineVO.getMasterNum()));
                }
            }
            purItemService.updateById(purItemEntity);
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean batchAnswerAndDelivery(Long[] ids) {
            for (int i = 0; i < ids.length; i++) {
            PurEntity purEntity = this.getById(ids[i]);
            if (purEntity!=null){
            if(purEntity.getReplyStat()==3){
                throw new RRException("第"+i+"条订单号为"+purEntity.getPurNo()+"已答交,请检查数据重新答交！");
            }
            List<PurItemEntity> purItemEntities = purItemService.queryLineList(ids[i]);
                PurConfirmVO purConfirmVO =new PurConfirmVO();
                purConfirmVO.setId(purEntity.getId());
                purConfirmVO.setOperType(2);
                purConfirmVO.setStat(3);
                List<PurConfirmItemVO> list = new ArrayList<>();
                for (PurItemEntity purItemEntity : purItemEntities) {
                  if(purItemEntity.getItemStat()==0){
                      throw new RRException("第"+i+"条订单号为"+purEntity.getPurNo()+"已关闭,请检查数据重新答交！");
                  }
                    PurConfirmItemVO purConfirmItemVO= new PurConfirmItemVO();
                    purConfirmItemVO.setId(purItemEntity.getId());
                    purConfirmItemVO.setPurId(purItemEntity.getPurId());
                    purConfirmItemVO.setItemStat(1);
                    purConfirmItemVO.setReplyDate(purItemEntity.getDeliveryDate());
                    list.add(purConfirmItemVO);
                }
                purConfirmVO.setPurList(list);
               this.updateOrderStatus(purConfirmVO);
            }else {
                throw new RRException("未查询到订单,请联系管理员！");
            }
        }
        return true;
    }

    /**
     * 一键答交
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public Long oneClickProcessingOrder(Long id) {
        PurEntity purEntity = this.getById(id);
        if (PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())){
            throw new RRException("订单已答交,请勿重复操作！");
        }
        List<PurItemEntity> purItemList = saleItemService.queryLineList(id);
        for (PurItemEntity purItem:purItemList) {
            purItem.setItemStat(PurlineStatEnum.CONFIRM.getValue());
            purItem.setReplyDate(new Date());
            saleItemService.updateById(purItem);
        }
        purEntity.setReplyStat(PurReplyStatEnum.e.getValue());
        purEntity.setStat(PurStatEnum.CONFIRM.getValue());
        this.updateById(purEntity);
        return purEntity.getId();
    }

    /**
     * 批量确认
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public Long batchConfirmOrder(Long[] ids) {
        PurEntity purEntity = this.getById(ids[0]);
        for (int i = 0; i < ids.length; i++) {
            purEntity = this.getById(ids[i]);
            if (PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())){
                throw new RRException("订单已确认,请勿重复确认！");
            }
            List<PurItemEntity> purItemList = saleItemService.queryLineList(ids[i]);
            for (PurItemEntity purItem:purItemList) {
                purItem.setItemStat(PurlineStatEnum.CONFIRM.getValue()); // 已确认
                purItem.setReplyDate(new Date());
                saleItemService.updateById(purItem);
            }
            purEntity.setReplyStat(PurReplyStatEnum.e.getValue()); // 全部答交
            purEntity.setStat(PurStatEnum.CONFIRM.getValue()); // 已确认
            this.updateById(purEntity);
            this.sendConfirmMsgToPur(purEntity);
        }
        return purEntity.getId();
    }

    /**
     * 销售订单进度列表查询
     *  -- hzy
     * @param params
     * @return
     */
    @Override
    public PageUtils querySaleProgressList(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        params.put("vendorId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());

        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findSaleProgressList(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 销售订单进度列表的气泡查询
     *  -- hzy
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> ProgressCountSaleMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
        try {
            resultMap = new HashMap<>();
            /**
             * i = 1 未送货 ；
             * 2 => 超时未送货
             * 3 => 未完成
             * 4 => 已完成
             * 5 => 全部
             * 6 => 急单
             * 7 => 我关注的
             */
            for (int i = 1; i <= 7; i++) {
                params.put("whereType",i);
                resultMap.put("count"+i,getBaseMapper().progressCountSaleMap(params));
            }

        } catch (Exception e) {
            return null;
        }
        return resultMap;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public PurItemLineVO updateFixNum(Long id, BigDecimal fixNum) {
        PurItemEntity purItemEntity = saleItemService.getById(id);
        Assert.isNull(purItemEntity, "数据不存在对应采购订单明细查询不到 id:"+id);
        if(purItemEntity.getFixNum() == null){
            purItemEntity.setFixNum(BigDecimal.ZERO);
        }

        purItemEntity.setFixNum(purItemEntity.getFixNum().add(fixNum));
        purItemEntity.setWaitNum(purItemEntity.getWaitNum().subtract(fixNum));
        if (purItemEntity.getFixNum().compareTo(BigDecimal.ZERO) < 0){
            purItemEntity.setFixNum(BigDecimal.ZERO);
        }
        if (purItemEntity.getWaitNum().compareTo(BigDecimal.ZERO) < 0){
            purItemEntity.setWaitNum(BigDecimal.ZERO);
        }
        purItemService.updateById(purItemEntity);
        return BeanConverter.convert(purItemEntity, PurItemLineVO.class);
    }

    /**
     * 回写期初在途送货单对应订单明细行数量
     * @param id
     * @param devNum
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateInTransitDeliveryNum(Long id, BigDecimal devNum) {
        PurItemEntity purItem = saleItemService.getById(id);//销售明细Id
        if (Objects.nonNull(purItem)){
            // 已匹配计划数
            BigDecimal matchedPlanNum = purItem.getMatchedPlanNum() == null? BigDecimal.ZERO:purItem.getMatchedPlanNum();
            // 已制单数量(包含已做送货单未确认送货和已做送货单已确认送货数据)
            BigDecimal makeNum = purItem.getMakeNum() == null? BigDecimal.ZERO:purItem.getMakeNum();
            // 已送货数量
            BigDecimal fixNum = purItem.getFixNum() == null? BigDecimal.ZERO:purItem.getFixNum();
            // 待送数量
            BigDecimal waitNum = purItem.getWaitNum() == null? BigDecimal.ZERO:purItem.getWaitNum();
            // 增加已匹配计划数量
            purItem.setMatchedPlanNum(matchedPlanNum.add(devNum));
            // 增加已制单数量
            purItem.setMakeNum(makeNum.add(devNum));
            // 增加已送货数量
            purItem.setFixNum(fixNum.add(devNum));
            // 减少待送货数量
            purItem.setWaitNum(waitNum.subtract(devNum));
            if (purItem.getWaitNum().compareTo(BigDecimal.ZERO) == 0 || purItem.getWaitNum().compareTo(BigDecimal.ZERO) < 0){
                purItem.setWaitNum(BigDecimal.ZERO);
            }
            purItemService.updateById(purItem);
        }
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateReceiveNum(Long id, BigDecimal receiveNum) {
        PurItemEntity purItemEntity =purItemService.getById(id);//订单明细Id
        if (purItemEntity != null){
            if(purItemEntity.getReceiveNum() == null){
                purItemEntity.setReceiveNum(BigDecimal.ZERO);
            }
            //暂收数量不能大于订单数量
            if(purItemEntity.getReceiveNum().compareTo(purItemEntity.getOrderNum())==1){
                BigDecimal subtract = purItemEntity.getReceiveNum().subtract(purItemEntity.getOrderNum());
                throw new RRException("已送数量超出"+subtract);
            }
            purItemEntity.setReceiveNum(purItemEntity.getReceiveNum().add(receiveNum));//订单明细更新暂收数量
            purItemService.updateById(purItemEntity);
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public void updateRefundNum(Long id, BigDecimal refundNum) {
        PurItemEntity purItemEntity =purItemService.getById(id);//销售明细Id
        if (purItemEntity != null){
            if(purItemEntity.getRefundNum() == null){
                purItemEntity.setRefundNum(BigDecimal.ZERO);
            }
            purItemEntity.setRefundNum(purItemEntity.getRefundNum().add(refundNum));
            BigDecimal makeNum = purItemEntity.getMakeNum().subtract(refundNum);
            purItemEntity.setMakeNum(makeNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:makeNum);
            BigDecimal fixNum = purItemEntity.getFixNum().subtract(refundNum);
            purItemEntity.setFixNum(fixNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:fixNum);
            BigDecimal waitNum = purItemEntity.getWaitNum().add(refundNum);
            purItemEntity.setWaitNum(waitNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:waitNum);
            purItemService.updateById(purItemEntity);
        }
    }

    /**
     * ERP按采购订单直接暂退
     * @param id
     * @param refundNum
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public void updateRefundNumByOrderReturn(Long id, BigDecimal refundNum) {
        PurItemEntity purItemEntity = purItemService.getById(id);
        Assert.isNull(purItemEntity, "采购订单明细不存在，id："+ id);
        BigDecimal refNum = purItemEntity.getRefundNum().add(refundNum);
        purItemEntity.setRefundNum(refNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:refNum);
        BigDecimal makeNum = purItemEntity.getMakeNum().subtract(refundNum);
        purItemEntity.setMakeNum(makeNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:makeNum);
        BigDecimal fixNum = purItemEntity.getFixNum().subtract(refundNum);
        purItemEntity.setFixNum(fixNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:fixNum);
        BigDecimal waitNum = purItemEntity.getWaitNum().add(refundNum);
        purItemEntity.setWaitNum(waitNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:waitNum);
        // 按计划送货
        if (PurItemDeliveryTypeEnum.PLAN.getValue().equals(purItemEntity.getDeliveryType())){
            BigDecimal planNum = purItemEntity.getMatchedPlanNum().subtract(refundNum);
            purItemEntity.setMatchedPlanNum(planNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : planNum);
        }
        purItemService.updateById(purItemEntity);
    }

    /**
     * ERP暂退并扣款
     * @param id
     * @param refundNum
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateRefundNumByRetDed(Long id, BigDecimal refundNum) {
        PurItemEntity purItemEntity = purItemService.getById(id);
        Assert.isNull(purItemEntity, "采购订单明细不存在，id："+ id);
        BigDecimal refNum = purItemEntity.getRefDedNum().add(refundNum);
        purItemEntity.setRefDedNum(refNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO:refNum);
        purItemService.updateById(purItemEntity);
    }


    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(PurEntity record, Class<?> cls) {

        //设置明细的主表ID
        ListUtils.setPropertyValue(record.getPurItemEntityList(),"pur_id",record.getId());

        ValidatorUtils.validateEntity(record, cls);

        for (PurItemEntity purItemEntity : record.getPurItemEntityList()) {
            int index = record.getPurItemEntityList().indexOf(purItemEntity) + 1;
            try {
                ValidatorUtils.validateEntity(purItemEntity, cls);
            }catch (RRException e){
                throw new RRException(String.format("第%s行 预订单明细校验失败<br/>"+e.getMsg(), index));
            }
        }
    }

    /**
     * 查询物料信息
     * @param goodsIdList
     * @return
     */
    private Map<Long, GoodsVO> queryGoodsList(List<Long> goodsIdList){
        List<GoodsVO> goodsVoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsIdList)){
            return null;
        }
        // 每次批量查询的物料数量
        int batchSize = 10;
        for (int i = 0; i < goodsIdList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, goodsIdList.size());
            List<Long> subList = goodsIdList.subList(i, end);
            GoodsReqVo goodsReqVo = new GoodsReqVo();
            goodsReqVo.setGoodsIdList(subList);
            List<GoodsVO> goodsList = baseClient.queryGoodsList(goodsReqVo);
            // 合并添加物料信息
            if (CollectionUtil.isNotEmpty(goodsList)){
                goodsVoList.addAll(goodsList);
            }
        }
        if (CollectionUtil.isNotEmpty(goodsVoList)){
            return goodsVoList.stream()
                    .filter(vo -> ObjectUtil.isNotEmpty(vo.getId()))
                    .collect(Collectors.toMap(
                            GoodsVO::getId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        }
        return null;
    }

    /**
     *  站内信及邮箱信息发送
     */
    private void sendEmailAndMessage(PurEntity purEntity){
        JSONObject mailJson = new JSONObject();
        mailJson.put("tenantId",purEntity.getTenantId());
        mailJson.put("userId",0L);
        mailJson.put("content",purEntity.getVendorName()+"采购订单"+purEntity.getPurNo()+PurStatEnum.getStat(purEntity.getStat()));
        mailJson.put("menuTitle","采购订单"+PurStatEnum.getStat(purEntity.getStat()));//菜单标题
        mailJson.put("url","order/produce/tenant?id="+purEntity.getId());//菜单地址
        sysClient.sendMail(mailJson);
//        sendConfirmOrRefuseEmail(purEntity);
    }

    /**
     * 供应商拒收采购订单发送信息通知采购员
     * @param purEntity
     */
    private void sendRejectionMsgToPur(PurEntity purEntity){
        try {
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(purEntity.getTenantId(), purEntity.getVendorId());
            Assert.isNull(vendorVo, "供应商不存在");
            if (ObjectUtil.isNotEmpty(vendorVo.getPurId())){
                List<SysUserVO> sysUserVOS = sysClient.queryUserByIds(Collections.singletonList(vendorVo.getPurId()));
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId", purEntity.getTenantId());
                mailJson.put("userId", vendorVo.getPurId());
                mailJson.put("content",String.format("供应商%s拒收了采购订单%s",vendorVo.getVendorName(),purEntity.getPurNo()));
                mailJson.put("menuTitle","采购订单详情");//菜单标题
//                mailJson.put("url","order/pur/purForm.html?id="+purEntity.getId());//菜单地址
                mailJson.put("url","order/produce/tenant?id="+purEntity.getId());//菜单地址
                sysClient.sendMail(mailJson);
                // 供应商邮箱地址不为空
                if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())){
                    EmailMessageVo emailMessageVo = new EmailMessageVo();
                    emailMessageVo.setTenantId(purEntity.getTenantId());
                    emailMessageVo.setTitle("您有一封新的邮件信息");
                    String content = vendorVo.getPurName()+",您好：\n" +
                            "<br>\n" +
                            "<br>供应商:"+vendorVo.getVendorName()+"拒收了采购订单("+purEntity.getPurNo()+")，请知悉！ \n" +
                            "<br>\n" +
                            "<br>请您及时查阅、处理！ 如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                            "<br>\n";
                    emailMessageVo.setContent(content);
                    emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                    baseClient.customSendEmail(emailMessageVo);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }


    /**
     * 供应商确认采购订单发送系统消息和邮件给对应的采购员
     * @param purEntity
     */
    private void sendConfirmMsgToPur(PurEntity purEntity){
        try {
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(purEntity.getTenantId(), purEntity.getVendorId());
            Assert.isNull(vendorVo, "供应商不存在");
            if (ObjectUtil.isNotEmpty(vendorVo.getPurId())){
                List<SysUserVO> sysUserVOS = sysClient.queryUserByIds(Collections.singletonList(vendorVo.getPurId()));
                JSONObject mailJson = new JSONObject();
                mailJson.put("tenantId", purEntity.getTenantId());
                mailJson.put("userId", vendorVo.getPurId());
                mailJson.put("content",String.format("供应商%s确认了采购订单%s",vendorVo.getVendorName(),purEntity.getPurNo()));
                mailJson.put("menuTitle","采购订单详情");//菜单标题
                mailJson.put("url","order/produce/tenant?id="+purEntity.getId());//菜单地址
                sysClient.sendMail(mailJson);
                // 供应商邮箱地址不为空
                if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())){
                    EmailMessageVo emailMessageVo = new EmailMessageVo();
                    emailMessageVo.setTenantId(purEntity.getTenantId());
                    emailMessageVo.setTitle("您有一封新的邮件信息");
                    String content = vendorVo.getPurName()+",您好：\n" +
                            "<br>\n" +
                            "<br>供应商:"+vendorVo.getVendorName()+"确认了采购订单("+purEntity.getPurNo()+")，请知悉！ \n" +
                            "<br>\n" +
                            "<br>请您及时查阅、处理！ 如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                            "<br>\n";
                    emailMessageVo.setContent(content);
                    emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                    baseClient.customSendEmail(emailMessageVo);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }
}
