/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.dm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.base.BaseClient;
import com.dian.client.order.OrderClient;
import com.dian.client.sys.SysClient;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.server.impl.BaseServiceImpl;
import com.dian.common.utils.*;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.config.AppConfig;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.mbo.sap.zmm009.InTransitQuantityInfoVO;
import com.dian.mbo.sap.zmm009.InTransitQuantityParamVO;
import com.dian.mbo.sap.zmm009.request.SapInTransitQuantityVO;
import com.dian.mbo.sap.zmm009.response.SapInTransitQuantityReturnInfoVO;
import com.dian.mes.rdyservice.request.SrmQuaNumInputMessage;
import com.dian.mes.rdyservice.request.SrmQuaNumInputMessageLine;
import com.dian.modules.base.query.VendorReqVo;
import com.dian.modules.base.vo.SaveGoodsBarVo;
import com.dian.modules.base.vo.VendorVO;
import com.dian.modules.dm.dao.DeliveryItemDao;
import com.dian.modules.dm.entity.*;
import com.dian.modules.dm.service.*;
import com.dian.modules.dm.vo.*;
import com.dian.modules.enums.common.IsCompromiseEnum;
import com.dian.modules.enums.common.OperBillEnum;
import com.dian.modules.enums.common.OperTypeEnum;
import com.dian.modules.enums.dm.*;
import com.dian.modules.mes.service.MesApiService;
import com.dian.modules.order.vo.PurItemLineVO;
import com.dian.modules.sap.service.SapApiService;
import com.dian.util.SapDateUtils;
import io.seata.spring.annotation.GlobalTransactional;
import jdk.nashorn.internal.objects.annotations.Where;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 送货单物料表服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-07 10:00:34
 */
@Service("deliveryItemService")
public class DeliveryItemServiceImpl extends BaseServiceImpl<DeliveryItemDao, DeliveryItemEntity> implements DeliveryItemService {

    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Autowired
    public CommonService commonService;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private DeliveryItemService deliveryItemService;

    @Autowired(required = false)
    private OrderClient orderClient;

    @Autowired(required = false)
    private BaseClient baseClient;

    @Autowired(required = false)
    private SysClient sysClient;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    MesApiService mesApiService;

    @Autowired
    SheetHeadService sheetHeadService;

    @Autowired
    SheetLineService sheetLineService;

    @Autowired
    SheetItemService sheetItemService;

    @Autowired
    private DefaultService defaultService;

    @Autowired
    private DeliveryPlanItemService deliveryPlanItemService;

    @Autowired
    private SapApiService sapApiService;


    /**
     * 送货单物料表分页
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        IPage<DeliveryItemEntity> page = this.page(new Query<DeliveryItemEntity>().getPage(params),getQueryWrapper(params) );
        return new PageUtils(page);
    }

    /**
     *  送货单物料表新增
     * @param deliveryItemEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean saveInfo(DeliveryItemEntity deliveryItemEntity) {

        //设置编码，等基础默然初始化数据设置
        //数据完整性校验
        this.paramsCheck(deliveryItemEntity,AddGroup.class);

        //保存
        this.save(deliveryItemEntity);

        return true;
    }

    /**
     * 送货单物料表新增V2
     * @param delivery
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean saveInfoV2(DeliveryEntity delivery) {
        List<DeliveryItemEntity> validItemList = delivery.getDeliveryItemEntityList().stream().filter(item -> !WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).collect(Collectors.toList());
        List<DeliveryItemEntity> notValidItemList = delivery.getDeliveryItemEntityList().stream().filter(item -> WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).collect(Collectors.toList());
        batchDelete(delivery,notValidItemList);
        validItemList.forEach(item -> {
            item.setDeId(delivery.getId());
            if (ObjectUtil.isEmpty(item.getId())){
                item.setTemNum(BigDecimal.ZERO);
                item.setUnInvNum(item.getDevNum());
                item.setInvNum(BigDecimal.ZERO);
                this.save(item);
            } else {
                this.updateById(item);
            }
        });
        List<Long> orderItemIds = validItemList.stream().map(item -> item.getSaleItemId()).distinct().collect(Collectors.toList());
        List<PurItemLineVO> orderItemList = new ArrayList<>();
        orderItemIds.forEach(orderItemId -> {
            PurItemLineVO orderLine = new PurItemLineVO();
            BigDecimal devNum = this.getBaseMapper().countDevNumByOrderItem(orderItemId);
            orderLine.setId(orderItemId);
            orderLine.setMakeNum(devNum);
            orderItemList.add(orderLine);
        });
        orderClient.againtCountDevNum(orderItemList);
        return true;
    }

    /**
     *送货单物料表更新
     * @param deliveryItemEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateInfo(DeliveryItemEntity deliveryItemEntity) {
        //修改状态校验
        this.updateCheck(deliveryItemEntity.getId());
        //更新主表
        this.updateById(deliveryItemEntity);
        return true;
    }

    @Override
    public BigDecimal countDevNumByOrderItem(Long orderItemId) {
        return this.getBaseMapper().countDevNumByOrderItem(orderItemId);
    }

    /**
     *送货单物料表删除(采购方)
     * @param ids
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean deleteInfo(Long[] ids) {
        String isDmDelect = sysClient.getValueByKeyAndTenantId("isDmDelect", commonService.getTenantId());
        if (isDmDelect.equals("0")){//根据isDmDelect系统参数来判断采购方是否允许删除已发货的送货单 0-否（不允许） 1-是（允许）
            throw new RRException("选择的送货单的状态已为已发出，无法进行删除");
        }
        //删除状态校验
//        this.deleteCheck(ids);
        for (Long id:ids) {
            DeliveryItemEntity deliveryItemEntity = this.getById(id);
            DeliveryEntity deliveryEntity = deliveryService.getById(deliveryItemEntity.getDeId());
            deliveryItemEntity.getSaleItemId();//订单明细id
            deliveryItemEntity.getDevNum();//送货数量
            deliveryItemEntity.getInvNum();//验收已入库数量（收货数量）
            if (!StrUtil.isBlankIfStr(deliveryItemEntity.getPlanSrmLineId())){//判断送货计划明细表id不为空时，需要修改送货计划的已制单数量
                DeliveryPlanItemEntity deliveryPlanItemEntity = deliveryPlanItemService.getById(deliveryItemEntity.getPlanSrmLineId());
                BigDecimal primaryMakeNum = deliveryPlanItemEntity.getMakeNum();//获取送货计划明细的原已制单数
                deliveryPlanItemEntity.setMakeNum(primaryMakeNum.subtract(deliveryItemEntity.getDevNum()));//删除送货单后送货计划明细的已制单数 = 原送货计划明细已制单数-送货单送货数量
                deliveryPlanItemEntity.setDeleteFlag(0);
                deliveryPlanItemService.updateById(deliveryPlanItemEntity);
            }
//            删除时回写采购订单中相对应的数量，如：可制单数量等
            orderClient.updateOrderItemMakeNum(
                    deliveryItemEntity.getSaleItemId(),
                    deliveryItemEntity.getDevNum(),deliveryEntity.getDeStat()
            );
            this.remove(new QueryWrapper<DeliveryItemEntity>().in("id",ids));

        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchDelete(DeliveryEntity delivery,List<DeliveryItemEntity> list) {
        if (CollectionUtil.isNotEmpty(list)){
            list = list.stream().filter(item -> ObjectUtil.isNotEmpty(item.getId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(list)){
                list.forEach(item -> {
                    if (DeliveryTypeEnum.PLANDELIVERY.getValue().equals(delivery)){
                        deliveryPlanItemService.updateByMakeNumV2(item.getPlanSrmLineId(),item.getDevNum().negate());
                    }
                    orderClient.updateMakenum(item.getSaleItemId(), item.getDevNum().negate(), null);
                    this.updateById(item);
                });
            }
        }
        return true;
    }

    /**
     * 送货单物料表详情
     * @param id
     * @return
     */
    @Override
    public DeliveryItemEntity getInfo(Long id) {
        DeliveryItemEntity deliveryItemEntity =getById(id);
        return deliveryItemEntity;
    }

    /**
     * 送货单物料表审核
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean checkInfo(Long id) {
        DeliveryItemEntity deliveryItemEntity =this.getById(id);
        checkCheck(deliveryItemEntity);
        //deliveryItemEntity.setOrderState(OrderHead_OrderStateEnum.AUDITED.getValue());
        //deliveryItemEntity.setOrderDate(new Date());
        ///deliveryItemEntity.setCheckUserId(commonService.getUserId());
        //deliveryItemEntity.setCheckUserName(commonService.getUserName());
        return this.updateById(deliveryItemEntity);
    }

    /**
     *送货单物料表当前页or全部导出
     * 2021-4-8
     * meng
     * @param params              getQueryWrapper
     * @return
     */
//    @Override
//    public List<DeliveryItemExportVO> exportList(Map<String, Object> params) {
//        List<HashMap<String,Object>> orderAndDeliveryList;
//        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
//
//        Map<String, Object> map=new HashMap<>();//导出全部参数
//        if(params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))){//采购方进入送货列表
//            map.put("tenantIds",commonService.getTenantId());//导出全部参数
//            params.remove("tenantIds");//导出当前页参数
//            params.put("tenantIds",commonService.getTenantId());//导出当前页参数
//        }else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
//            map.put("vendorId",commonService.getTenantId());//导出全部参数
//            params.remove("vendorIds");//导出当前页参数
//            params.put("vendorIds",commonService.getTenantId());//导出当前页参数
//        }
//
//        if(!StrUtil.isBlankIfStr(params.get("exportType")) && Integer.parseInt(params.get("exportType").toString()) != 1){
//            orderAndDeliveryList=getDeliveryList(page,params);//接收数据
//        }else{
//            orderAndDeliveryList= getDeliveryList(page,map);//接收数据
//        }
//        logger.info("orderAndDeliveryList ================================== "+orderAndDeliveryList.toString());
//        List<DeliveryItemExportVO> resultList = new ArrayList<>();//导出数据实体类
//
//        if(CollectionUtils.isNotEmpty(orderAndDeliveryList)) {//转换
//            resultList = BeanConverter.convertList(orderAndDeliveryList, DeliveryItemExportVO.class);
//        }
//        return resultList;
//    }

    @Override
    public List<DeliveryItemExportVO> exportList(Map<String, Object> params) {
        if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }
        List<HashMap<String,Object>> list;
        if(!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType")+"")){
        }else{
            params.put("page",1);
            params.put("limit",Long.MAX_VALUE);
        }

        list = this.getDeliveryListPage(params).getList();
        //创建一个采购方质检报告单导出类的集合
        List<DeliveryItemExportVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)){
            //假如list判断为空
            resultList = BeanConverter.convertList(list,DeliveryItemExportVO.class);
        }
        return resultList;
    }

    /**
     * 获取送货列表_分页
     * meng
     * 2021-4-8
     * @param params
     * @return
     */
    @Override
    public PageUtils getDeliveryListPage(Map<String, Object> params){
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getDeliveryList(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 2021-4-8
     * meng
     * @param page
     * @param params
     * @return
     */
    public List<HashMap<String, Object>> getDeliveryList(IPage<HashMap<String, Object>> page,Map<String, Object> params){
        try {
            if (params.get("deliveryDate") != null && !params.get("deliveryDate").equals("")) {
                String[] split = params.get("deliveryDate").toString().split(" 至 ");
                params.put("sdeliveryDate", split[0] + " 00:00:00");
                params.put("edeliveryDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("送货单搜索送货日期有误");
            throw new RRException("送货日期输入有误");
        }

        if(params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))){//采购方进入送货列表
            params.remove("tenantIds");
            params.put("tenantIds",commonService.getTenantId());
        }else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }
        List<HashMap<String, Object>> list = getBaseMapper().getDeliveryLists(page, params);
        if(CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    /**
     * 2021-4-8
     * meng
     * @param params
     * @return
     */
    @Override
    public HashMap<String, Object> getDeliveryCount(Map<String, Object> params){
        Map<String,Object> map = new HashMap<>();

        try {
            if (params.get("deliveryDate") != null && !params.get("deliveryDate").equals("")) {
                String[] split = params.get("deliveryDate").toString().split(" 至 ");
                params.put("sdeliveryDate", split[0] + " 00:00:00");
                params.put("edeliveryDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("送货单搜索送货日期有误");
            throw new RRException("送货日期输入有误");
        }

        if(params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))){//采购方进入送货列表
            params.remove("tenantIds");
            params.put("tenantIds",commonService.getTenantId());
        }else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }

        params.put("pur","pur");
        int sentButNotReceivedCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount")+"");
        params.remove("pur");

        params.put("unsNum","unsNum");
        int temporaryReturnCount =  Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount")+"");
        params.remove("unsNum");

        int allCount =  Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount")+"");
        // 暂收
        params.put("temNum","temNum");
        int temporaryIncomeCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount")+"");
        params.remove("temNum");

        params.put("deStat", 1);
        int tobeissued = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount") + "");
        params.remove("deStat");
        //已送货
        params.put("shipped", 2);
        int shipped = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount") + "");
        params.remove("shipped");

        //是否需要让步的数据
        params.put("isCompromise","isCompromise");
        int isCompromiseCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount").toString());
        params.remove("isCompromise");

        //是否有未检验的数据
        params.put("isQuality","isQuality");
        int isQualityCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount").toString());
        params.remove("isQuality");


        params.put("defectiveManualReturn","defectiveManualReturn");
        int defectiveManualReturnCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount").toString());
        params.remove("defectiveManualReturn");

        // 待发出
        map.put("tobeissued", tobeissued);
        //已发出（已送货）
        map.put("shipped", shipped);
        map.put("sentButNotReceivedCount",sentButNotReceivedCount);
        map.put("temporaryReturnCount",temporaryReturnCount);
        map.put("allCount",allCount);
        // 暂收
        map.put("temporaryIncomeCount",temporaryIncomeCount);
        map.put("isCompromiseCount",isCompromiseCount);
        map.put("isQualityCount",isQualityCount);
        map.put("defectiveManualReturnCount",defectiveManualReturnCount);
        return (HashMap<String, Object>) map;
    }


    /**
     * 生成条码回写到送货明细表
     * meng
     * 2021-4-13
     * @param params
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public R updateDeliveryItem(Map<String, Object> params) {
        //生成条码
        JSONObject goodsBarcodeJson = new JSONObject();
        // 自定义条码 不更新大小包条码
        if (params.containsKey("headWork") && !StrUtil.isEmptyIfStr(params.get("headWork"))) {
            //生成手工条码数据并返回
            goodsBarcodeJson = baseClient.saveHeadWorkBarcode(JSONObject.parseObject(JSONObject.toJSONString(params.get("barCodeParams"))));
            return R.ok().put(goodsBarcodeJson);
        } else if ("1".equals(params.get("falg"))) {
            goodsBarcodeJson = baseClient.saveDefindBarcode(JSONObject.parseObject(JSONObject.toJSONString(params.get("barCodeParams"))));
        } else {
            goodsBarcodeJson=baseClient.saveGoodsBarcode( JSONObject.parseObject(JSONObject.toJSONString(params.get("barCodeParams"))));//生成条码数据返回信息
        }
        return updateDeliveryItemMethod(params,goodsBarcodeJson);
    }

    /**
     * 根据送货单明细批量生成条码
     * @param saveGoodsBarVo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean generateBarcodeByDelivery(SaveGoodsBarVo saveGoodsBarVo) {
        DeliveryEntity deliveryEntity = deliveryService.getInfo(saveGoodsBarVo.getSourceId());
        Assert.isNull(deliveryEntity, "未找到送货单数据");
        if (CollectionUtil.isEmpty(deliveryEntity.getDeliveryItemEntityList())){
            throw new RRException("送货单明细数据不能为空");
        }
        Assert.isNull(saveGoodsBarVo.getProdDate(), "生产日期不能为空");
        saveGoodsBarVo.setSumQty(saveGoodsBarVo.getDevNum());
        saveGoodsBarVo.setProductDate(DateUtil.parseDate(saveGoodsBarVo.getProdDate()));
        saveGoodsBarVo.setBigBarQty(new BigDecimal(saveGoodsBarVo.getBigPackStandardNum()));
        saveGoodsBarVo.setBigBarSheets(new BigDecimal(saveGoodsBarVo.getBigPackLabelNum()));
        saveGoodsBarVo.setBigMantissa(new BigDecimal(saveGoodsBarVo.getBigPackMantissa()));
        saveGoodsBarVo.setSmallBarQty(new BigDecimal(saveGoodsBarVo.getSmallPackStandardNum()));
        saveGoodsBarVo.setSmallBarSheets(new BigDecimal(saveGoodsBarVo.getSmallPackLabelNum()));
        saveGoodsBarVo.setSmallMantissa(new BigDecimal(saveGoodsBarVo.getSmallPackMantissa()));
        saveGoodsBarVo.setSourceNo(deliveryEntity.getDeNo());
        saveGoodsBarVo.setSourceType(2);
        List<DeliveryItemEntity> deliveryItemList = deliveryEntity.getDeliveryItemEntityList().stream().filter(item -> item.getGoodsErpCode().equals(saveGoodsBarVo.getGoodsErpCode())).collect(Collectors.toList());
        baseClient.batchSaveBarCode(saveGoodsBarVo);
        deliveryItemList.forEach(item -> {
            item.setBigPackStandardNum(saveGoodsBarVo.getBigPackStandardNum());
            item.setBigPackLabelNum(saveGoodsBarVo.getBigPackLabelNum());
            item.setBigPackMantissa(saveGoodsBarVo.getBigPackMantissa());
            item.setSmallPackStandardNum(saveGoodsBarVo.getSmallPackStandardNum());
            item.setSmallPackLabelNum(saveGoodsBarVo.getSmallPackLabelNum());
            item.setSmallPackMantissa(saveGoodsBarVo.getSmallPackMantissa());
            this.updateById(item);
        });
        return true;
    }

    /**
     * 根据送货主表id查询送货明细
     * meng
     * 2021-4-9
     * @param params
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Map<String, Object> findDeliveryAndSaleOrderMap(Map<String, Object> params) {
        Map<String,Object> mapObject=new HashMap<>();
        if (params.containsKey("vendorIds") && params.containsKey("id") && !StrUtil.isEmptyIfStr(params.get("id"))){
            DeliveryEntity deliveryEntity =deliveryService.getById(Long.parseLong(params.get("id").toString()));
            if (deliveryEntity != null){
                //查询客户名称
                /*Map<String,Object> queryVendorMap=new HashMap<>();
                queryVendorMap.put("tenantId",deliveryEntity.getTenantId());
                queryVendorMap.put("soureId",deliveryEntity.getVendorId());
                Map<String,Object> vendorMap= (Map<String,Object>) baseClient.queryVendorName(queryVendorMap).get("vendorEntity");
                if (vendorMap != null){
                    deliveryEntity.setTenantName(vendorMap.get("tenantName").toString());
                }*/
                String ifQuality = sysClient.getValueByKeyAndTenantId("ifQuality", deliveryEntity.getTenantId());//根据采购方租户id获取系统参数 - 是否质检
//                logger.info("ifQuality 是否质检 系统参数 ===================== "+ifQuality);
                //查询客户名称结束
                params.remove("vendorIds");//删除数据
                params.put("vendorIds",commonService.getTenantId());
                List<HashMap<String,Object>> listHashMap=getBaseMapper().findDeliveryAndSaleOrderMap(params);
                if (CollectionUtils.isNotEmpty(listHashMap)){
                    if (StringUtils.isNotBlank(ifQuality)){//ifQuality不为空时
                        deliveryEntity.setIfQuality(ifQuality);//ifQuality 是否质检参数 0-否 1-是 默认为0
                    }
                    mapObject.put("delivery",deliveryEntity);
                    mapObject.put("deliveryAndSaleOrder",listHashMap);
                    //保存日志
                    JSONObject saveOperMsg=new JSONObject();
                    saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
                    saveOperMsg.put("operType", OperTypeEnum.INFO.getValue());//类型是查看送货明细
                    saveOperMsg.put("soureHeadId", deliveryEntity.getId());
                    saveOperMsg.put("remark", "查看送货明细:"+deliveryEntity.getDeNo());
                    saveOperMsg.put("attr1", deliveryEntity.getDeNo());
                    saveOperMsg.put("attr2", commonService.getUserName());
                    saveOperMsg.put("tenantId", commonService.getTenantId());
                    saveOperMsg.put("vendorId", deliveryEntity.getVendorId());
                    baseClient.saveOperMsg(saveOperMsg);
                    return mapObject;
                }
            }
        }else  if (params.containsKey("tenantIds") && params.containsKey("id") && !StrUtil.isEmptyIfStr(params.get("id"))){
            DeliveryEntity deliveryEntity =deliveryService.getById(Long.parseLong(params.get("id").toString()));
            if (deliveryEntity != null){
                params.remove("tenantIds");//删除数据
                params.put("tenantIds",commonService.getTenantId());
                List<HashMap<String,Object>> listHashMap=getBaseMapper().findDeliveryAndSaleOrderMap(params);
                if (CollectionUtils.isNotEmpty(listHashMap)){
                    mapObject.put("delivery",deliveryEntity);
                    mapObject.put("deliveryAndSaleOrder",listHashMap);
                    //保存日志
                    JSONObject saveOperMsg=new JSONObject();
                    saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
                    saveOperMsg.put("operType", OperTypeEnum.INFO.getValue());//类型是查看送货明细
                    saveOperMsg.put("soureHeadId", deliveryEntity.getId());
                    saveOperMsg.put("remark", "查看送货明细:"+deliveryEntity.getDeNo());
                    saveOperMsg.put("attr1", deliveryEntity.getDeNo());
                    saveOperMsg.put("attr2", commonService.getUserName());
                    saveOperMsg.put("tenantId", commonService.getTenantId());
                    saveOperMsg.put("vendorId", deliveryEntity.getVendorId());
                    baseClient.saveOperMsg(saveOperMsg);
                    return mapObject;
                }
            }
        }

        return null;
    }

    @Override
    public List<DeliveryItemEntity> queryItemById(Long deId) {
        return this.list(new LambdaQueryWrapper<DeliveryItemEntity>()
                .eq(DeliveryItemEntity::getDeId,deId)
                .eq(DeliveryItemEntity::getDeleteFlag, WhetherEnum.NO.getCode())
        );
    }

    /**
     * 根据id查询送货明细
     * @param id
     * @return
     */
    @Override
    public DeliveryItemVO getDeliveryItemVoById(Long id) {
        DeliveryItemEntity deliveryItem = this.getById(id);
        if (Objects.nonNull(deliveryItem)){
            DeliveryEntity delivery = deliveryService.getById(deliveryItem.getDeId());
            deliveryItem.setDeliveryNo(ObjectUtil.isNotEmpty(delivery) ? delivery.getDeNo() : "");
            return BeanConverter.convert(deliveryItem, DeliveryItemVO.class);
        }
        return null;
    }

    /**
     * 根据送货单号查询送货明细
     * @param deNo
     * @return
     */
    @Override
    public List<DeliveryItemVO> getDeliveryItemVoByDeNo(String deNo) {
        DeliveryEntity delivery = this.deliveryService.getOne(new LambdaQueryWrapper<DeliveryEntity>().eq(DeliveryEntity::getDeNo, deNo).eq(DeliveryEntity::getDeleteFlag, WhetherEnum.NO.getCode()));
        if (Objects.nonNull(delivery)){
            List<DeliveryItemEntity> list = this.list(new LambdaQueryWrapper<DeliveryItemEntity>().eq(DeliveryItemEntity::getDeId, delivery.getId()));
            return BeanConverter.convertList(list, DeliveryItemVO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 修改条码数据并生成回写到送货明细表
     * meng
     * @param params
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public R updateBarCodeAndDeliveryQuantity(Map<String, Object> params) {
//        //修改条码数据返回值
        JSONObject goodsBarCodeJson=baseClient.updateGoodsBarCode(JSONObject.parseObject(JSONObject.toJSONString(params.get("barCodeParams"))));
//        //在回写送货明细条码数据
//        updateDeliveryItemMethod(params, goodsBarCodeJson);
        return  updateDeliveryItemMethod(params, goodsBarCodeJson);
    }

    private R updateDeliveryItemMethod(Map<String,Object> params,JSONObject goodsBarcodeJson){
        Map<String,Object> resultMap=new HashMap<>();
        //获取obj数据（采购订单数据）并存入map对象中
        Map<String,Object> map = (Map<String, Object>) params.get("obj");
        if (params.containsKey("obj") && !StrUtil.isEmptyIfStr(params.get("obj")) && goodsBarcodeJson.containsKey("smallBag")){
            JSONObject printDetailLinesJson=JSONObject.parseObject(JSONObject.toJSONString(params.get("obj")));//前端选中行的信息
            DeliveryItemEntity deliveryItemEntity= deliveryItemService.getById(Long.parseLong(printDetailLinesJson.getLong("deliveryId").toString()));//获取当前送货明细信息
            DeliveryEntity deliveryEntity = deliveryService.getById(deliveryItemEntity.getDeId());
            //回写(送货详情表)
            if (goodsBarcodeJson.containsKey("smallBag") && goodsBarcodeJson.containsKey("defindFalg")) {
                JSONObject defindBagJson = JSONObject.parseObject(JSONObject.toJSONString(goodsBarcodeJson.get("smallBag")));
                deliveryItemEntity.setBarcodeType(printDetailLinesJson.getInteger("barcodeType"));
//                deliveryItemEntity.setSmallPackStandardNum(defindBagJson.getLong("builtInSum"));//标准数量
//                deliveryItemEntity.setSmallPackLabelNum(defindBagJson.getLong("sum"));//标签数量
//                deliveryItemEntity.setSmallPackMantissa(defindBagJson.getLong("mantissa"));//尾数
                deliveryItemEntity.setDefindStat(1); // 是自定义的条码
            } else if(goodsBarcodeJson.containsKey("smallBag")) {//条码类型(smallBag:小包)
                JSONObject smallBagJson = JSONObject.parseObject(JSONObject.toJSONString(goodsBarcodeJson.get("smallBag")));
                deliveryItemEntity.setBarcodeType(printDetailLinesJson.getInteger("barcodeType"));
                deliveryItemEntity.setSmallPackStandardNum(smallBagJson.getLong("builtInSum"));//标准数量
                deliveryItemEntity.setSmallPackLabelNum(smallBagJson.getLong("sum"));//标签数量
                deliveryItemEntity.setSmallPackMantissa(smallBagJson.getLong("mantissa"));//尾数
                //barType=2;
            }
            if (goodsBarcodeJson.containsKey("bigBag")) {//条码类型(bigBag:大包)
                JSONObject bigBagJson = JSONObject.parseObject(JSONObject.toJSONString(goodsBarcodeJson.get("bigBag")));
                deliveryItemEntity.setBigPackStandardNum(bigBagJson.getLong("builtInSum"));
                deliveryItemEntity.setBigPackLabelNum(bigBagJson.getLong("sum"));
                deliveryItemEntity.setBigPackMantissa(bigBagJson.getLong("mantissa"));
                //生成二维码,格式为供应商代码+物料代码+批次+数量+条码
                String qrCode="";//定义二维码变量
                //获取物料条码
//                JSONObject jsonObject = baseClient.getErpCode(deliveryItemEntity.getTenantId(), deliveryItemEntity.getGoodsErpCode());
//                if(jsonObject==null){
//                    throw new RRException("系统中获取不到["+deliveryItemEntity.getGoodsErpCode()+"]的物料信息");
//                }
//                String barCode = jsonObject.getString("barCode");
                qrCode=deliveryEntity.getVendorCode()+deliveryItemEntity.getGoodsErpCode()
                        + SapDateUtils.getDateStr(new Date())
                        + deliveryItemEntity.getBigPackLabelNum();
//                        + barCode;
                deliveryItemEntity.setQrCode(qrCode);
            } else {//不生成大包条码
                deliveryItemEntity.setBigPackStandardNum(null);
                deliveryItemEntity.setBigPackLabelNum(null);
                deliveryItemEntity.setBigPackMantissa(null);
            }
            //设置订单行号（订单序号）
            deliveryItemEntity.setSaleSeq(map.get("saleSeq").toString());
            deliveryItemService.updateInfo(deliveryItemEntity);//回写送货详情数量
            //回写(送货详情表)

            //更新供应商交易物料
            if (!goodsBarcodeJson.containsKey("defindFalg")) {
                JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(params.get("obj")));
                JSONObject vendorGoodsJson = JSONObject.parseObject(JSONObject.toJSONString(json.get("vendorGoods")));
                baseClient.updateVendorGoodsInformation(vendorGoodsJson);
            }

            //更新销售订单的条码控制
            Map<String,Object> orderSaleMap=new HashMap<>();
            orderSaleMap.put("saleItemId",printDetailLinesJson.getLong("orderSaleId"));
            orderSaleMap.put("barcodeType",printDetailLinesJson.getInteger("barcodeType"));
            orderClient.updateBarcodeControl(orderSaleMap);

            //保存日志
            JSONObject saveOperMsg=new JSONObject();
            saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
            saveOperMsg.put("operType", OperTypeEnum.BARCODE.getValue());//类型是生成条码
            saveOperMsg.put("soureHeadId", deliveryItemEntity.getDeId());
            saveOperMsg.put("remark", "已在送货单生成条码:"+printDetailLinesJson.getString("deNo"));
            saveOperMsg.put("attr1", printDetailLinesJson.getString("deNo"));
            saveOperMsg.put("attr2", commonService.getUserName());
            saveOperMsg.put("tenantId", commonService.getTenantId());
            saveOperMsg.put("vendorId", commonService.getTenantId());
            baseClient.saveOperMsg(saveOperMsg);

            resultMap.put("code", 200);
            resultMap.put("msg", "条码创建成功");
            resultMap.put("result", deliveryItemEntity);
            return R.ok().put(resultMap);

        }
        resultMap.put("code", 500);
        resultMap.put("msg", "生成条码异常");
        return R.error().put(resultMap);
    }

    /**
     * 更新送货明细打印次数和打印状态
     * meng
     * 2021-4-13
     * @param params
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public R updateDeliveryItemPrintCountAndState(Map<String, Object> params) {
        Map<String,Object> resultMap=new HashMap<>();
        if (params.containsKey("soureLineId")){
            Long id=Long.parseLong(params.get("soureLineId").toString());
            DeliveryItemEntity deliveryItemEntity=this.getById(id);
            if (deliveryItemEntity != null){
                deliveryItemEntity.setIsPrint(1);
                if (deliveryItemEntity.getPrintCount() == null || deliveryItemEntity.getPrintCount() == 0){
                    deliveryItemEntity.setPrintCount(1);
                }else{
                    deliveryItemEntity.setPrintCount(deliveryItemEntity.getPrintCount()+1);
                }
                this.updateById(deliveryItemEntity);

                DeliveryEntity deliveryEntity=deliveryService.getById(deliveryItemEntity.getDeId());
                //更新条码表打印次数和状态
                Map<String,Object> goodsBarCodeMap=new HashMap<>();
                Long barType=Long.parseLong(params.get("barType").toString());

                goodsBarCodeMap.put("soureHeadIds",deliveryEntity.getId());//主表Id
                goodsBarCodeMap.put("soureLineIds",id);//明细Id
                goodsBarCodeMap.put("tenantIds",deliveryEntity.getTenantId());//采购方租户Id
                goodsBarCodeMap.put("barStats",3);//作废标识
                goodsBarCodeMap.put("soureTypes",Long.parseLong(params.get("soureType").toString()));//soure_type_条码生成类型:1-采购单生成；2-送货单生成
                goodsBarCodeMap.put("barTypes",barType);//bar_type_条码类型1-大包条码；2-小包条码
                baseClient.updateGoodsBarCodePrintCountAndState(goodsBarCodeMap);

                String remark="送货明细大包条码已打印";
                if (barType == 1){
                    remark="送货明细小包条码已打印";
                }

                //保存日志
                JSONObject saveOperMsg=new JSONObject();
                saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
                saveOperMsg.put("operType", OperTypeEnum.PRINT.getValue());//类型是打印
                saveOperMsg.put("soureHeadId", deliveryEntity.getId());
                saveOperMsg.put("remark", remark+":"+deliveryEntity.getDeNo());
                saveOperMsg.put("attr1", deliveryEntity.getDeNo());
                saveOperMsg.put("attr2", commonService.getUserName());
                saveOperMsg.put("tenantId", commonService.getTenantId());
                saveOperMsg.put("vendorId", deliveryEntity.getTenantId());
                baseClient.saveOperMsg(saveOperMsg);

                resultMap.put("code",200);
                resultMap.put("msg","成功更新打印次数和打印状态");
                resultMap.put("result",deliveryItemEntity);
                return R.ok(resultMap);
            }
        }
        resultMap.put("code",500);
        resultMap.put("msg","更新打印次数和打印状态出现异常");
        resultMap.put("result",null);
        return R.error().put(resultMap);
    }

    /**
     * 送货明细行上传文件
     * meng
     * 2021-4-27
     * @param params
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public R saveDeliveryItemUpload(Map<String, Object> params) {
        try{
            if(params.containsKey("id") && !StrUtil.isEmptyIfStr(params.get("id"))){
                DeliveryItemEntity deliveryItemEntity=this.getById(Long.parseLong(params.get("id").toString()));
                String remark;
                Integer operType=OperTypeEnum.DOCCONFIRM.getValue();//文档确认
                if (deliveryItemEntity != null){
                    if(params.containsKey("purchaseConfirm")){//采购方确认文档
                        deliveryItemEntity.setPurchaseConfirm(2);
                        remark=commonService.getUserName()+"确认了文档:"+deliveryItemEntity.getVendorDocName();
                    }else{//供应商上传文档
                        deliveryItemEntity.setVendorDocName(params.get("vendorDocName").toString());
                        deliveryItemEntity.setVendorDocUrl(params.get("vendorDocUrl").toString());
                        deliveryItemEntity.setPurchaseConfirm(1);//文档待确认
                        remark=commonService.getUserName()+"上传了文件:"+deliveryItemEntity.getVendorDocName();
                        operType=OperTypeEnum.UPLOAD.getValue();//上传文件
                        baseClient.saveInterface((Map<String, Object>)params.get("demandParams"));
                    }
                    this.updateById(deliveryItemEntity);

                    DeliveryEntity deliveryEntity=deliveryService.getById(deliveryItemEntity.getDeId());
                    //保存日志
                    JSONObject saveOperMsg=new JSONObject();
                    saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue());//类型是送货单
                    saveOperMsg.put("operType", operType);//类型
                    saveOperMsg.put("soureHeadId", deliveryEntity.getId());
                    saveOperMsg.put("remark", remark+"__"+deliveryEntity.getDeNo());
                    saveOperMsg.put("attr1", deliveryEntity.getDeNo());
                    saveOperMsg.put("attr2", commonService.getUserName());
                    saveOperMsg.put("tenantId", commonService.getTenantId());
                    saveOperMsg.put("vendorId", deliveryEntity.getVendorId());
                    baseClient.saveOperMsg(saveOperMsg);
                    return R.ok().put(deliveryItemEntity);
                }
            }
        }catch(Exception e){
            throw new RRException(String.format("[%s] 文档操作异常"));
        }
        return R.error().put(null);
    }

    /**
     * 送货未收计数
     * meng
     * @param params
     * @return
     */
    @Override
    public Integer queryDeliveryNotReceivedCount(Map<String, Object> params){
        if(params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))){//采购方进入送货列表
            params.remove("tenantIds");
            params.put("tenantIds",commonService.getTenantId());
        }else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }
        params.put("pur","pur");
        int sentButNotReceivedCount = Integer.parseInt(getBaseMapper().getDeliveryCount(params).get("deliveryCount")+"");
        if (sentButNotReceivedCount > 0){
            return sentButNotReceivedCount;
        }
        return 0;
    }

    /**
     * 修改生成质检报告单状态
     * @param deliveryItemEntity
     * @return
     */
    @Override
    public boolean updateQualityType(DeliveryItemEntity deliveryItemEntity) {
//        this.update(deliveryItemEntity);
        this.updateById(deliveryItemEntity);
        return true;
    }


    /**
     * 单独页面（无登录）- 根据前端传入的送货单号进行查询
     * <AUTHOR>
     * @date 2021/9/06 11:48:00
     * @param params
     * @return List<Map<String,Object>>
     */
    @Override
    public List<Map<String, Object>> findByDeNo(Map<String,Object> params) {
        CommonService commonService= (CommonService) SpringContextUtils.getBean("commonBizService");
        commonService.setTaskUser(Long.parseLong(params.get("tenantId").toString()));
        params.put("tenantId", commonService.getTenantId());
        //从params对象中获取到deNo，赋值到String deNo对象中，用于判断是否为空
        String deNo = params.get("deNo").toString();
        //当deNo为null或deNo为''
        if (deNo.equals(null) || deNo.equals("")){
            //满足条件时，抛出异常，并在前端显示请输入送货单号
            throw new RRException(String.format("请输入送货单号"));
        }
        //根据params中的deNo进行查询送货单信息
        List<Map<String, Object>> list = getBaseMapper().findByDeNo(params);
        return list;
    }

    /**
     * 单独页面（无登录） - 根据送货id查询相对应的送货明细信息
     * <AUTHOR>
     * @date 2021/9/06 14:26:00
     * @param params
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> findByDeliveryId(Map<String, Object> params) {
        CommonService commonService= (CommonService) SpringContextUtils.getBean("commonBizService");
        commonService.setTaskUser(Long.parseLong(params.get("tenantId").toString()));
        params.put("tenantId", commonService.getTenantId());
        //创建一个空的map对象,用于存储查询的结果，并返回给到前端页面中
        Map<String,Object> mapObj = new HashMap<>();
        //获取到送货单的id并赋值到deliveryId，用于查询
        Long deliveryId = Long.parseLong(params.get("id").toString());
        //根据送货单的id查询出这条送货单主表对应的信息
        DeliveryEntity deliveryEntity = deliveryService.getById(deliveryId);
        //当deliveryEntity不为空（null时），才查询送货单明细信息
        if (deliveryEntity != null){
            //根据送货单id进行查询送货单明细
            List<HashMap<String, Object>> deliveryItemList = getBaseMapper().findDeliveryId(params);
            //deliveryItemList不为空时，就往mapObj添加
            if (CollectionUtils.isNotEmpty(deliveryItemList)){
                //往mapObj中添加deliveryEntity送货单主表信息
                mapObj.put("deliveryEntity",deliveryEntity);
                //往mapObj中添加deliveryItemList送货单明细信息
                mapObj.put("deliveryItemList",deliveryItemList);
                //返回mapObj对象，前端可取出mapObj中的信息
                return mapObj;
            }
        }
        return mapObj;
    }

    /**
     * 填写抽检数量
     * @param params
     * @return
     */
    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Throwable.class)
    public HashMap<String,Object> updateQuaNum(Map<String, Object> params) {
        Long tenantId = Long.parseLong(params.get("tenantId").toString());
        Integer quaCheckStat = Integer.parseInt(params.get("quaCheckStat").toString());
        DeliveryItemEntity deliveryItemEntity = new DeliveryItemEntity();
        DeliveryItemEntity deQuaCheckStat = new DeliveryItemEntity();
        deliveryItemEntity.setDeId(Long.parseLong(params.get("deId").toString()));
        deliveryItemEntity.setSaleNo(params.get("saleNo").toString());
        deliveryItemEntity.setId(Long.parseLong(params.get("deliveryId").toString()));
        deliveryItemEntity.setGoodsErpCode(params.get("goodsErpCode").toString());
        BigDecimal quaNum = new BigDecimal(params.get("quaNum").toString());
        BigDecimal devNum = new BigDecimal(params.get("devNum").toString());
        deliveryItemEntity.setSeq(params.get("deliverySeq").toString());
        deliveryItemEntity.setSaleSeq(params.get("saleSeq").toString());
        deliveryItemEntity.setQuaNum(quaNum);
        deliveryItemEntity.setDevNum(devNum);
        deliveryItemEntity.setSubmitStat(SubmitStatEnum.YES.getCode());
        //设置物料检验完成时间
        deliveryItemEntity.setQualityCompletedTime(new Date());
        BigDecimal competentNum = new BigDecimal(params.get("competentNum").toString());
        deliveryItemEntity.setCompetentNum(competentNum);
        BigDecimal unCompetentNum = new BigDecimal(params.get("unCompetentNum").toString());
        deliveryItemEntity.setUnCompetentNum(unCompetentNum);
        if (IsQualifiedEnum.QUALIFIED.getValue().equals(quaCheckStat)){
            deliveryItemEntity.setQuaCheckStat(IsQualifiedEnum.QUALIFIED.getCode());
        }

        if (IsQualifiedEnum.NOTSELECTED.getValue().equals(quaCheckStat)){//假如前端传过的值为0（不合格）
            deliveryItemEntity.setQuaCheckStat(IsQualifiedEnum.UNQUALIFIED.getCode());
            deliveryItemEntity.setIsCompromise(IsCompromiseEnum.YES.getCode());//将是否允许让步接收状态更改为 是
            deliveryItemEntity.setIsCompromiseCreate(IsCompromiseCreateEnum.NOTCREATED.getCode());
        }

        List<DeliveryItemEntity> deliveryItemEntities = new ArrayList<>();
        deliveryItemEntities.add(deliveryItemEntity);
        DeliveryEntity deliveryEntity = new DeliveryEntity();
        deliveryEntity.setTenantId(tenantId);
        deliveryEntity.setDeNo(params.get("deNo").toString());
        deliveryEntity.setVendorCode(params.get("vendorCode").toString());
        deliveryEntity.setDeliveryItemEntityList(deliveryItemEntities);
        Map<String, Object> byItemIdTwo = sheetHeadService.findByItemIdTwo(params);
        List<HashMap> qualityList = (List<HashMap>) byItemIdTwo.get("qualityList");
        Long sheetHeadId = Long.parseLong(qualityList.get(0).get("sheetLineHeadId").toString());
        SheetHeadEntity sheetHeadEntity = new SheetHeadEntity();
        sheetHeadEntity.setId(sheetHeadId);
        sheetHeadEntity.setCreater(params.get("operName").toString());
        sheetHeadEntity.setModifier(params.get("operName").toString());
        sheetHeadEntity.setModifyDate(new Date());
        sheetHeadService.updateById(sheetHeadEntity);
        List<HashMap<String, Object>> unQuaContents = sheetLineService.findUnQuaContents(sheetHeadId);
        deliveryEntity.setUnQuaContents(unQuaContents);
        //获取是否启用创维MES接口
        String isEnableCwMESInterface = sysClient.getValueByKeyAndTenantId("isEnableMESInterface", tenantId);
        if (!StrUtil.isBlankIfStr(isEnableCwMESInterface) && "1".equals(isEnableCwMESInterface)){
            String sendMes = updateQuaNumToMes(deliveryEntity);//调用MES接口
            String[] strs = sendMes.split("}");
            String msg = strs[0] + "}";
            JSONObject jsonMsg = JSONObject.parseObject(msg);
            if (!jsonMsg.get("unStatus").equals(1)){
                throw new RRException("SRM抽检结果同步到MES失败:"+jsonMsg.get("ucMsg"));
            }
        }



        this.updateById(deliveryItemEntity);

        Map<String,Object> map = new HashMap<>();
        map.put("deliveryId",deliveryItemEntity.getDeId());
        map.put("goodsErpCode",deliveryItemEntity.getGoodsErpCode());

        HashMap<String, Object> deliveryMap = this.findDeliveryItemId(params);//送货明细
        if (IsQualifiedEnum.NOTSELECTED.getValue().equals(quaCheckStat)){//假如前端传过的值为0（不合格）
            List<HashMap<String, Object>> unQualifiedList = sheetHeadService.findUnQualified(params);//查询出该送货单中当前质检的这一款物料的不合格的质检报告信息
            if (unQualifiedList.get(0).get("unQualifiedDescribe") == null || unQualifiedList.get(0).get("unQualifiedDescribe") == " "){
                throw new RRException("目前为不合格状态，不合格报告不能为空");
            }
            if(CollectionUtils.isNotEmpty(unQualifiedList)) {
                HashMap<String, Object> deliveryMap1 = deliveryItemService.findDeliveryItemId(params);//根据送货单明细id查询送货单信息
                deliveryMap.put("unqualified",deliveryItemEntity.getUnCompetentNum());
                try {
                    defaultService.saveByDelivey(deliveryMap, unQualifiedList);//当点击提交保存不合格报告 将defaultMap通过接口传值到defaultService中作为新增保存违约通知单的相关数据
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }

        return deliveryMap;
    }

    @Override
    public HashMap<String, Object> findDeliveryItemId(Map<String,Object> params) {
        CommonService commonService= (CommonService) SpringContextUtils.getBean("commonBizService");
        commonService.setTaskUser(Long.parseLong(params.get("tenantId").toString()));
        params.put("tenantId", commonService.getTenantId());
        HashMap<String, Object> deliveryMap = getBaseMapper().findDeliveryItemId(Long.parseLong(params.get("deliveryId").toString()));
        return deliveryMap;
    }

    /**
     * 查询出该送货单下相同的物料
     * @param params
     * @return
     */
    @Override
    public List<HashMap<String, Object>> findByDeIdAndGoodsErpCode(Map<String, Object> params) {
        return getBaseMapper().findByDeIdAndGoodsErpCode(params);
    }

    /**
     * 查询出该送货单下相同的物料指定订单行物料
     * @param deId
     * @param saleNo
     * @param saleSeq
     * @return
     */
    @Override
    public List<DeliveryItemEntity> queryByDeIdList(Long deId,String saleNo,String saleSeq,Long deItemId) {
        QueryWrapper<DeliveryItemEntity> queryWrapper=new QueryWrapper<>();
       if(!StrUtil.isEmptyIfStr(deId)){
           queryWrapper.eq("de_id", deId);
       }
       if(!StrUtil.isEmptyIfStr(saleNo)){
    queryWrapper.eq("sale_no", saleNo);
       }
       if(!StrUtil.isEmptyIfStr(saleSeq)){
  queryWrapper.eq("sale_seq",saleSeq);
       }
       if(!StrUtil.isEmptyIfStr(deItemId)){
           queryWrapper.eq("id",deItemId);
       }
        return this.list(queryWrapper);
    }
    @Override
    public List<DeliveryItemEntity> queryByList(Long deId) {
        return this.list(  new QueryWrapper<DeliveryItemEntity>().eq("de_id",deId).lambda().eq(DeliveryItemEntity::getDeleteFlag,WhetherEnum.NO.getCode()));
    }
    @Override
    public List<DeliveryItemEntity> queryByPurId(Long purId) {
        return  this.list(new QueryWrapper<DeliveryItemEntity>()
                .eq("sale_id", purId));
    }

    @Override
    public List<DeliveryItemVO> queryBySaleItemId(Long purId, Long saleItemId) {
        QueryWrapper<DeliveryItemEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("sale_id",purId);
        queryWrapper.eq("sale_item_id",saleItemId);
        List<DeliveryItemEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return  BeanConverter.convertList(list, DeliveryItemVO.class);
        }else {
            return null;
        }
    }

    /**
     * 查询供应商送货列表
     * @param page
     * @param params
     * @return
     */
    @Override
    public List<HashMap<String, Object>> getDeliveryVendorList(IPage<HashMap<String, Object>> page, Map<String, Object> params) {
        try {
            if (params.get("deliveryDate") != null && !params.get("deliveryDate").equals("")) {
                String[] split = params.get("deliveryDate").toString().split(" 至 ");
                params.put("sdeliveryDate", split[0] + " 00:00:00");
                params.put("edeliveryDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("送货单搜索送货日期有误");
            throw new RRException("送货日期输入有误");
        }

        if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }
        List<HashMap<String, Object>> list = getBaseMapper().getDeliveryLists(page, params);

        return list;
    }

    /**
     * 供方导出送货单
     * @param params
     */
    @Override
    public List<DeliveryltemSupplierExportVO> exportVendorList(Map<String, Object> params) {
        if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }
        List<HashMap<String,Object>> list;
        if(!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType")+"")){
        }else{
            params.put("page",1);
            params.put("limit",Long.MAX_VALUE);
        }

        list = this.getDeliveryListPage(params).getList();
        //创建一个采购方质检报告单导出类的集合
        List<DeliveryltemSupplierExportVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)){
            //假如list判断为空
            resultList = BeanConverter.convertList(list,DeliveryltemSupplierExportVO.class);
        }
        return resultList;
    }

    //获取系统参数ifQuality
    @Override
    public String getSysParam(String sysParamName) {
        String ifQuality = sysClient.getValueByKeyAndTenantId(sysParamName, commonService.getTenantId());
        return ifQuality;
    }

    @Override
    public List<DeliveryItemEntity> queryByPlanId(Long planLineId, Long planId) {
        QueryWrapper<DeliveryItemEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("plan_line_id",planLineId);
        queryWrapper.eq("plan_id",planId);
        return this.list(queryWrapper);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean detailDeletion(Long id) {
        DeliveryItemEntity deliveryItemEntity = this.getById(id);
        DeliveryEntity deliveryEntity = deliveryService.getById(deliveryItemEntity.getDeId());
        List<DeliveryItemEntity> list = this.queryByList(deliveryEntity.getId());
        if(list.size()==1){
            throw new RRException("当前送货物单明细已是最后一条明细,请将整单删除！");
        }
        if (!StrUtil.isBlankIfStr(deliveryItemEntity.getPlanSrmLineId())){//判断送货计划明细表id不为空时，需要修改送货计划的已制单数量
            DeliveryPlanItemEntity deliveryPlanItemEntity = deliveryPlanItemService.getById(deliveryItemEntity.getPlanSrmLineId());
            BigDecimal primaryMakeNum = deliveryPlanItemEntity.getMakeNum();//获取送货计划明细的原已制单数
            deliveryPlanItemEntity.setMakeNum(primaryMakeNum.subtract(deliveryItemEntity.getDevNum()));//删除送货单后送货计划明细的已制单数 = 原送货计划明细已制单数-送货单送货数量
            deliveryPlanItemEntity.setDeleteFlag(0);
            deliveryPlanItemService.updateById(deliveryPlanItemEntity);
        }
//            删除时回写采购订单中相对应的数量，如：可制单数量等
        orderClient.updateOrderItemMakeNum(
                deliveryItemEntity.getSaleItemId(),
                deliveryItemEntity.getDevNum(),deliveryEntity.getDeStat()
        );
        this.remove(new QueryWrapper<DeliveryItemEntity>().eq("id",id));
        JSONObject jsonObject =
                deliveryService.saveOperMsg(
                        OperBillEnum.DELIVRY.getValue(),
                        OperTypeEnum.DELETE.getValue(),
                        deliveryEntity.getId(),
                        "删除送货单明细" + deliveryEntity.getDeNo()+deliveryItemEntity.getGoodsErpCode(),
                        deliveryEntity.getDeNo());
        int i = Integer.valueOf(jsonObject.get("code") + "");
        if (i == 500) {
            throw new RRException(String.format("[%s] " + jsonObject.get("msg"), deliveryEntity.getDeNo()));
        }
        return true;
    }

    @Override
    public HashMap<String, Object> getDeliveryListCount(Map<String, Object> params){

        HashMap<String,Object> resultMap = new HashMap<>();

        try {
            if (params.get("deliveryDate") != null && !params.get("deliveryDate").equals("")) {
                String[] split = params.get("deliveryDate").toString().split(" 至 ");
                params.put("sdeliveryDate", split[0] + " 00:00:00");
                params.put("edeliveryDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("送货单搜索送货日期有误");
            throw new RRException("送货日期输入有误");
        }

        if(params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))){//采购方进入送货列表
            params.remove("tenantIds");
            params.put("tenantIds",commonService.getTenantId());
        }else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))){//供应商进入送货列表
            params.remove("vendorIds");
            params.put("vendorIds",commonService.getTenantId());
        }

        for (int i = 0; i <= 8; i++) {
            params.put("whereType",i);
            resultMap.put("count"+i,getBaseMapper().getDeliveryListCount(params));
        }


        return resultMap;
    }

    /**
     * 更改送货单明细暂收数量/暂收时间
     * @param id
     * @param num
     * @param deleteFlag
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateTemNum(Long id, BigDecimal num,Integer deleteFlag) {
        DeliveryItemEntity deliveryItem = this.getById(id);
        BigDecimal temNum = deliveryItem.getTemNum() == null? BigDecimal.ZERO:deliveryItem.getTemNum();
        if (num.compareTo(BigDecimal.ZERO) == 0){
            if (WhetherEnum.NO.getCode().equals(deleteFlag)){
                deliveryItem.setTemNum(deliveryItem.getDevNum().negate());
            }
        } else {
            // 删除时将数量取反进行扣减，否则就增加
            num = WhetherEnum.YES.getCode().equals(deleteFlag) ? num.negate() : num;
            deliveryItem.setTemNum(temNum.add(num));
            deliveryItem.setTemDate(WhetherEnum.YES.getCode().equals(deleteFlag) ? new Date() : null);
        }
        if (deliveryItem.getDevNum().compareTo(num) != 0 && WhetherEnum.NO.getCode().equals(deleteFlag)){
            BigDecimal needSubFixNum = deliveryItem.getDevNum().subtract(num);
            orderClient.updateOrderItemMakeNum(deliveryItem.getSaleItemId(), needSubFixNum.negate());
            orderClient.updateFixNum(deliveryItem.getSaleItemId(), needSubFixNum.negate());
        }
        if (deliveryItem.getDevNum().compareTo(num) != 0 && WhetherEnum.YES.getCode().equals(deleteFlag)){
            BigDecimal needSubFixNum = deliveryItem.getDevNum().subtract(num.abs());
            orderClient.updateOrderItemMakeNum(deliveryItem.getSaleItemId(), needSubFixNum);
            orderClient.updateFixNum(deliveryItem.getSaleItemId(), needSubFixNum);
        }
        this.updateById(deliveryItem);
        return true;
    }

    /**
     * 更改送货单明细行验收入库数和未验收入库数
     * @param id
     * @param num
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateInvNumAndUnInvNum(Long id, BigDecimal num,Integer masterType) {
        DeliveryItemEntity deliveryItem = this.getById(id);
        //验收入库数
        BigDecimal invNum = deliveryItem.getInvNum() == null? BigDecimal.ZERO:deliveryItem.getInvNum();
        //未验收入库数
        BigDecimal unInvNum = deliveryItem.getUnInvNum() == null? BigDecimal.ZERO:deliveryItem.getUnInvNum();
        //收货类型为收货单
        if (MasterTypeEnum.REC.getValue().equals(masterType)){
            //验收入库数 = 当前验收入库数+入库数量
            deliveryItem.setInvNum(invNum.add(num));
            //未验收入库数 = 当前验收入库数-入库数量
            deliveryItem.setUnInvNum(unInvNum.subtract(num));
        }
        //收货类型为退货单
        if (MasterTypeEnum.RET.getValue().equals(masterType)){
            //验收入库数 = 当前验收入库数-入库数量
            deliveryItem.setInvNum(invNum.subtract(num));
        }
        this.updateById(deliveryItem);
        return false;
    }

    /**
     * 根据主表id获取送货单明细行数量总和
     * @param deId
     * @return
     */
    @Override
    public BigDecimal getDeliveryTotalNumByDeId(Long deId) {
        BigDecimal devTotalNum = this.getBaseMapper().getDeliveryTotalNumByDeId(deId);
        return devTotalNum;
    }

    /**
     * 采购方关闭供应商整单送货单明细数据
     * @param deId
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean closeDeliveryItemByTenant(Long deId,Integer deliveryStat){
        DeliveryEntity delivery = deliveryService.getById(deId);
        List<DeliveryItemEntity> deliveryItemList = this.queryByList(deId);
        if (CollectionUtil.isNotEmpty(deliveryItemList)){
            for (DeliveryItemEntity item:deliveryItemList) {
                if (item.getTemNum() != null && item.getTemNum().compareTo(BigDecimal.ZERO) > 0){
                    throw new RRException(String.format("当前送货单已被仓库暂收处理过，无法关闭"));
                }
                item.setDeleteFlag(WhetherEnum.YES.getCode());
                if (!"期初数据".equals(delivery.getRemark())){
                    //回写扣减对应送货计划的已制单数量
                    deliveryPlanItemService.updateByMakeNum(item.getPlanSrmLineId(),item.getDevNum());
                }
                // 减少采购订单行的已制送货单数量make_num
                orderClient.updateOrderItemMakeNum(item.getSaleItemId(),item.getDevNum(),deliveryStat);
                this.updateById(item);
            }
        }
        return true;
    }

    /**
     * 根据明细行id关闭送货单明细行数据
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean closeDeliveryItemById(Long id) {
        DeliveryItemEntity deliveryItem = this.getById(id);
        return true;
    }

    /**
     * 查询送货单主从表信息
     * @param params
     * @return
     */
    @Override
    public List<DeliverySlaveVO> findDeliverySlaveList(Map<String, Object> params) {
        List<DeliverySlaveVO> deliverySlaveList = this.getBaseMapper().findDeliverySlaveList(params);
        return deliverySlaveList;
    }

    /**
     * 根据WMS提供的收货(退货)信息更改送货单信息
     * @param masterItem
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateDeliveryItemByWms(MasterItemEntity masterItem) {
        DeliveryItemEntity deliveryItem = this.getById(masterItem.getDeItemId());
        if(Objects.isNull(deliveryItem)){
            throw new RRException(String.format("查找不到对应的送货单信息%s-%s",masterItem.getDeNo(),masterItem.getDeSeq()));
        }
        //验收入库数
        BigDecimal invNum = deliveryItem.getInvNum() == null? BigDecimal.ZERO:deliveryItem.getInvNum();
        //未验收入库数
        BigDecimal unInvNum = deliveryItem.getUnInvNum() == null? BigDecimal.ZERO:deliveryItem.getUnInvNum();
        if (masterItem.getMasterNum().compareTo(BigDecimal.ZERO) == 0){
            deliveryItem.setInvNum(deliveryItem.getDevNum().negate());
            deliveryItem.setUnInvNum(BigDecimal.ZERO);
        } else {
            deliveryItem.setInvNum(invNum.add(masterItem.getMasterNum()));
            deliveryItem.setUnInvNum(deliveryItem.getDevNum().subtract(masterItem.getMasterNum()));
        }
//        if (Objects.isNull(deliveryItem.getArrivalDate())){
//            deliveryItem.setArrivalDate(new Date());
//        }
        // 入库数量小于送货数量
        if (masterItem.getMasterNum().compareTo(deliveryItem.getDevNum()) < 0){
            BigDecimal updateOrderNum = deliveryItem.getDevNum().subtract(masterItem.getMasterNum());
            // 入库数量为0 需要回写送货行原本的送货数 送货500-入库0
            if (masterItem.getMasterNum().compareTo(BigDecimal.ZERO) == 0){
                // 回写送货计划明细行已制单数量
                deliveryPlanItemService.updateMakeAndHistoryNum(deliveryItem.getPlanSrmLineId(),updateOrderNum,WhetherEnum.YES.getCode(),masterItem.getMasterNum());
            } else {
                // 回写送货计划明细行已制单数量
                deliveryPlanItemService.updateMakeAndHistoryNum(deliveryItem.getPlanSrmLineId(),masterItem.getMasterNum(),WhetherEnum.YES.getCode(),deliveryItem.getDevNum());
            }
            // 回写采购订单已制单数量
            orderClient.updateOrderItemMakeNum(deliveryItem.getSaleItemId(),updateOrderNum,2);
//            orderClient.updateMatchedPlanNum(deliveryItem.getSaleItemId(),updateOrderNum.negate());
            DeliveryEntity delivery = deliveryService.getById(deliveryItem.getDeId());
            // 调用回传SAP在途数量接口
            uploadSapInTransitQuantity(delivery,deliveryItem,updateOrderNum,2);
        } else {
            // 回写送货计划明细行已制单数量
            deliveryPlanItemService.updateMakeAndHistoryNum(deliveryItem.getPlanSrmLineId(),masterItem.getMasterNum(),WhetherEnum.NO.getCode(),null);
        }
        this.updateById(deliveryItem);
        return true;
    }

    /**
     * 根据MES提供的收货(退货)信息更改送货单信息
     * @param masterItem
     * @param masterType
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean updateDeliveryItemByMes(MasterItemEntity masterItem,Integer masterType) {
        DeliveryItemEntity deliveryItem = this.getById(masterItem.getDeItemId());
        if(Objects.isNull(deliveryItem)){
            throw new RRException(String.format("查找不到对应的送货单信息%s-%s",masterItem.getDeNo(),masterItem.getDeSeq()));
        }
        //验收入库数
        BigDecimal invNum = deliveryItem.getInvNum() == null? BigDecimal.ZERO:deliveryItem.getInvNum();
        BigDecimal retQty = deliveryItem.getRetQty() == null? BigDecimal.ZERO:deliveryItem.getRetQty();
        invNum = invNum.add(masterItem.getMasterNum());
        if (MasterTypeEnum.RET.getValue().equals(masterType)) {
            retQty = retQty.add(masterItem.getMasterNum().abs());
        }
        deliveryItem.setInvNum(invNum);
        if (invNum.compareTo(BigDecimal.ZERO) < 0){
            deliveryItem.setInvNum(BigDecimal.ZERO);
        }
        deliveryItem.setRetQty(retQty);
        deliveryItem.setUnInvNum(deliveryItem.getDevNum().subtract(masterItem.getMasterNum()));
        this.updateById(deliveryItem);
        // 回写送货计划明细行已制单数量
        deliveryPlanItemService.updateMakeAndHistoryNum(deliveryItem.getPlanSrmLineId(),masterItem.getMasterNum(),masterType);
        return true;
    }

    @Override
    public List<InTransitQuantityInfoVO> getSumInTransitQuantity(InTransitQuantityParamVO inTransitQuantityParamVO) {
        return getBaseMapper().getSumInTransitQuantity(inTransitQuantityParamVO);
    }

    @Override
    public List<Long> getInTransitQuantityIds(InTransitQuantityParamVO inTransitQuantityParamVO) {
        return getBaseMapper().getInTransitQuantityIds(inTransitQuantityParamVO);
    }

    /**
     * 导入期初在途送货单明细数据
     * @param delivery
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean importInTransitDeliveryItem(DeliveryEntity delivery, List<InTransitDeliveryImportVO> list) {
        int idxSeq = 0;
        for (InTransitDeliveryImportVO data:list) {
            idxSeq++;
            DeliveryItemEntity deliveryItem = BeanConverter.convert(data, DeliveryItemEntity.class);
            deliveryItem.setDeId(delivery.getId());
            deliveryItem.setTenantId(delivery.getTenantId());
            deliveryItem.setTenantPId(delivery.getTenantPId());
            deliveryItem.setSeq(idxSeq+"");
            deliveryItem.setInvNum(BigDecimal.ZERO);
            deliveryItem.setTemNum(BigDecimal.ZERO);
            deliveryItem.setUnNum(BigDecimal.ZERO);
            deliveryItem.setUnInvNum(BigDecimal.ZERO);
            deliveryItem.setDeliveryDate(delivery.getDeliveryDate());
            deliveryItem.setReserved06(data.getErpOrderNo());
            deliveryItem.setReserved07(data.getErpOrderSeq());
            deliveryItem.setReserved08(data.getErpGoodsCode());
            deliveryItem.setReserved09(data.getOriginalDeNo());
            orderClient.updateInTransitDeliveryNum(deliveryItem.getSaleItemId(),deliveryItem.getDevNum());
            this.save(deliveryItem);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据MES送货单检验不合格数据进行退货
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean unQuaInspectionReturnByMes(List<DeliveryItemVO> list) {
        for (DeliveryItemVO deliveryItemVO : list) {
            DeliveryEntity delivery = deliveryService.getById(deliveryItemVO.getDeId());
            DeliveryItemEntity deliveryItem = this.getById(deliveryItemVO.getId());
            deliveryItem.setUnCompetentNum(deliveryItemVO.getUnCompetentNum());
            deliveryItem.setReserved10(deliveryItemVO.getReserved10());
            this.updateById(deliveryItem);
            // 回写送货计划明细行已制单数量、不合格数
            deliveryPlanItemService.updateUnCompetentNumById(deliveryItem.getPlanSrmLineId(),deliveryItemVO.getUnCompetentNum());
            // 回写采购订单不合格数量
            orderClient.updateUnCompetentNumById(deliveryItem.getSaleItemId(),deliveryItemVO.getUnCompetentNum());
            // 调用回传SAP在途数量接口
            uploadSapInTransitQuantity(delivery,deliveryItem,deliveryItemVO.getUnCompetentNum(),2);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据id查询送货单明细行信息(多个ID)
     * @param ids
     * @return
     */
    @Override
    public Map<Long, DeliveryItemVO>  findDeliveryItemByIds(List<Long> ids) {
        List<DeliveryItemVO> list = new ArrayList<>();
        // 每次批量查询的送货单明细数量
        int batchSize = 10;
        // 每次批量查询的送货单明细信息
        for (int i = 0; i < ids.size(); i += batchSize) {
            int end = Math.min(i + batchSize, ids.size());
            List<Long> subList = ids.subList(i, end);
            List<DeliveryItemEntity> result = this.list(new LambdaQueryWrapper<DeliveryItemEntity>().in(DeliveryItemEntity::getId, subList));
            // 合并添加供应商信息
            if (CollectionUtil.isNotEmpty(result)) {
                list.addAll(BeanConverter.convertList(result, DeliveryItemVO.class));
            }
        }
        if (CollectionUtil.isNotEmpty(list)){
            Map<Long, DeliveryItemVO> map = list.stream()
                    .filter(vo -> ObjectUtil.isNotEmpty(vo.getId()))
                    .collect(Collectors.toMap(
                            DeliveryItemVO::getId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            return map;
        }
        return null;
    }

    @Override
    public List<PurQuaExportVO> purQuaExportList(Map<String, Object> params) {
        CommonService commonService= (CommonService) SpringContextUtils.getBean("commonBizService");
        commonService.setTaskUser(Long.parseLong(params.get("tenantId").toString()));
        params.put("tenantId", commonService.getTenantId());
        List<Map<String,Object>> orderAndDeliveryList;
        Map<String, Object> map=new HashMap<>();//导出全部参数

        if(!StrUtil.isBlankIfStr(params.get("exportType")) && Integer.parseInt(params.get("exportType").toString()) != 1){
            orderAndDeliveryList=findByDeNo(params);//接收数据
        }else{
            orderAndDeliveryList=findByDeNo(map);//接收数据
        }

        List<PurQuaExportVO> resultList = new ArrayList<>();//导出数据实体类

        if(CollectionUtils.isNotEmpty(orderAndDeliveryList)) {//转换
            resultList = BeanConverter.convertList(orderAndDeliveryList, PurQuaExportVO.class);
        }
        return resultList;
    }

    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        DeliveryItemEntity deliveryItemEntity =this.getById(id);
        //if(deliveryItemEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("已审核的销售订单禁止修改"));
        //}
    }
    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkCheck(DeliveryItemEntity deliveryItemEntity) {
        //if(deliveryItemEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("[%s] 此销售单已审核"));
        //}
    }

    /**
     * 删除状态校验
     *
     * @param ids
     */
    private void deleteCheck(Long[] ids) {
        for (Long id:ids) {
                DeliveryItemEntity deliveryItemEntity = this.getById(id);
        }
        //if(!saleOrderHeadEntity.getOrderState().equals(OrderHead_OrderStateEnum.WAITCHECK.getValue())){
        //    throw new RRException(String.format("只允许删除未审核的销售订单"));
        //}
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(DeliveryItemEntity record, Class<?> cls) {
        ValidatorUtils.validateEntity(record, cls);
    }
    /**
     * 获取查询条件
     *
     * @param
     */
    private QueryWrapper<DeliveryItemEntity> getQueryWrapper(Map<String, Object> params) {
        QueryWrapper<DeliveryItemEntity> queryWrapper=new QueryWrapper<>();
        if(!StrUtil.isEmptyIfStr(params.get("propKey"))){

        }

        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }

    @Override
    public List<HashMap<String,Object>> findDeliveryAndSaleList(Map<String, Object> params){
        return getBaseMapper().findDeliveryAndSaleOrderMap(params);
    }

    /**
     * 获取送货单打印明细
     * @param params
     * @return
     */
    @Override
    public List<HashMap<String,Object>> getPrintInfo(Map<String, Object> params){
        return getBaseMapper().getPrintInfo(params);
    }

    public String updateQuaNumToMes(DeliveryEntity deliveryEntity){
        return null;
    }

    /**
     * SRM->SAP 供应商送货在途数据同步接口
     * @return
     */
    private SapInTransitQuantityReturnInfoVO uploadSapInTransitQuantity(DeliveryEntity delivery,DeliveryItemEntity deliveryItem,BigDecimal num, Integer operType){
        List<SapInTransitQuantityVO> inTransitQuantityVOList = new ArrayList<>();
        SapInTransitQuantityVO sapInTransitQuantityVO = new SapInTransitQuantityVO();
        sapInTransitQuantityVO.setLIFNR(delivery.getVendorCode());
        sapInTransitQuantityVO.setWERKS(delivery.getDeptCode());
        sapInTransitQuantityVO.setMATNR(deliveryItem.getGoodsErpCode());
        if (operType.equals(1)){
            sapInTransitQuantityVO.setSCMNG(num.toString());
        } else {
            sapInTransitQuantityVO.setSCMNG(num.negate().toString());
        }
        // 查询出对应的送货计划明细行
        DeliveryPlanItemEntity deliveryPlanItem = deliveryPlanItemService.getById(deliveryItem.getPlanSrmLineId());
        if (Objects.nonNull(deliveryPlanItem)){
            sapInTransitQuantityVO.setZTDATE(DateUtil.format(deliveryPlanItem.getPlanDate(),"yyyyMMdd"));
            sapInTransitQuantityVO.setBERID(deliveryPlanItem.getMrpRegion());
        } else {
            sapInTransitQuantityVO.setZTDATE(DateUtil.format(delivery.getAuditTime(),"yyyyMMdd"));
        }
        sapInTransitQuantityVO.setVDATU(DateUtil.format(new Date(),"yyyyMMdd"));
        inTransitQuantityVOList.add(sapInTransitQuantityVO);
        String sapLinkUrl = sysClient.getValueByKeyAndTenantId("SAPLinkUrl", commonService.getTenantId());
        // SAP的环境
        String sapClient = sysClient.getValueByKeyAndTenantId("sap-client", commonService.getTenantId());
        if (StrUtil.isEmptyIfStr(sapLinkUrl)){
                throw new RRException("没有维护SAP接口访问IP,请求接口失败,请联系IT进行维护数据");
        }
        sapLinkUrl = sapLinkUrl + "/zpp009_http?sap-client="+sapClient;
        String jsonData = JSON.toJSONString(inTransitQuantityVOList);
        logger.info("SRM调用SAP在途数量接口传参="+jsonData);
        String result = sapApiService.sendSap(sapLinkUrl, jsonData, "SRM调用SAP在途数量接口", commonService.getTenantId());
        if (StrUtil.isEmptyIfStr(result)){
            throw new RRException(String.format("SRM调用SAP在途数量接口,无返回信息数据返回,请求失败"));
        }
        logger.info("接口返回数据===================》"+result);
        return null;
    }
}
