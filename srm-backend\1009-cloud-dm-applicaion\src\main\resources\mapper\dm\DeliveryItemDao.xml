<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dian.modules.dm.dao.DeliveryItemDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dian.modules.dm.entity.DeliveryItemEntity" id="deliveryItemMap">
            <result property="tenantPId" column="tenant_p_id"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="deId" column="de_id"/>
            <result property="saleId" column="sale_id"/>
            <result property="saleNo" column="sale_no"/>
            <result property="saleNo" column="sale_no"/>
            <result property="saleItemId" column="sale_item_id"/>
            <result property="id" column="id"/>
            <result property="goodsId" column="goods_id"/>
            <result property="goodsErpCode" column="goods_erp_code"/>
            <result property="goodsCode" column="goods_code"/>
            <result property="goodsName" column="goods_name"/>
            <result property="goodsModel" column="goods_model"/>
            <result property="uomId" column="uom_id"/>
            <result property="uomName" column="uom_name"/>
            <result property="rateId" column="rate_id"/>
            <result property="rateName" column="rate_name"/>
            <result property="rateVal" column="rate_val"/>
            <result property="currencyId" column="currency_id"/>
            <result property="currencyName" column="currency_name"/>
            <result property="taxesType" column="taxes_type"/>
            <result property="invoiceType" column="invoice_type"/>
            <result property="gstPrice" column="gst_price"/>
            <result property="taxPrice" column="tax_price"/>
            <result property="deliveryDate" column="delivery_date"/>
            <result property="temDate" column="tem_date"/>
            <result property="devNum" column="dev_num"/>
            <result property="temNum" column="tem_num"/>
            <result property="unNum" column="un_num"/>
            <result property="invNum" column="inv_num"/>
            <result property="unInvNum" column="un_inv_num"/>
            <result property="orderNum" column="order_num"/>
            <result property="barcodeType" column="barcode_type"/>
            <result property="docName" column="doc_name"/>
            <result property="docUrl" column="doc_url"/>
            <result property="isPrint" column="is_print"/>
            <result property="printCount" column="print_count"/>
            <result property="remark" column="remark"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="versionNum" column="version_num"/>
            <result property="seq" column="seq"/>
            <result property="bigPackStandardNum" column="big_pack_standard_num"/>
            <result property="smallPackStandardNum" column="small_pack_standard_num"/>
            <result property="bigPackLabelNum" column="big_pack_label_num"/>
            <result property="smallPackLabelNum" column="small_pack_label_num"/>
            <result property="bigPackMantissa" column="big_pack_mantissa"/>
            <result property="smallPackMantissa" column="small_pack_mantissa"/>
            <result property="purchaseConfirm" column="purchase_confirm"/>
            <result property="vendorDocUrl" column="vendor_doc_url"/>
            <result property="vendorDocName" column="vendor_doc_name"/>
            <result property="gstAmount" column="gst_amount"/>
            <result property="taxAmount" column="tax_amount"/>
            <result property="soureNo" column="soure_no"/>
            <result property="purEmployeeName" column="pur_employee_name"/>
            <result property="saleEmployeeName" column="sale_employee_name"/>
            <result property="goodsClassName" column="goods_class_name"/>
            <result property="warehouseCode" column="warehouse_code"/>
            <result property="warehouseName" column="warehouse_name"/>
            <result property="orderType" column="order_type"/>
    </resultMap>

        <!--获取送货列表分页_meng-->
        <select id="getDeliveryLists" resultType="java.util.HashMap" parameterType="java.util.HashMap">
            SELECT
            dd.id, /*送货单Id*/
            dd.de_no deNo,/*送货单号*/
            dd.tenant_id deliveryTenantId, /*送货主表采购方id-----(meng)*/
            dd.vendor_id deliveryVendorId, /*送货主表供应商id-----(meng)*/
            ddi.seq,/*序号*/
            ddi.id deItemId,
            dd.delivery_date deDate,/*送货日期*/
            dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
            ddi.sale_id,/*采购订单id*/
            ddi.sale_item_id,/*采购订单行id*/
            ddi.sale_no,/*采购订单号*/
            ddi.sale_seq purSeq,/*采购订单序号*/
            dd.vendor_code vendorCode,/*供应商编码*/
            dd.vendor_name vendorName,/*供应商名称*/
            ddi.goods_id goodsId,/*物料id*/
            ddi.goods_code goodsCode,/*品号*/
            ddi.goods_erp_code goodsErpCode,/*erp*/
            ddi.goods_name goodsName,/*品名*/
            ddi.goods_model goodsModel,/*物料描述*/
            ddi.goods_class_code,/*物料分类编码*/
            ddi.goods_class_name,/*物料分类名称*/
            ddi.order_num,/*订单数量*/
            ddi.uom_id uomId,/*计量单位ID*/
            ddi.uom_name uomName,/*计量单位名称*/
            ddi.uom_num,/*计量单位名称*/
            ddi.aux_uom_id,
            ddi.aux_uom_code,
            ddi.aux_uom_name,
            ddi.rate_id rateId,/*税率ID*/
            ddi.rate_val rateVal,/*税率值*/
            ddi.currency_id currencyId,/*币别id*/
            ddi.currency_name currencyName,/*币别名称*/
            ddi.taxes_type taxesType,/*税种类型*/
            ddi.invoice_type invoiceType,/*发票类型*/
            ddi.gst_price gstPrice,/*含税单价*/
            ddi.tax_price taxPrice,/*不含税单价*/
            ddi.tem_date temDate,/*暂收日期*/
            ddi.dev_num devNum,/*已送货数量*/
            ddi.tem_num temNum,/*暂收数量*/
            ddi.un_num unNum,/*未收数量*/
            ddi.inv_num invNum,/*验收已入库数量*/
            ddi.un_inv_num unInvNum,/*未验收数量*/
            ddi.order_num orderNum, /*测试字段 - 总数量*/
            dd.see see,/*是否已查看:0-未查看,1-已查看-----(meng)*/
            ddi.vendor_doc_url vendorDocUrl,/*供货方文档URL(meng)-----(meng)*/
            ddi.delivery_date deliveryDate,/*交货日期*/
            dd.delivery_date reality_delivery_date,
            ddi.reply_date replyDate,/*答交日期*/
            ddi.rate_name rateName,/*税别*/
            ddi.drawing_no,
            ddi.pur_employee_name purEmployee,/*采购方业务*/
            ddi.sale_employee_name saleEmployee,/*销售方业务*/
            ddi.barcode_type barcodeType,/*条码控制*/
            dd.tenant_name tenantName,/*客户名称*/
            ddi.warehouse_name warehouseName, /*仓库名称*/
            dd.is_compromise isCompromise,/*是否让步接收 0否 1是*/
            dd.is_compromise_create isComproiseCreate,/*判断是否创建让步接收单：0_未创建，1_已创建*/
            ddi.is_compromise itemIsCompromise,/*送货单明细行让步接收标识：0_否 ，1_是*/
            ddi.is_compromise_create itemIsCompromiseCreate,/*是否创建让步接收单：0_未创建，1_已创建*/
            ddi.compromise_num compromiseNum,/*让步接收数量*/
            ddi.ret_num retNum,/*暂退数量*/
            ddi.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
            ddi.qua_sheet_stat quaSheetStat, /*质检报告单质检状态 0_还有未质检的质检报告 1_全部已质检*/
            ddi.qua_check_stat quaCheckStat, /*抽检状态 1_合格，2_不合格*/
            ddi.submit_stat submitStat, /*是否有提交到MES 0_未提交 1_已提交*/
            dd.create_date createDate,/*送货单单据创建日期*/
            dd.is_print isPrint,/*送货单打印状态*/
            dd.print_count printCount,/*送货单打印次数*/
            ddi.plan_date,
            dd.dept_id,
            dd.dept_code,
            dd.dept_name,
            dd.audit_time,
            ddi.remark as line_remark
            FROM
             dm_delivery dd,dm_delivery_item ddi
             <where>
                 and dd.id=ddi.de_id and dd.delete_flag = 0
                 and ddi.delete_flag = 0
                 <if test="params.sdeliveryDate != null and params.sdeliveryDate != '' ">
                     <![CDATA[ and dd.create_date >= STR_TO_DATE(#{params.sdeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
                 </if>
                 <if test="params.edeliveryDate != null and params.edeliveryDate != '' ">
                     <![CDATA[ and dd.create_date <= STR_TO_DATE(#{params.edeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
                 </if>
                  /*采购方条件查询*/
                 <if test="params.tenantIds != null and params.tenantIds != '' ">
                     and dd.tenant_id=#{params.tenantIds}
--                      and dd.de_stat&gt;1
                     <if test="params.keyword != null and params.keyword != '' ">
                         and (
                         ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                         or dd.vendor_name like CONCAT('%',#{params.keyword},'%')
                         or dd.vendor_code like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                         or dd.de_no like CONCAT('%',#{params.keyword},'%')
                         or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                     </if>
                 </if>
                 /*供应商条件查询*/
                 <if test="params.vendorIds != null and params.vendorIds != '' ">
                     and dd.vendor_id=#{params.vendorIds}
                     <if test="params.keyword != null and params.keyword != '' ">
                         and (ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                         or dd.tenant_name like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                         or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                         or dd.de_no like CONCAT('%',#{params.keyword},'%')
                         or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                     </if>
                 </if>
                 <if test="params.deptId != null and params.deptId != ''">
                     AND dd.dept_id = #{params.deptId}
                 </if>
                 <if test="params.deNo != null and params.deNo != ''">
                     AND dd.de_no like CONCAT('%',#{params.deNo},'%')
                 </if>
                 <if test="params.orderNo != null and params.orderNo != ''">
                     AND ddi.sale_no like CONCAT('%',#{params.orderNo},'%')
                 </if>
                 <if test="params.dept != null and params.dept != ''">
                     AND (
                     dd.dept_code like CONCAT('%',#{params.dept},'%') or dd.dept_name like CONCAT('%',#{params.dept},'%')
                     )
                 </if>
                 <if test="params.vendor != null and params.vendor != ''">
                     AND (
                     dd.vendor_code like CONCAT('%',#{params.vendor},'%') or dd.vendor_name like CONCAT('%',#{params.vendor},'%')
                     )
                 </if>
                 <if test="params.goods != null and params.goods != ''">
                     AND (
                     ddi.goods_code like CONCAT('%',#{params.goods},'%')
                     or ddi.goods_erp_code like CONCAT('%',#{params.goods},'%')
                     or ddi.goods_name like CONCAT('%',#{params.goods},'%')
                     or ddi.goods_model like CONCAT('%',#{params.goods},'%')
                     )
                 </if>
                 <if test="params.deStat != null and params.deStat != '' ">
                     AND dd.de_stat =#{params.deStat}
                 </if>
                 <if test="params.lineRemark != null and params.lineRemark != '' ">
                     AND ddi.remark LIKE CONCAT('%',#{params.lineRemark},'%')
                 </if>
                 <if test="params.whereType != null and params.whereType != ''">
                     <choose>
                         <!-- 全部 -->
                         <when test="params.whereType == 1 or params.whereType == '1'"></when>
                         <!-- 已送未收 -->
                         <when test="params.whereType == 2 or params.whereType == '2'">
                             and dd.de_stat = 2
                             and ddi.inv_num = 0
                             and ddi.un_competent_num = 0
                         </when>
                         <!-- 待发出 -->
                         <when test="params.whereType == 3 or params.whereType == '3'">
                             and dd.de_stat = 1
                         </when>
                         <!-- 暂收 -->
                         <when test="params.whereType == 4 or params.whereType == '4'">
                             and ddi.tem_num&gt; 0
                         </when>
                         <!-- 暂退 -->
                         <when test="params.whereType == 5 or params.whereType == '5'">
                             and ddi.ret_num &gt; 0
                         </when>
                         <!-- 待检区 -->
                         <when test="params.whereType == 6 or params.whereType == '6'">
                             and dd.de_stat = 2 <!-- 已发出 -->
                             and ddi.qua_sheet_stat = 0 <!-- qua_sheet_stat = 0 表示还有未质检的送货单明细-->
                         </when>
                         <!-- 不良区 -->
                         <when test="params.whereType == 7 or params.whereType == '7'">
                             <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                             and ddi.is_compromise = 1
                             and ddi.is_compromise_create = 0
                             and ddi.qua_check_stat = 2
                             <!-- 只查询当前送货单明细行的暂收数量大于当前送货单明细行对应让步接收单的汇总数量 -->
                             AND ddi.tem_num > (SELECT IFNULL(SUM(dci.act_comproise_num),0) FROM dm_compromise_item dci WHERE dci.de_item_id = ddi.id)
                         </when>
                         <!-- 不良手工退货区 -->
                         <when test="params.whereType == 8 or params.whereType == '8'">
                             and ddi.is_compromise = 1
                             and ddi.defective_manual_return_type = 1
                         </when>
                     </choose>
                 </if>
             </where>
            ORDER BY dd.id DESC,ddi.seq
        </select>

    <select id="getDeliveryListCount" parameterType="java.util.HashMap" resultType="java.lang.Long">
        SELECT
        count(*) as deliveryCount
        FROM
        dm_delivery dd,dm_delivery_item ddi
        <where>
            and dd.id=ddi.de_id   and dd.delete_flag=0
            and ddi.delete_flag=0
            <if test="params.sdeliveryDate != null and params.sdeliveryDate != '' ">
                <![CDATA[ and dd.create_date >= STR_TO_DATE(#{params.sdeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            <if test="params.edeliveryDate != null and params.edeliveryDate != '' ">
                <![CDATA[ and dd.create_date <= STR_TO_DATE(#{params.edeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            /*采购方条件查询*/
            <if test="params.tenantIds != null and params.tenantIds != '' ">
                and dd.tenant_id=#{params.tenantIds}
                <if test="params.keyword != null and params.keyword != '' ">
                    and (ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                    or dd.vendor_name like CONCAT('%',#{params.keyword},'%')
                    or dd.vendor_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                    or dd.de_no like CONCAT('%',#{params.keyword},'%')
                    or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                </if>
            </if>
            /*供应商条件查询*/
            <if test="params.vendorIds != null and params.vendorIds != '' ">
                and dd.vendor_id=#{params.vendorIds}
                <if test="params.keyword != null and params.keyword != '' ">
                    and (ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                    or dd.tenant_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                    or dd.de_no like CONCAT('%',#{params.keyword},'%')
                    or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                </if>
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                AND dd.dept_id = #{params.deptId}
            </if>
            <if test="params.deNo != null and params.deNo != ''">
                AND dd.de_no like CONCAT('%',#{params.deNo},'%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                AND ddi.sale_no like CONCAT('%',#{params.orderNo},'%')
            </if>
            <if test="params.dept != null and params.dept != ''">
                AND (
                dd.dept_code like CONCAT('%',#{params.dept},'%') or dd.dept_name like CONCAT(#{params.dept},'%')
                )
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                AND (
                dd.vendor_code like CONCAT('%',#{params.vendor},'%') or dd.vendor_name like CONCAT(#{params.vendor},'%')
                )
            </if>
            <if test="params.goods != null and params.goods != ''">
                AND (
                ddi.goods_code like CONCAT('%',#{params.goods},'%')
                or ddi.goods_erp_code like CONCAT('%',#{params.goods},'%')
                or ddi.goods_name like CONCAT('%',#{params.goods},'%')
                or ddi.goods_model like CONCAT('%',#{params.goods},'%')
                )
            </if>
            <if test="params.deStat != null and params.deStat != '' ">
                AND dd.de_stat =#{params.deStat}
            </if>
            <if test="params.pur != null and params.pur != '' ">
                and dd.de_stat = 2
                and inv_num = 0
            </if>
            <if test="params.deStat != null and params.deStat != '' ">
                and dd.de_stat = 1
            </if>
            <if test="params.temNum != null and params.temNum != '' ">
                and ddi.tem_num &gt; 0
            </if>
            <if test="params.unsNum != null and params.unsNum != '' ">
                and ddi.ret_num &gt; 0
            </if>
            <if test="params.isCompromise != null and params.isCompromise != ''">
                <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                and ddi.is_compromise = 1
                and ddi.is_compromise_create = 0
                and ddi.qua_check_stat = 2
            </if>
            <if test="params.isQuality != null and params.isQuality != ''">
                and dd.de_stat = 2 <!-- 已发出 -->
                and ddi.qua_sheet_stat = 0 <!-- qua_sheet_stat = 0 表示还有未质检的送货单明细-->
            </if>
            <if test="params.defectiveManualReturn != null and params.defectiveManualReturn != ''">
                <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                and ddi.is_compromise = 1
                and ddi.defective_manual_return_type = 1
            </if>
            <if test="params.whereType != null and params.whereType != ''">
                <choose>
                    <!-- 全部 -->
                    <when test="params.whereType == 1 or params.whereType == '1'"></when>
                    <!-- 已送未收 -->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        and dd.de_stat = 2
                        and inv_num = 0
                        and ddi.un_competent_num = 0
                    </when>
                    <!-- 待发出 -->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and dd.de_stat = 1
                    </when>
                    <!-- 暂收 -->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and ddi.tem_num &gt; 0
                    </when>
                    <!-- 暂退 -->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                        and ddi.ret_num &gt; 0
                    </when>
                    <!-- 待检区 -->
                    <when test="params.whereType == 6 or params.whereType == '6'">
                        and dd.de_stat = 2 <!-- 已发出 -->
                        and ddi.qua_sheet_stat = 0 <!-- qua_sheet_stat = 0 表示还有未质检的送货单明细-->
                    </when>
                    <!-- 不良区 -->
                    <when test="params.whereType == 7 or params.whereType == '7'">
                        <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                        and ddi.is_compromise = 1
                        and ddi.is_compromise_create = 0
                        and ddi.qua_check_stat = 2
                        <!-- 只查询当前送货单明细行的暂收数量大于当前送货单明细行对应让步接收单的汇总数量 -->
                        AND ddi.tem_num > (SELECT IFNULL(SUM(dci.act_comproise_num),0) FROM dm_compromise_item dci WHERE dci.de_item_id = ddi.id)
                    </when>
                    <!-- 不良手工退货区 -->
                    <when test="params.whereType == 8 or params.whereType == '8'">
                        and ddi.is_compromise = 1
                        and ddi.defective_manual_return_type = 1
                    </when>
                </choose>
            </if>
        </where>
        order by dd.id desc ,ddi.seq desc
    </select>


    <select id="getDeliveryCount" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT
        count(*) as deliveryCount
        FROM
        dm_delivery dd,dm_delivery_item ddi
        <where>
            and dd.id=ddi.de_id   and dd.delete_flag=0
            and ddi.delete_flag=0
            <if test="params.sdeliveryDate != null and params.sdeliveryDate != '' ">
                <![CDATA[ and dd.create_date >= STR_TO_DATE(#{params.sdeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            <if test="params.edeliveryDate != null and params.edeliveryDate != '' ">
                <![CDATA[ and dd.create_date <= STR_TO_DATE(#{params.edeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            /*采购方条件查询*/
            <if test="params.tenantIds != null and params.tenantIds != '' ">
                and dd.tenant_id=#{params.tenantIds}
                --                 and dd.de_stat&gt;1
                <if test="params.keyword != null and params.keyword != '' ">
                    and (ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                    or dd.vendor_name like CONCAT('%',#{params.keyword},'%')
                    or dd.vendor_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                    or dd.de_no like CONCAT('%',#{params.keyword},'%')
                    or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                </if>
            </if>
            /*供应商条件查询*/
            <if test="params.vendorIds != null and params.vendorIds != '' ">
                and dd.vendor_id=#{params.vendorIds}
                <if test="params.keyword != null and params.keyword != '' ">
                    and (ddi.sale_no like CONCAT('%',#{params.keyword},'%')
                    or dd.tenant_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_erp_code like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_name like CONCAT('%',#{params.keyword},'%')
                    or ddi.goods_model like CONCAT('%',#{params.keyword},'%')
                    or dd.de_no like CONCAT('%',#{params.keyword},'%')
                    or ddi.drawing_no like CONCAT('%',#{params.keyword},'%'))
                </if>
            </if>
            <if test="params.pur != null and params.pur != '' ">
                and dd.de_stat = 2
                and inv_num = 0
            </if>
            <if test="params.deStat != null and params.deStat != '' ">
                and dd.de_stat = 1
            </if>
            <if test="params.shipped != null and params.shipped != '' ">
                and dd.de_stat = 2
            </if>
            <if test="params.temNum != null and params.temNum != '' ">
                and ddi.tem_num &gt; 0
            </if>
            <if test="params.unsNum != null and params.unsNum != '' ">
                and ddi.ret_num &gt; 0
            </if>
            <if test="params.isCompromise != null and params.isCompromise != ''">
                <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                and ddi.is_compromise = 1
                and ddi.is_compromise_create = 0
                and ddi.qua_check_stat = 2
            </if>
            <if test="params.isQuality != null and params.isQuality != ''">
                and dd.de_stat = 2 <!-- 已发出 -->
                and ddi.qua_sheet_stat = 0 <!-- qua_sheet_stat = 0 表示还有未质检的送货单明细-->
            </if>
            <if test="params.defectiveManualReturn != null and params.defectiveManualReturn != ''">
                <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                and ddi.is_compromise = 1
                and ddi.defective_manual_return_type = 1
            </if>
        </where>
        order by dd.id desc ,ddi.seq desc
    </select>

    <select id="findDeliveryAndSaleOrderMap" parameterType="java.util.HashMap" resultType="java.util.HashMap">
            SELECT
                ordersale.werks,
                ordersale.warehouse_code as warehouseCode,
                delivery.id,
                ordersale.id AS orderSaleId,/*销售订单明细id*/
                ordersale.tenant_id AS orderSaleTenantId,/*销售订单租户id*/
                delivery.sale_seq AS saleSeq,/*销售订单明细序号*/
                ordersale.delivery_date AS deliveryDate,/*销售订单明细订单货期*/
                ordersale.item_stat AS itemStat,/*销售订单明细行状态*/
                ordersale.wait_num AS waitNum,/*销售订单明细待送货量*/
                ordersale.make_num AS makeNum,/*销售订单明细制单数量*/
                ifnull(ordersale.order_num,0) + ifnull(ordersale.refund_num,0) + ifnull(ordersale.erp_reject_num,0) + ifnull(delivery.dev_num,0) - ifnull(ordersale.make_num,0) AS canmakeNum,/*销售订单明细可制单数量*/
                ordersale.delivery_date AS orderLeadTime,/*销售订单明细订单货期*/
                ordersale.reply_date AS replyDate,/*销售订单明细答交日期*/
                ordersale.warehouse_name AS warehouseName,/*销售订单明细收货仓库名称*/
                ordersale.barcode_type AS barcodeType,/*销售订单明细条码类型*/
                ordersale.change_count AS changeCount,/*销售订单明细订单变更次数*/
                ordersale.big_pack_standard_num AS orderSaleBigPackStandardNum,/*大包标准数量*/
                ordersale.small_pack_standard_num AS orderSaleSmallPackStandardNum,/*小包标准数量*/
                ordersale.big_pack_label_num AS orderSaleBigPackLabelNum,/*大包标签数量*/
                ordersale.small_pack_label_num AS orderSaleSmallPackLabelNum,/*小包标签数量*/
                ordersale.big_pack_mantissa AS orderSaleBigPackMantissa,/*大包尾数*/
                ordersale.small_pack_mantissa AS orderSaleSmallPackMantissa,/*小包尾数*/
                delivery.delivery_date AS deliveryDates,/*送货日期*/
                delivery.seq,/*送货明细序号*/
                delivery.drawing_no AS drawingNo,
                delivery.tenant_id AS tenantId,
                delivery.de_id AS deId,
                delivery.sale_id AS saleId,
                delivery.sale_no AS saleNo,
                delivery.sale_item_id AS saleItemId,
                delivery.goods_id AS goodsId,
                delivery.goods_erp_code AS goodsErpCode,
                delivery.goods_code AS goodsCode,
                delivery.goods_name AS goodsName,
                delivery.goods_model AS goodsModel,
                delivery.goods_class_code AS goodsClassCode,
                delivery.goods_class_name AS goodsClassName,
                delivery.uom_id AS uomId,
                delivery.uom_name AS uomName,
                delivery.aux_uom_id,
                delivery.aux_uom_code,
                delivery.aux_uom_name,
                delivery.rate_id AS rateId,
                delivery.rate_name AS rateName,
                delivery.rate_val AS rateVal,
                delivery.currency_id AS currencyId,
                delivery.currency_name AS currencyName,
                delivery.taxes_type AS taxesType,
                delivery.invoice_type AS invoiceType,
                delivery.gst_price AS gstPrice,
                delivery.tax_price AS taxPrice,
                delivery.dev_num AS devNum,
                delivery.tem_num AS temNum,
                delivery.un_num AS unNum,
                delivery.inv_num AS invNum,
                delivery.un_inv_num AS unInvNum,
                delivery.order_num AS orderNum,
                delivery.tem_date AS temDate,
                delivery.doc_name AS docName,
                delivery.doc_url AS docUrl,
                delivery.is_print AS isPrint,
                delivery.print_count AS printCount,
                delivery.remark AS remark,
                delivery.big_pack_standard_num AS bigPackStandardNum,
                delivery.small_pack_standard_num AS smallPackStandardNum,
                delivery.big_pack_label_num AS bigPackLabelNum,
                delivery.small_pack_label_num AS smallPackLabelNum,
                delivery.big_pack_mantissa AS bigPackMantissa,
                delivery.small_pack_mantissa AS smallPackMantissa,
                delivery.purchase_confirm as purchaseConfirm,
                delivery.vendor_doc_url AS vendorDocUrl,
                delivery.vendor_doc_name AS vendorDocName,
                delivery.quality_type as qualityType,
                delivery.is_compromise as itemIsCompromise,
                delivery.create_date as createDate,
                dd.tenant_name AS tenantName,
                delivery.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
                delivery.plan_date,
                '' this_tem_num,/*送货单本次暂收数量*/
                delivery.uom_num,
                delivery.prod_date as prodDate,
                delivery.prod_batch_no as prodBatchNo,
                delivery.warranty_period as warrantyPeriod
                FROM
                    dm_delivery_item AS delivery,
                    order_pur_item AS ordersale,
                    dm_delivery AS dd
                <where>
                    delivery.sale_item_id=ordersale.id AND delivery.de_id=dd.id
                    AND delivery.delete_flag = 0 AND dd.delete_flag = 0
                    <if test="params.id != null and params.id != '' ">
                        and delivery.de_id=#{params.id}
                    </if>
                    <if test="params.vendorIds != null and params.vendorIds != '' ">
                        and dd.vendor_id=#{params.vendorIds}
                    </if>
                    <if test="params.tenantIds != null and params.tenantIds != '' ">
                        and dd.tenant_id=#{params.tenantIds}
                    </if>
                </where>
        </select>

    <select id="getPrintInfo" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
            ordersale.werks,
            ordersale.warehouse_code as warehouseCode,
            delivery.sale_seq AS saleSeq,/*销售订单明细序号*/
            ordersale.delivery_date AS deliveryDate,/*销售订单明细订单货期*/
            delivery.delivery_date AS deliveryDates,/*送货日期*/
            delivery.seq AS deliverySeq,/*送货明细序号*/
            delivery.sale_no AS saleNo,
            delivery.delivery_date AS deliveryDates,/*送货日期*/
            ordersale.reply_date AS replyDate,/*销售订单明细答交日期*/
            ordersale.warehouse_name AS warehouseName,/*销售订单明细收货仓库名称*/
            delivery.goods_erp_code AS goodsErpCode,
            delivery.goods_code AS goodsCode,
            delivery.goods_name AS goodsName,
            delivery.goods_model AS goodsModel,
            delivery.goods_class_code AS goodsClassCode,
            delivery.goods_class_name AS goodsClassName,
            sum(delivery.dev_num) AS devNum,
            delivery.tem_num AS temNum,
            delivery.un_num AS unNum,
            delivery.inv_num AS invNum,
            delivery.un_inv_num AS unInvNum,
            delivery.order_num AS orderNum,
            delivery.tem_date AS temDate,
            delivery.remark AS remark,
            delivery.create_date as createDate,
            dd.tenant_name AS tenantName,
            delivery.arrival_date arrivalDate/*到货时间 - MES扫描清点入库时间*/
            FROM
            dm_delivery_item AS delivery,
            order_pur_item AS ordersale,
            dm_delivery AS dd
        <where>
            delivery.sale_item_id=ordersale.id AND delivery.de_id=dd.id
            AND delivery.delete_flag = 0  AND dd.delete_flag = 0
            <if test="params.id != null and params.id != '' ">
                and delivery.de_id=#{params.id}
            </if>
            <if test="params.vendorIds != null and params.vendorIds != '' ">
                and dd.vendor_id=#{params.vendorIds}
            </if>
            <if test="params.tenantIds != null and params.tenantIds != '' ">
                and dd.tenant_id=#{params.tenantIds}
            </if>
        </where>
        group by delivery.sale_no, delivery.goods_erp_code, delivery.goods_name
        ORDER BY deliverySeq
    </select>

        <update id="updateQualityType" parameterType="com.dian.modules.dm.entity.DeliveryItemEntity">
            update
              dm_delivery_item
            set
              quality_type = #{qualityType}
            where
               id = #{id}
        </update>


        <select id="findByDeNo" resultType="java.util.Map" parameterType="java.util.Map">
            SELECT
                dd.id, /*送货单Id*/
                dd.de_no deNo,/*送货单号*/
                dd.tenant_id deliveryTenantId, /* 送货主表采购方id */
                dd.vendor_id deliveryVendorId, /* 送货主表供应商id */
                ddi.seq,/*序号*/
                ddi.delivery_date deDate,/*送货日期*/
                dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
                ddi.sale_no sale_no,/*采购订单号*/
                ddi.sale_seq purSeq,/*采购订单序号*/
                dd.vendor_code vendorCode,/*供应商编码*/
                dd.vendor_name vendorName,/*供应商名称*/
                ddi.goods_code goodsCode,/*品号*/
                ddi.goods_erp_code goodsErpCode,/*erp*/
                ddi.goods_name goodsName,/*品名*/
                ddi.goods_model goodsModel,/*物料描述*/
                ddi.order_num orderNum,/*订单数量*/
                ddi.tem_date temDate,/*暂收日期*/
                ddi.dev_num devNum,/*已送货数量*/
                ddi.tem_num temNum,/*暂收数量*/
                ddi.un_num unNum,/*未收数量*/
                ddi.inv_num invNum,/*验收已入库数量*/
                ddi.un_inv_num unInvNum,/*未验收数量*/
                dd.see see,/* 是否已查看:0-未查看,1-已查看*/
                ddi.vendor_doc_url vendorDocUrl,/* 供货方文档URL */
                dd.tenant_name tenantName,/*客户名称*/
                ddi.qua_num,
                ddi.qua_check_stat,
                ddi.qua_sheet_stat,
                ddi.competent_num,
                ddi.un_competent_num,
                ddi.submit_stat,
                ddi.compromise_num,
                ddi.arrival_date arrivalDate/*到货时间 - MES扫描清点入库时间*/
            FROM
              dm_delivery dd,dm_delivery_item ddi
            <where>
                and dd.id=ddi.de_id
                and dd.delete_flag=0
                and ddi.delete_flag=0
                and dd.de_stat >= 2
                /*采购方条件查询*/
                <!--<if test="params.tenantId != null and params.tenantId != '' ">-->
                    and dd.tenant_id=#{params.tenantId}
                    <if test="params.deNo != null and params.deNo != '' ">
                        and dd.de_no like CONCAT('%',#{params.deNo},'%')
                    </if>
                <!--</if>-->
            </where>
            order by dd.id desc ,ddi.seq desc
        </select>

        <select id="findDeliveryId" parameterType="java.util.HashMap" resultType="java.util.HashMap">
            SELECT
                delivery.id AS deliveryId,/*送货明细id*/
                delivery.sale_id AS orderSaleId,/*销售订单明细id*/
                delivery.tenant_id AS orderSaleTenantId,/*销售订单租户id*/
                delivery.sale_seq AS saleSeq,/*销售订单明细序号*/
                delivery.delivery_date AS deliveryDates,/*送货日期*/
                dd.de_no as deNo,/*送货单号*/
                dd.vendor_code as vendorCode,
                dd.vendor_name as vendorName,
                delivery.seq AS deliverySeq,/*送货明细序号*/

                delivery.tenant_id AS tenantId,
                delivery.de_id AS deId,
                delivery.sale_id AS saleId,
                delivery.sale_no AS saleNo,
                delivery.sale_item_id AS saleItemId,
                delivery.goods_id AS goodsId,
                delivery.goods_erp_code AS goodsErpCode,
                delivery.goods_code AS goodsCode,
                delivery.goods_name AS goodsName,
                delivery.goods_model AS goodsModel,
                delivery.uom_id AS uomId,
                delivery.uom_name AS uomName,
                delivery.rate_id AS rateId,
                delivery.rate_name AS rateName,
                delivery.rate_val AS rateVal,
                delivery.currency_id AS currencyId,
                delivery.currency_name AS currencyName,
                delivery.taxes_type AS taxesType,
                delivery.invoice_type AS invoiceType,
                delivery.gst_price AS gstPrice,
                delivery.tax_price AS taxPrice,
                delivery.dev_num AS devNum,
                delivery.tem_num AS temNum,
                delivery.un_num AS unNum,
                delivery.inv_num AS invNum,
                delivery.un_inv_num AS unInvNum,
                delivery.order_num AS orderNum,
                delivery.tem_date AS temDate,
                delivery.doc_name AS docName,
                delivery.doc_url AS docUrl,
                delivery.is_print AS isPrint,
                delivery.print_count AS printCount,
                delivery.remark AS remark,
                delivery.big_pack_standard_num AS bigPackStandardNum,
                delivery.small_pack_standard_num AS smallPackStandardNum,
                delivery.big_pack_label_num AS bigPackLabelNum,
                delivery.small_pack_label_num AS smallPackLabelNum,
                delivery.big_pack_mantissa AS bigPackMantissa,
                delivery.small_pack_mantissa AS smallPackMantissa,
                delivery.purchase_confirm as purchaseConfirm,
                delivery.vendor_doc_url AS vendorDocUrl,
                delivery.vendor_doc_name AS vendorDocName,
                delivery.quality_type as qualityType,/*判断该送货单明细是否有生成质检报告单 0为未生成 1为已生成*/
                delivery.goods_class_name as goodsClassName,
                dd.tenant_name AS tenantName,
                delivery.qua_num as quaNum,
                delivery.qua_check_stat as quaCheckStat,
                delivery.qua_sheet_stat as quaSheetStat,
                delivery.competent_num as competentNum,
                delivery.un_competent_num as unCompetentNum,
                delivery.submit_stat AS submitStat,
                delivery.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
                qsh.batch_no batchNo
            FROM
                dm_delivery_item AS delivery,
                dm_delivery AS dd,
                qua_sheet_head as qsh
            <where>
                delivery.de_id=dd.id and qsh.delivery_item_id = delivery.id
                AND delivery.delete_flag = 0 AND dd.delete_flag = 0
                <if test="params.id != null and params.id != '' ">
                    and delivery.de_id=#{params.id}
                </if>
            </where>
        </select>

        <select id="findDeliveryByGoodsErpCode" parameterType="java.util.HashMap" resultType="java.util.HashMap">
            dd.tenant_p_id deliveryTenantPId,/*送货单号*/
            dd.tenant_id deliveryTenantId, /* 送货主表采购方id */
            dd.id deliveryId,
            dd.de_no deNo,/*送货单号*/
            dd.vendor_id deliveryVendorId, /* 送货主表供应商id */
            dd.vendor_code deliveryVendorCode, /* 送货主表供应商id */
            dd.vendor_name deliveryVendorName, /* 送货主表供应商id */
            dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
            dd.tenant_name tenantName,/*客户名称*/
            dd.delivery_date deliveryDate,
            /*dd.sap_de_no sapDeNo,*/
            ddi.tenant_id deliveryItemTenantId,
            ddi.de_id deId,
            ddi.sale_id saleId,
            ddi.sale_item_id saleItemId,
            ddi.sale_no saleNo,
            ddi.id deliveryItemId,
            ddi.seq seq,/*序号*/
            ddi.delivery_date deliveryDate,/*送货日期*/
            ddi.goods_id goodsId,
            ddi.goods_erp_code goodsErpCode,
            ddi.goods_code goodsCode,
            ddi.goods_name goodsName,
            ddi.goods_model goodsModel,
            ddi.goods_class_name goodsClassName,
            ddi.goods_class_code goodsClassCode,
            ddi.sale_seq saleSeq,
            ddi.qua_num quaNum,
            ddi.qua_check_stat quaCheckStat,
            ddi.qua_sheet_stat quaSheetStat,
            ddi.competent_num as competentNum,
            ddi.un_competent_num as unCompetentNum,
            ddi.submit_stat AS submitStat,
            ddi.arrival_date arrivalDate/*到货时间 - MES扫描清点入库时间*/
          from
            dm_delivery dd,dm_delivery_item ddi
          <where>
              and dd.id=ddi.de_id
              <if test="params.deNo != null and params.deNo != ''">
                  and dd.de_no = #{params.deNo}
              </if>
              <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
                  and ddi.goods_erp_code = #{params.goodsErpCode}
              </if>
          </where>
        </select>

        <select id="findDeliveryItemId" parameterType="java.lang.Long" resultType="java.util.HashMap">
            SELECT
                delivery.tenant_id,
                delivery.id,
                delivery.de_no,
                delivery.vendor_id,
                delivery.vendor_code,
                delivery.vendor_name,
                delivery.de_stat,
                deliveryItem.de_id AS deId,/*送货单主表Id*/
                deliveryItem.seq AS deliverySeq,/**/
                deliveryItem.id AS deliveryId,
                deliveryItem.sale_id,
                deliveryItem.sale_no,
                deliveryItem.sale_item_id,
                deliveryItem.goods_id,
                deliveryItem.goods_erp_code,
                deliveryItem.goods_code,
                deliveryItem.goods_name,
                deliveryItem.goods_model,
                deliveryItem.delivery_date as deliveryDates,
                deliveryItem.dev_num,/*送货数量*/
                deliveryItem.goods_class_code,
                deliveryItem.goods_class_name,
                deliveryItem.sale_seq as saleSeq,
                deliveryItem.qua_num,/*抽检数量*/
                deliveryItem.warehouse_name,
                deliveryItem.qua_check_stat,
                deliveryItem.qua_sheet_stat as quaSheetStat,
                deliveryItem.competent_num as competentNum,
                deliveryItem.un_competent_num as unCompetentNum,/*不合格数量*/
                deliveryItem.submit_stat AS submitStat,
                deliveryItem.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
                qsh.batch_no batchNo
            from
              dm_delivery delivery ,dm_delivery_item deliveryItem,qua_sheet_head qsh
            where delivery.id = deliveryItem.de_id
            and qsh.delivery_item_id = deliveryItem.id
            and deliveryItem.id = #{deliveryId}
        </select>

        <select id="findByDeIdAndGoodsErpCode" parameterType="java.util.HashMap" resultType="java.util.HashMap">
            select
              deliveryItem.de_id AS deId,deliveryItem.seq AS deliverySeq,deliveryItem.id AS deliveryId,deliveryItem.sale_id,
              deliveryItem.sale_no,deliveryItem.sale_item_id,deliveryItem.goods_id,deliveryItem.goods_erp_code,
              deliveryItem.goods_code,deliveryItem.goods_name,deliveryItem.goods_model,deliveryItem.delivery_date as deliveryDates,
              deliveryItem.dev_num,deliveryItem.goods_class_code,deliveryItem.goods_class_name,deliveryItem.sale_seq as saleSeq,
              deliveryItem.qua_num,deliveryItem.warehouse_name,deliveryItem.qua_check_stat,deliveryItem.qua_sheet_stat as quaSheetStat,
              deliveryItem.competent_num as competentNum,deliveryItem.un_competent_num as unCompetentNum,deliveryItem.submit_stat AS submitStat,
              deliveryItem.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
              delivery.de_no,delivery.tenant_id,delivery.vendor_id,delivery.vendor_code,delivery.vendor_name
            from
               dm_delivery_item deliveryItem,dm_delivery delivery
            <where>
                delivery.id = deliveryItem.de_id
                and delivery.de_stat = 2
                and delivery.delete_flag = 0
                and deliveryItem.delete_flag = 0
                <if test="params.tenantId != null and params.tenantId != ''">
                    and delivery.tenant_id = #{params.tenantId}
                </if>
                <if test="params.vendorId != null and params.vendorId != ''">
                    and delivery.vendor_id = #{params.vendorId}
                </if>
                <if test="params.deliveryId != null and params.deliveryId != ''">
                    and deliveryItem.de_id = #{params.deliveryId}
                </if>
                <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
                   and deliveryItem.goods_erp_code = #{params.goodsErpCode}
                </if>
            </where>
        </select>

    <!--根据主表id获取送货单明细行数量总和-->
    <select id="getDeliveryTotalNumByDeId" resultType="java.math.BigDecimal">
        SELECT SUM(dev_num)
        FROM dm_delivery_item
        WHERE de_id = #{deId}
    </select>

    <!--  查询送货单主从表信息  -->
    <select id="findDeliverySlaveList" resultType="com.dian.modules.dm.vo.DeliverySlaveVO">
        SELECT
        dd.id, /*送货单Id*/
        dd.de_no deNo,/*送货单号*/
        dd.tenant_id deliveryTenantId, /*送货主表采购方id-----(meng)*/
        dd.vendor_id deliveryVendorId, /*送货主表供应商id-----(meng)*/
        ddi.seq,/*序号*/
        ddi.id deItemId,
        dd.delivery_date deDate,/*送货日期*/
        dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
        ddi.sale_id,/*采购订单id*/
        ddi.sale_item_id,/*采购订单行id*/
        ddi.sale_no,/*采购订单号*/
        ddi.sale_seq purSeq,/*采购订单序号*/
        dd.vendor_code vendorCode,/*供应商编码*/
        dd.vendor_name vendorName,/*供应商名称*/
        ddi.goods_id goodsId,/*物料id*/
        ddi.goods_code goodsCode,/*品号*/
        ddi.goods_erp_code goodsErpCode,/*erp*/
        ddi.goods_name goodsName,/*品名*/
        ddi.goods_model goodsModel,/*物料描述*/
        ddi.goods_class_code,/*物料分类编码*/
        ddi.goods_class_name,/*物料分类名称*/
        ddi.uom_id uomId,/*计量单位ID*/
        ddi.uom_name uomName,/*计量单位名称*/
        ddi.uom_num,/*计量单位名称*/
        ddi.aux_uom_id,
        ddi.aux_uom_code,
        ddi.aux_uom_name,
        ddi.rate_id rateId,/*税率ID*/
        ddi.rate_val rateVal,/*税率值*/
        ddi.currency_id currencyId,/*币别id*/
        ddi.currency_name currencyName,/*币别名称*/
        ddi.taxes_type taxesType,/*税种类型*/
        ddi.invoice_type invoiceType,/*发票类型*/
        ddi.gst_price gstPrice,/*含税单价*/
        ddi.tax_price taxPrice,/*不含税单价*/
        ddi.tem_date temDate,/*暂收日期*/
        ddi.dev_num devNum,/*已送货数量*/
        ddi.tem_num temNum,/*暂收数量*/
        ddi.un_num unNum,/*未收数量*/
        ddi.inv_num invNum,/*验收已入库数量*/
        ddi.un_inv_num unInvNum,/*未验收数量*/
        ddi.order_num orderNum, /*测试字段 - 总数量*/
        ddi.delivery_date deliveryDate,/*交货日期*/
        dd.delivery_date reality_delivery_date,
        ddi.reply_date replyDate,/*答交日期*/
        ddi.rate_name rateName,/*税别*/
        ddi.drawing_no,
        ddi.pur_employee_name purEmployee,/*采购方业务*/
        ddi.sale_employee_name saleEmployee,/*销售方业务*/
        ddi.barcode_type barcodeType,/*条码控制*/
        dd.tenant_name tenantName,/*客户名称*/
        ddi.warehouse_name warehouseName, /*仓库名称*/
        dd.is_compromise isCompromise,/*是否让步接收 0否 1是*/
        dd.is_compromise_create isComproiseCreate,/*判断是否创建让步接收单：0_未创建，1_已创建*/
        ddi.is_compromise itemIsCompromise,/*送货单明细行让步接收标识：0_否 ，1_是*/
        ddi.is_compromise_create itemIsCompromiseCreate,/*是否创建让步接收单：0_未创建，1_已创建*/
        ddi.compromise_num compromiseNum,/*让步接收数量*/
        ddi.ret_num retNum,/*暂退数量*/
        ddi.arrival_date arrivalDate,/*到货时间 - MES扫描清点入库时间*/
        ddi.qua_sheet_stat quaSheetStat, /*质检报告单质检状态 0_还有未质检的质检报告 1_全部已质检*/
        ddi.qua_check_stat quaCheckStat, /*抽检状态 1_合格，2_不合格*/
        ddi.submit_stat submitStat, /*是否有提交到MES 0_未提交 1_已提交*/
        dd.create_date createDate,/*送货单单据创建日期*/
        dd.is_print isPrint,/*送货单打印状态*/
        dd.print_count printCount,/*送货单打印次数*/
        ddi.plan_date,
        dd.dept_id,
        dd.dept_code,
        dd.dept_name,
        dd.audit_time,
        ddi.prod_date,
        ddi.remark as line_remark
        FROM
        dm_delivery dd,dm_delivery_item ddi
        <where>
            AND dd.id = ddi.de_id AND dd.delete_flag = 0
            AND ddi.delete_flag = 0
            <if test="params.mainIds != null and params.mainIds.size > 0">
                AND dd.id in
                <foreach collection="params.mainIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.lineId != null and params.lineId != ''">
                AND ddi.id = #{params.lineId}
            </if>
            <if test="params.lineIds != null and params.lineIds.size > 0">
                AND ddi.id in
                <foreach collection="params.lineIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.sdeliveryDate != null and params.sdeliveryDate != '' ">
                <![CDATA[ and dd.create_date >= STR_TO_DATE(#{params.sdeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            <if test="params.edeliveryDate != null and params.edeliveryDate != '' ">
                <![CDATA[ and dd.create_date <= STR_TO_DATE(#{params.edeliveryDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                AND dd.dept_id = #{params.deptId}
            </if>
            <if test="params.deNo != null and params.deNo != ''">
                AND dd.de_no like CONCAT('%',#{params.deNo},'%')
            </if>
            <if test="params.deSeq != null and params.deSeq != ''">
                AND ddi.seq like CONCAT('%',#{params.deSeq},'%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                AND ddi.sale_no like CONCAT('%',#{params.orderNo},'%')
            </if>
            <if test="params.goodsIdList != null and params.goodsIdList.size > 0">
                AND ddi.goods_id in
                <foreach collection="params.goodsIdList" item="goodsId" open="(" separator="," close=")">
                    #{goodsId}
                </foreach>
            </if>
            <if test="params.dept != null and params.dept != ''">
                AND (
                dd.dept_code like CONCAT('%',#{params.dept},'%') or dd.dept_name like CONCAT('%',#{params.dept},'%')
                )
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                AND dd.vendor_id = #{params.vendorId}
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                AND (
                dd.vendor_code like CONCAT('%',#{params.vendor},'%') or dd.vendor_name like CONCAT('%',#{params.vendor},'%')
                )
            </if>
            <if test="params.goods != null and params.goods != ''">
                AND (
                ddi.goods_code like CONCAT('%',#{params.goods},'%')
                or ddi.goods_erp_code like CONCAT('%',#{params.goods},'%')
                or ddi.goods_name like CONCAT('%',#{params.goods},'%')
                or ddi.goods_model like CONCAT('%',#{params.goods},'%')
                )
            </if>
            <if test="params.deStat != null and params.deStat != '' ">
                AND dd.de_stat =#{params.deStat}
            </if>
            <if test="params.lineRemark != null and params.lineRemark != '' ">
                AND ddi.remark LIKE CONCAT('%',#{params.lineRemark},'%')
            </if>
            <if test="params.whereType != null and params.whereType != ''">
                <choose>
                    <!-- 全部 -->
                    <when test="params.whereType == 1 or params.whereType == '1'"></when>
                    <!-- 已送未收 -->
                    <when test="params.whereType == 2 or params.whereType == '2'">
                        and dd.de_stat = 2
                        and inv_num = 0
                        and ddi.un_competent_num = 0
                    </when>
                    <!-- 待发出 -->
                    <when test="params.whereType == 3 or params.whereType == '3'">
                        and dd.de_stat = 1
                    </when>
                    <!-- 暂收 -->
                    <when test="params.whereType == 4 or params.whereType == '4'">
                        and ddi.tem_num&gt; 0
                    </when>
                    <!-- 暂退 -->
                    <when test="params.whereType == 5 or params.whereType == '5'">
                        and ddi.ret_num &gt; 0
                    </when>
                    <!-- 待检区 -->
                    <when test="params.whereType == 6 or params.whereType == '6'">
                        and dd.de_stat = 2 <!-- 已发出 -->
                        and ddi.qua_sheet_stat = 0 <!-- qua_sheet_stat = 0 表示还有未质检的送货单明细-->
                    </when>
                    <!-- 不良区 -->
                    <when test="params.whereType == 7 or params.whereType == '7'">
                        <!-- 只查询出是否让步状态为是 和 是否有创建让步单为未创建 -->
                        and ddi.is_compromise = 1
                        and ddi.is_compromise_create = 0
                        and ddi.qua_check_stat = 2
                        <!-- 只查询当前送货单明细行的暂收数量大于当前送货单明细行对应让步接收单的汇总数量 -->
                        AND ddi.tem_num > (SELECT IFNULL(SUM(dci.act_comproise_num),0) FROM dm_compromise_item dci WHERE dci.de_item_id = ddi.id)
                    </when>
                    <!-- 不良手工退货区 -->
                    <when test="params.whereType == 8 or params.whereType == '8'">
                        and ddi.is_compromise = 1
                        and ddi.defective_manual_return_type = 1
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY dd.id DESC,ddi.seq
    </select>


    <select id="getSumInTransitQuantity" resultType="com.dian.mbo.sap.zmm009.InTransitQuantityInfoVO">
        SELECT
            dd.dept_code,
            dd.vendor_code,
            dd.audit_time,
            ddi.goods_erp_code,
            SUM((IFNULL(ddi.dev_num,0) - (IFNULL(ddi.inv_num,0)))) as dev_num
        FROM
            dm_delivery dd,
            dm_delivery_item ddi
        WHERE
            dd.id = ddi.de_id
          AND dd.delete_flag = 0 AND dd.delete_flag = 0
        <if test="request.deptCode != null">
            AND dd.dept_code = #{request.deptCode}
        </if>
        <if test="request.vendorCode != null">
            AND dd.vendor_code = #{request.vendorCode}
        </if>
        <if test="request.goodsErpCode != null">
            AND ddi.goods_erp_code = #{request.goodsErpCode}
        </if>
        <if test="request.startDate != null and request.endDate != null">
            AND dd.audit_time BETWEEN #{request.startDate} AND #{request.endDate}
        </if>
        GROUP BY dd.dept_code,dd.vendor_code,ddi.goods_erp_code
    </select>

    <select id="getInTransitQuantityIds" resultType="java.lang.Long">
        SELECT
            DISTINCT dd.id
        FROM
        dm_delivery dd,
        dm_delivery_item ddi
        WHERE
        dd.id = ddi.de_id
        AND dd.delete_flag = 0 AND dd.delete_flag = 0
        <if test="request.deptCode != null">
            AND dd.dept_code = #{request.deptCode}
        </if>
        <if test="request.vendorCode != null">
            AND dd.vendor_code = #{request.vendorCode}
        </if>
        <if test="request.goodsErpCode != null">
            AND ddi.goods_erp_code = #{request.goodsErpCode}
        </if>
        <if test="request.startDate != null and request.endDate != null">
            AND dd.audit_time BETWEEN #{request.startDate} AND #{request.endDate}
        </if>
    </select>

    <select id="countDevNumByOrderItem" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT SUM(dev_num - ABS(tem_num)) as dev_num
        FROM dm_delivery_item
        WHERE
            delete_flag = 0
        <if test="orderItemId != null and orderItemId != ''">
            AND sale_item_id = #{orderItemId}
        </if>
    </select>
</mapper>
