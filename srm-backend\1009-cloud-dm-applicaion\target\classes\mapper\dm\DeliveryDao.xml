<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dian.modules.dm.dao.DeliveryDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dian.modules.dm.vo.DeliveryVO" id="deliveryMap">
        <id column="delivery_id" property="id"/>
        <result property="deNo" column="de_no"/>
        <result property="vendorId" column="vendor_id"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="isPrint" column="is_print"/>
        <result property="deStat" column="de_stat"/>
        <result property="isEle" column="is_ele"/>
        <result property="logisticsName" column="logistics_name"/>
        <result property="eleNo" column="ele_no"/>
        <result property="deliveryDate" column="delivery_date"/>
        <result property="documentDate" column="document_date"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="see" column="see"/>
        <result property="tenantName" column="tenant_name"/>
        <result property="creater" column="creater"/>
        <result property="createDate" column="create_date"/>
        <result property="modifiId" column="modifi_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyDate" column="modify_date"/>
        <collection property="deliveryItemVOList" ofType="com.dian.modules.dm.vo.DeliveryItemVO">
            <id property="id" column="id"/>
            <result property="deId" column="de_id"/>
            <result property="saleId" column="sale_id"/>
            <result property="saleNo" column="sale_no"/>
            <result property="saleItemId" column="sale_item_id"/>
            <result property="goodsId" column="goods_id"/>
            <result property="goodsErpCode" column="goods_erp_code"/>
            <result property="goodsCode" column="goods_code"/>
            <result property="goodsName" column="goods_name"/>
            <result property="goodsModel" column="goods_model"/>
            <result property="uomId" column="uom_id"/>
            <result property="uomName" column="uom_name"/>
            <result property="rateId" column="rate_id"/>
            <result property="rateName" column="rate_name"/>
            <result property="rateVal" column="rate_val"/>
            <result property="currencyId" column="currency_id"/>
            <result property="currencyName" column="currency_name"/>
            <result property="taxesType" column="taxes_type"/>
            <result property="invoiceType" column="invoice_type"/>
            <result property="gstPrice" column="gst_price"/>
            <result property="taxPrice" column="tax_price"/>
            <result property="temDate" column="tem_date"/>
            <result property="devNum" column="dev_num"/>
            <result property="temNum" column="tem_num"/>
            <result property="unNum" column="un_num"/>
            <result property="invNum" column="inv_num"/>
            <result property="unInvNum" column="un_inv_num"/>
            <result property="orderNum" column="order_num"/>
            <result property="barcodeType" column="barcode_type"/>
            <result property="docName" column="doc_name"/>
            <result property="docUrl" column="doc_url"/>
            <result property="isPrint" column="is_print"/>
            <result property="printCount" column="print_count"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="seq" column="seq"/>
            <result property="bigPackStandardNum" column="big_pack_standard_num"/>
            <result property="smallPackStandardNum" column="small_pack_standard_num"/>
            <result property="bigPackLabelNum" column="big_pack_label_num"/>
            <result property="smallPackLabelNum" column="small_pack_label_num"/>
            <result property="bigPackMantissa" column="big_pack_mantissa"/>
            <result property="smallPackMantissa" column="small_pack_mantissa"/>
            <result property="purchaseConfirm" column="purchase_confirm"/>
            <result property="vendorDocUrl" column="vendor_doc_url"/>
            <result property="vendorDocName" column="vendor_doc_name"/>
            <result property="gstAmount" column="gst_amount"/>
            <result property="taxAmount" column="tax_amount"/>
            <result property="soureNo" column="soure_no"/>
            <result property="purEmployeeName" column="pur_employee_name"/>
            <result property="saleEmployeeName" column="sale_employee_name"/>
            <result property="goodsClassName" column="goods_class_name"/>
            <result property="warehouseCode" column="warehouse_code"/>
            <result property="warehouseName" column="warehouse_name"/>
            <result property="orderType" column="order_type"/>
        </collection>
    </resultMap>

    <select id="getSerialNo" parameterType="java.util.HashMap" statementType="CALLABLE">
            call getSerialNo(
                    #{params.tableNames,mode=IN,jdbcType=VARCHAR},
                    #{params.outBillNo,mode=OUT,jdbcType=VARCHAR}
                )
        </select>

    <select id="getDeliveryHomeCount" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT
        count(*) as deliveryHomeCount
        FROM
        order_pur so,
        order_pur_item soi
        WHERE
        so.id=soi.pur_id
        and((so.change_count=0 and item_stat=4)or so.change_count>0 and item_stat in (1,4) )
        <if test="params.startRequiredDeliveryTime != null and params.startRequiredDeliveryTime != '' ">
            <![CDATA[ and soi.reply_date >= STR_TO_DATE(#{params.startRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.endRequiredDeliveryTime != null and params.endRequiredDeliveryTime != '' ">
            <![CDATA[ and soi.reply_date <= STR_TO_DATE(#{params.endRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.deliveryType != null and params.deliveryType != '' ">
            and soi.delivery_type=#{params.deliveryType}
        </if>
        <if test="params.tenantId != null and params.tenantId != '' ">
            and so.tenant_id=#{params.tenantId}
        </if>
        <if test="params.vendorId != null and params.vendorId != '' ">
            and so.vendor_id=#{params.vendorId}
        </if>
        <if test="params.tenantName != null and params.tenantName != ''">
            and so.tenant_name like CONCAT('%',#{params.tenantName},'%')
        </if>
        <if test="params.orderNo != null and params.orderNo != '' ">
            and (so.pur_no like CONCAT('%',#{params.orderNo},'%'))
        </if>
        <if test="params.dept != null and params.dept != '' ">
            and (so.dept_name like CONCAT('%',#{params.dept},'%'))
        </if>
        <if test="params.sortMode != null and params.sortMode != ''">
            <choose>
                <!-- 默认序号排序 -->
                <when test="params.sortMode == 1 or params.sortMode == '1'">
                    ORDER BY soi.reply_date desc
                </when>
                <!-- 物料排序 -->
                <when test="params.sortMode == 2 or params.sortMode == '2'">
                    and (ifnull(soi.order_num, 0) - ifnull(soi.make_num, 0)) > 0
                    ORDER BY soi.goods_erp_code desc
                </when>
                <!-- 可制单数排序 - 显示可制单数量大于0 -->
                <when test="params.sortMode == 3 or params.sortMode == '3'">
                    and (ifnull(soi.order_num, 0) - ifnull(soi.make_num, 0)) > 0
                    ORDER BY soi.reply_date desc
                </when>
            </choose>
        </if>
    </select>

    <select id="getDeliveryHomeList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT * FROM (
        <if test="params.deliveryType != null and params.two != '' ">
            <choose>
                <when test="params.deliveryType == 2 or params.deliveryType == '2'">
                    <include refid="getDeliveryBoardByPlan"/><!-- 按送货计划 -->
                </when>
                <when test="params.deliveryType == 1 or params.deliveryType == '1'">
                    <include refid="getDeliveryBoardByOrder"/><!-- 按订单 -->
                </when>
            </choose>
        </if>
        ) so
        <where>
            <if test="params.tenantName != null and params.tenantName != ''">
                and so.tenantName like CONCAT('%',#{params.tenantName},'%')
            </if>
            <if test="params.orderNo != null and params.orderNo != '' ">
                and (so.saleNo like CONCAT('%',#{params.orderNo},'%'))
            </if>
            <if test="params.dept != null and params.dept != '' ">
                and (so.dept_name like CONCAT('%',#{params.dept},'%'))
            </if>
             <if test="params.bsart != null and params.bsart != '' ">
                and so.order_type=#{params.bsart}
            </if>
            <if test="params.isOut != null and params.isOut != ''">
                <choose>
                    <when test="params.isOut == 1 or params.isOut == '1'">
                        and so.material_list_num > 0
                    </when>
                    <when test="params.isOut == 0 or params.isOut == '0'">
                        and so.material_list_num = 0
                    </when>
                </choose>
            </if>
            AND so.canMakeNum > 0
            ORDER BY so.deliveryDate ASC
        </where>
    </select>

    <!-- 按订单 -->
    <sql id="getDeliveryBoardByOrder">
        SELECT
        #{params.startDate} as startDate,
        soi.uom_code,
        so.pur_id,
        so.pur_Name,
        so.dept_id,
        so.dept_name,
        so.dept_code,
        soi.order_price_uom,
        soi.werks,
        soi.price_uom,
        so.tenant_name tenantName,
        soi.delivery_type,
        so.id saleId,
        soi.id saleItemId,
        so.pur_no saleNo,
        '' planNo,
        so.order_date orderDate,
        soi.goods_id goodsId,
        soi.goods_code goodsCode,
        soi.goods_name goodsName,
        soi.goods_model goodsModel,
        soi.goods_erp_code goodsErpCode,
        soi.goods_class_name goodsClassName,
        soi.class_code goodsClassCode,
        soi.seq saleSeq,
        soi.order_num orderNum,
        soi.fix_num fixNum,
        so.vendor_id,
        so.vendor_code vendorCode,
        so.vendor_name vendorName,
        soi.wait_num waitNum,
        soi.uom_id uomId,
        soi.uom_name uomName,
        soi.aux_uom_id,
        soi.aux_uom_name,
        soi.gst_price gstPrice,
        soi.tax_price taxPrice,
        soi.gst_price*soi.order_num gstAmount,
        soi.currency_name currencyName,
        soi.currency_id currencyId,
        soi.rate_id rateId,
        soi.rate_val rateVal,
        soi.rate_name rateName,
        soi.taxes_type taxesType,
        soi.invoice_type invoiceType,
        so.order_type orderType,
        soi.purchase_remark remark,
        soi.change_count changeCount,
        so.stat,
        soi.seq,
        soi.is_close,
        soi.reply_date replyDate,
        barcode_type barcodeType,
        soi.pur_employee_name purEmployee,
        soi.sale_employee_name saleEmployee,
        soi.soure_no sourceNo,
        soi.delivery_date deliveryDate,
        soi.warehouse_code warehouseCode,
        soi.warehouse_name warehouseName,
        soi.item_stat itemStat,
        soi.delivery_type deliveryType,
        '' collectFlag,
        0 receivingControl,
        so.vendor_delivery_address,
        so.shipping_address,
        0 id,
        0 list_id,
        soi.tenant_id tenantId,
        ifnull(soi.make_num,0) makeNum,
        (ifnull(soi.order_num,0)- ifnull(soi.make_num,0) ) canMakeNum,
        so.order_type,
        0 is_source_to_plan,
        (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num
        FROM order_pur so,order_pur_item soi WHERE
        so.id=soi.pur_id
--         AND soi.item_stat=4
--         AND soi.delete_flag=0
--         AND soi.delivery_type = 1
        AND soi.is_close = 0
        AND soi.return_mark = 0
        <if test="params.startRequiredDeliveryTime != null and params.startRequiredDeliveryTime != '' ">
            <![CDATA[ and soi.reply_date >= STR_TO_DATE(#{params.startRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.endRequiredDeliveryTime != null and params.endRequiredDeliveryTime != '' ">
            <![CDATA[ and soi.reply_date <= STR_TO_DATE(#{params.endRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.tenantId != null and params.tenantId != '' ">
            and so.tenant_Id=#{params.tenantId}
        </if>
        <if test="params.vendorId != null and params.vendorId != '' ">
            and so.vendor_id=#{params.vendorId}
        </if>
        <if test="params.goods != null and params.goods != ''">
            and (
            soi.goods_erp_code LIKE CONCAT('%',#{params.goods},'%')
            OR soi.goods_name LIKE CONCAT('%',#{params.goods},'%')
            )
        </if>
        <if test="params.goodsList != null and params.goodsList.size()">
            AND soi.goods_erp_code IN
            <foreach collection="params.goodsList" item="goods" separator="," open="(" close=")">
                #{goods}
            </foreach>
        </if>
    </sql>

<!--    <sql id="getDeliveryThree">-->
<!--        SELECT-->
<!--        #{params.startDate} as startDate,-->
<!--        so.dept_id,-->
<!--        so.dept_name,-->
<!--        so.dept_code,-->
<!--        soi.order_price_uom,-->
<!--        soi.werks,-->
<!--        soi.price_uom,-->
<!--        so.tenant_name,-->
<!--        soi.delivery_type,-->
<!--        so.id saleId,-->
<!--        soi.id saleItemId,-->
<!--        so.pur_no saleNo,-->
<!--        it.plan_no planNo,-->
<!--        so.order_date orderDate,-->
<!--        soi.goods_id goodsId,-->
<!--        soi.goods_code goodsCode,-->
<!--        soi.goods_name goodsName,-->
<!--        soi.goods_model goodsModel,-->
<!--        soi.goods_erp_code goodsErpCode,-->
<!--        soi.goods_class_name goodsClassName,-->
<!--        soi.class_code goodsClassCode,-->
<!--        soi.seq saleSeq,-->
<!--        ifnull(it.drawing_no,'-') as drawingNo,-->
<!--        it.match_num as orderNum,-->
<!--        ( SELECT IFNULL(SUM(dev_num),0) FROM dm_delivery dd,dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum,-->
<!--        so.vendor_id,-->
<!--        so.vendor_code vendorCode,-->
<!--        so.vendor_name vendorName,-->
<!--        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) waitNum,-->
<!--        soi.uom_id uomId,-->
<!--        soi.uom_code,-->
<!--        soi.uom_name uomName,-->
<!--        soi.gst_price gstPrice,-->
<!--        soi.tax_price taxPrice,-->
<!--        soi.gst_price*soi.order_num gstAmount,-->
<!--        soi.currency_name currencyName,-->
<!--        soi.currency_id currencyId,-->
<!--        soi.rate_id rateId,-->
<!--        soi.rate_val rateVal,-->
<!--        soi.rate_name rateName,-->
<!--        soi.taxes_type taxesType,-->
<!--        soi.invoice_type invoiceType,-->
<!--        so.order_type orderType,-->
<!--        soi.purchase_remark remark,-->
<!--        soi.change_count changeCount,-->
<!--        so.stat,-->
<!--        soi.seq,-->
<!--        &#45;&#45; soi.creation_date,-->
<!--        soi.is_close,-->
<!--        it.plan_date replyDate,-->
<!--        barcode_type barcodeType,-->
<!--        it.purchaser_name purEmployee,-->
<!--        soi.sale_employee_name saleEmployee,-->
<!--        soi.soure_no sourceNo,-->
<!--        it.delivery_date deliveryDate,-->
<!--        it.warehouse_code warehouseCode,-->
<!--        soi.warehouse_name warehouseName,-->
<!--        soi.item_stat itemStat,-->
<!--        soi.delivery_type deliveryType,-->
<!--        it.collect_flag collectFlag,-->
<!--        it.receiving_control receivingControl,-->
<!--        so.vendor_delivery_address,-->
<!--        so.shipping_address,-->
<!--        it.id,-->
<!--        it.id as list_id,-->
<!--        soi.tenant_id tenantId,-->
<!--        so.pur_id,-->
<!--        so.pur_name,-->
<!--        IFNULL(it.refund_num,0) refund_num,-->
<!--        IFNULL(it.un_competent_num,0) un_competent_num,-->
<!--        (ifnull( it.make_num, 0 )) makeNum,-->
<!--&#45;&#45;         canMakeNum剩余可创建送货单数 = 计划数 - 已制单数 + 暂退数 + 不合格数-->
<!--        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) canMakeNum,-->
<!--        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) devNum,-->
<!--        ifnull( it.make_num, 0 ) tempFixNum,-->
<!--        so.order_type,-->
<!--        soi.aux_uom_id,-->
<!--        soi.aux_uom_code,-->
<!--        soi.aux_uom_name,-->
<!--        soi.aux_uom_num,-->
<!--        it.uom_num,-->
<!--        0 as convert_numerator-->
<!--        FROM order_pur so,order_pur_item soi,	dm_delivery_plan_item it-->
<!--        WHERE-->
<!--        so.id = soi.pur_id-->
<!--        and it.sale_item_id=soi.id-->
<!--        and it.sale_id=so.id-->
<!--        and it.delete_Flag &lt;&gt; 2-->
<!--        AND soi.item_stat = 4-->
<!--&#45;&#45;         AND soi.delete_flag =0-->
<!--        AND soi.delivery_type=2-->
<!--        AND soi.is_close=0-->
<!--        AND soi.delete_Flag=0-->
<!--&#45;&#45;         AND soi.is_close = 0-->
<!--&#45;&#45;         AND soi.is_return_po = 0-->
<!--        <if test="params.sortMode != null and params.sortMode != ''">-->
<!--            <choose>-->
<!--                &lt;!&ndash; 可制单数排序 - 显示可制单数量大于0 &ndash;&gt;-->
<!--                <when test="params.sortMode == 3 or params.sortMode == '3'">-->
<!--                    and (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) > 0-->
<!--                </when>-->
<!--            </choose>-->
<!--        </if>-->
<!--        <if test="params.startRequiredDeliveryTime != null and params.startRequiredDeliveryTime != '' ">-->
<!--            <![CDATA[ and it.delivery_date >= STR_TO_DATE(#{params.startRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>-->
<!--        </if>-->
<!--        <if test="params.endRequiredDeliveryTime != null and params.endRequiredDeliveryTime != '' ">-->
<!--            <![CDATA[ and it.delivery_date <= STR_TO_DATE(#{params.endRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>-->
<!--        </if>-->
<!--        <if test="params.tenantId != null and params.tenantId != '' ">-->
<!--            and so.tenant_Id=#{params.tenantId}-->
<!--        </if>-->
<!--        <if test="params.vendorId != null and params.vendorId != '' ">-->
<!--            and so.vendor_id=#{params.vendorId}-->
<!--        </if>-->
<!--        <if test="params.deptId != null and params.deptId != '' ">-->
<!--            and so.dept_id=#{params.deptId}-->
<!--        </if>-->
<!--        <if test="params.purName != null and params.purName != '' ">-->
<!--            and so.pur_name=#{params.purName}-->
<!--        </if>-->
<!--    </sql>-->

    <!-- 按送货计划 -->
    <sql id="getDeliveryBoardByPlan">
        SELECT
        #{params.startDate} as startDate,
        so.pur_id,
        so.pur_name,
        so.dept_id,
        so.dept_name,
        so.dept_code,
        soi.order_price_uom,
        soi.werks,
        soi.price_uom,
        so.tenant_name tenantName,
        soi.delivery_type,
        so.id saleId,
        soi.id saleItemId,
        so.pur_no saleNo,
        it.plan_no planNo,
        so.order_date orderDate,
        soi.goods_id goodsId,
        soi.goods_code goodsCode,
        soi.goods_name goodsName,
        soi.goods_model goodsModel,
        soi.goods_erp_code goodsErpCode,
        soi.goods_class_name goodsClassName,
        soi.class_code goodsClassCode,
        soi.seq saleSeq,
        ifnull(it.drawing_no,'-') as drawingNo,
        it.match_num as orderNum,
        -- (soi.fix_num-soi.refund_num-soi.erp_reject_num) fixNum,
        ( SELECT IFNULL(SUM(dev_num),0) FROM dm_delivery dd,dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum,
        so.vendor_id,
        so.vendor_code vendorCode,
        so.vendor_name vendorName,
        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) waitNum,
        soi.uom_id uomId,
        soi.uom_code,
        soi.uom_name uomName,
        soi.gst_price gstPrice,
        soi.tax_price taxPrice,
        soi.gst_price*soi.order_num gstAmount,
        soi.currency_name currencyName,
        soi.currency_id currencyId,
        soi.rate_id rateId,
        soi.rate_val rateVal,
        soi.rate_name rateName,
        soi.taxes_type taxesType,
        soi.invoice_type invoiceType,
        so.order_type orderType,
        soi.purchase_remark remark,
        soi.change_count changeCount,
        so.stat,
        soi.seq,
        -- soi.creation_date,
        soi.is_close,
        it.plan_date replyDate,
        barcode_type barcodeType,
        it.purchaser_name purEmployee,
        soi.sale_employee_name saleEmployee,
        soi.soure_no sourceNo,
        it.delivery_date deliveryDate,
        it.warehouse_code warehouseCode,
        soi.warehouse_name warehouseName,
        soi.item_stat itemStat,
        soi.delivery_type deliveryType,
        it.collect_flag collectFlag,
        it.receiving_control receivingControl,
        so.vendor_delivery_address,
        so.shipping_address,
        it.id,
        it.id as list_id,
        it.id as plan_srm_line_id,
        IFNULL(it.refund_num,0) refund_num,
        IFNULL(it.un_competent_num,0) un_competent_num,
        soi.tenant_id tenantId,
        ifnull( it.make_num, 0 ) makeNum,
        --         canMakeNum剩余可创建送货单数 = 计划数 - 已制单数 + 暂退数 + 不合格数
        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) canMakeNum,
        (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) devNum,
        ifnull( it.make_num, 0 ) tempFixNum,so.order_type,
        soi.aux_uom_id,
        soi.aux_uom_code,
        soi.aux_uom_name,
        soi.aux_uom_num,
        it.uom_num,
        1 is_source_to_plan,
        0 convert_numerator,
        (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num
        FROM order_pur so,order_pur_item soi, dm_delivery_plan_item it
        WHERE
        so.id = soi.pur_id
        and it.sale_item_id=soi.id
        and it.sale_id=so.id
        and it.delete_Flag &lt;&gt; 2
--         AND soi.delete_flag =0
        and((so.change_count=0 and item_stat=4)or so.change_count>0 and item_stat in (1,4) )
        AND soi.delivery_type = 2
        and (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)) >0
        AND soi.is_close = 0
        AND soi.return_mark = 0
        <if test="params.startRequiredDeliveryTime != null and params.startRequiredDeliveryTime != '' ">
            <![CDATA[ and it.delivery_date >= STR_TO_DATE(#{params.startRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.endRequiredDeliveryTime != null and params.endRequiredDeliveryTime != '' ">
            <![CDATA[ and it.delivery_date <= STR_TO_DATE(#{params.endRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.tenantId != null and params.tenantId != '' ">
            and so.tenant_Id=#{params.tenantId}
        </if>
        <if test="params.vendorId != null and params.vendorId != '' ">
            and so.vendor_id=#{params.vendorId}
        </if>
        <if test="params.deptId != null and params.deptId != '' ">
            and so.dept_id=#{params.deptId}
        </if>
        <if test="params.purName != null and params.purName != '' ">
            and so.pur_name=#{params.purName}
        </if>
        <if test="params.goods != null and params.goods != ''">
            and (
                    it.goods_erp_code LIKE CONCAT('%',#{params.goods},'%')
                    OR it.goods_name LIKE CONCAT('%',#{params.goods},'%')
                    OR it.goods_model LIKE CONCAT('%',#{params.goods},'%')
                )
        </if>
        <if test="params.goodsList != null and params.goodsList.size()">
            AND it.goods_erp_code IN
            <foreach collection="params.goodsList" item="goods" separator="," open="(" close=")">
                #{goods}
            </foreach>
        </if>
    </sql>


    <!--获取送货明细的送货总量-->
    <select id="getDeliveryItemTotalQuantity" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT SUM(dev_num) AS deliveryQuantity , sale_item_id AS saleItemId , id AS deliveryItemId
        FROM dm_delivery_item
        <where>
            delete_flag = 0
            <if test="params.id != null and params.id != '' ">
                AND de_id=#{params.id} GROUP BY sale_item_id
            </if>
        </where>
    </select>


    <select id="getDeliveryItemAndOrderSaleListMap" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT ordersale.order_num AS orderSum,/*总数量*/
        ordersale.make_num AS makeNum,/*制单数量*/
        delivery.sale_item_id AS saleItemId
        FROM
        dm_delivery_item AS delivery,
        order_pur_item AS ordersale
        <where>
            delivery.sale_item_id=ordersale.id AND delivery.delete_flag = 0 AND ordersale.delete_flag = 0
            <if test="params.id != null and params.id != '' ">
                AND delivery.de_id=#{params.id} GROUP BY delivery.sale_item_id
            </if>
        </where>
    </select>

    <select id="getPlanNum" resultType="java.util.HashMap" parameterType="String">
        select ifnull(sum(sdi.match_num),0) planNum from dm_delivery_plan_item sdi where  sdi.sale_item_id=#{saleItemId}
    </select>

    <select id="findByDeNoInfo" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT
        dd.tenant_p_id deliveryTenantPId,/*送货单号*/
        dd.tenant_id deliveryTenantId, /* 送货主表采购方id */
        dd.id deliveryId,
        dd.de_no deNo,/*送货单号*/
        dd.vendor_id deliveryVendorId, /* 送货主表供应商id */
        dd.vendor_code deliveryVendorCode, /* 送货主表供应商id */
        dd.vendor_name deliveryVendorName, /* 送货主表供应商id */
        dd.dept_name deptName,
        dd.is_print isPrint,
        dd.print_count printCount,
        dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
        dd.is_ele isEle,
        dd.logistics_name logisticsName,
        dd.ele_no eleNo,
        dd.remark,
        dd.delete_flag deleteFlag,
        dd.see,
        dd.tenant_name tenantName,/*客户名称*/
        dd.create_id createId,
        dd.creater,
        dd.create_date createDate,
        dd.modifi_id modifiId,
        dd.modifier,
        dd.modify_date modifyDate,
        dd.delivery_date deliveryDate,
        dd.document_date documentDate,
        /* dd.sap_de_no sapDeNo,*/
        ddi.tenant_p_id deliveryItemTenantPId,
        ddi.tenant_id deliveryItemTenantId,
        ddi.de_id deId,
        ddi.sale_id saleId,
        ddi.sale_item_id saleItemId,
        ddi.sale_no saleNo,
        ddi.id deliveryItemId,
        ddi.seq seq,/*序号*/
        ddi.delivery_date deliveryDate,/*送货日期*/
        ddi.taxes_type taxesType,
        ddi.invoice_type invoiceType,
        ddi.tem_date temDate,
        ddi.goods_id goodsId,
        ddi.goods_erp_code goodsErpCode,
        ddi.goods_code goodsCode,
        ddi.goods_name goodsName,
        ddi.goods_model goodsModel,
        ddi.goods_class_name goodsClassName,
        ddi.goods_class_code goodsClassCode,
        ddi.sale_seq saleSeq,
        ddi.uom_id uomId,
        ddi.uom_code uomCode,
        ddi.uom_name uomName,/*单位*/
        ddi.rate_id rateId,
        ddi.rate_name rateName,/*税别*/
        ddi.rate_val rateVal,/*税率*/
        ddi.currency_id currencyId,
        ddi.currency_name currencyName,/*币别*/
        ddi.pur_employee_name purEmployee,/*采购方业务*/
        ddi.sale_employee_name saleEmployee,/*销售方业务*/
        ddi.soure_no sourceNo,/*来源单号*/
        ddi.gst_price gstPrice,/*含税单价*/
        ddi.tax_price taxPrice,/*不含税单价*/
        ddi.doc_name docName,
        ddi.doc_url docUrl,
        ddi.is_print deliveryItemIsPrint,
        ddi.print_count deliveryItemPrintCount,
        ddi.remark deliveryItemRemark,
        ddi.delete_flag deliveryItemDeleteFlag,
        ddi.big_pack_standard_num bigPackStandardNum,
        ddi.small_pack_standard_num smallPackStandardNum,
        ddi.big_pack_label_num bigPackLabelNum,
        ddi.small_pack_mantissa smallPackMantissa,
        ddi.purchase_confirm purchaseConfirm,
        ddi.vendor_doc_url vendorDocUrl,/* 供货方文档URL */
        ddi.vendor_doc_name vendorDocName,
        ddi.gst_amount gstAmount,
        ddi.tax_amount taxAmount,
        ddi.order_type orderType,
        ddi.quality_type qualityType,
        ddi.werks,
        ddi.order_price_uom orderPriceUom,
        ddi.price_uom priceUom,
        ddi.dev_num devNum,/*已送货数量*/
        ddi.tem_num temNum,/*暂收数量*/
        ddi.un_num unNum,/*未收数量*/
        ddi.order_num orderNum, /*测试字段 - 总数量*/
        ddi.inv_num invNum,/*验收已入库数量*/
        ddi.un_inv_num unInvNum,/*未验收数量*/
        ddi.barcode_type barcodeType,
        ddi.small_pack_label_num smallPackLabelNum,
        ddi.big_pack_mantissa bigPackMantissa,
        ddi.warehouse_code warehouseCode,
        ddi.warehouse_name warehouseName, /*仓库名称*/
        ddi.plan_item_id planItemId /*sap字段*/
        FROM
        dm_delivery dd,dm_delivery_item ddi
        <where>
            and dd.id=ddi.de_id
            and dd.delete_flag = 0
            and ddi.delete_flag = 0
            /*采购方条件查询*/
            <if test="params.tenantId != null and params.tenantId != '' ">
                and dd.tenant_id=#{params.tenantId}
            </if>
            <if test="params.deNo != null and params.deNo != '' ">
                and dd.de_no like CONCAT('%',#{params.deNo},'%')
            </if>
        </where>
        order by dd.id desc ,ddi.seq desc
    </select>

    <select id="findByDeNoAddCompromise" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        dd.tenant_p_id deliveryTenantPId,/*送货单号*/
        dd.tenant_id deliveryTenantId, /* 送货主表采购方id */
        dd.id deliveryId,
        dd.de_no deNo,/*送货单号*/
        dd.vendor_id deliveryVendorId, /* 送货主表供应商id */
        dd.vendor_code deliveryVendorCode, /* 送货主表供应商id */
        dd.vendor_name deliveryVendorName, /* 送货主表供应商id */
        dd.dept_name deptName,
        dd.is_print isPrint,
        dd.print_count printCount,
        dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
        dd.is_ele isEle,
        dd.logistics_name logisticsName,
        dd.ele_no eleNo,
        dd.remark,
        dd.delete_flag deleteFlag,
        dd.see,
        dd.tenant_name tenantName,/*客户名称*/
        dd.create_id createId,
        dd.creater,
        dd.create_date createDate,
        dd.modifi_id modifiId,
        dd.modifier,
        dd.modify_date modifyDate,
        dd.delivery_date deliveryDate,
        dd.document_date documentDate,
        /*dd.sap_de_no sapDeNo,*/
        ddi.tenant_p_id deliveryItemTenantPId,
        ddi.tenant_id deliveryItemTenantId,
        ddi.de_id deId,
        ddi.sale_id saleId,
        ddi.sale_item_id saleItemId,
        ddi.sale_no saleNo,
        ddi.id deliveryItemId,
        ddi.seq seq,/*序号*/
        ddi.delivery_date deliveryDate,/*送货日期*/
        ddi.taxes_type taxesType,
        ddi.invoice_type invoiceType,
        ddi.tem_date temDate,
        ddi.goods_id goodsId,
        ddi.goods_erp_code goodsErpCode,
        ddi.goods_code goodsCode,
        ddi.goods_name goodsName,
        ddi.goods_model goodsModel,
        ddi.goods_class_name goodsClassName,
        ddi.goods_class_code goodsClassCode,
        ddi.sale_seq saleSeq,
        ddi.uom_id uomId,
        ddi.uom_code uomCode,
        ddi.uom_name uomName,/*单位*/
        ddi.rate_id rateId,
        ddi.rate_name rateName,/*税别*/
        ddi.rate_val rateVal,/*税率*/
        ddi.currency_id currencyId,
        ddi.currency_name currencyName,/*币别*/
        ddi.pur_employee_name purEmployee,/*采购方业务*/
        ddi.sale_employee_name saleEmployee,/*销售方业务*/
        ddi.soure_no sourceNo,/*来源单号*/
        ddi.gst_price gstPrice,/*含税单价*/
        ddi.tax_price taxPrice,/*不含税单价*/
        ddi.doc_name docName,
        ddi.doc_url docUrl,
        ddi.is_print deliveryItemIsPrint,
        ddi.print_count deliveryItemPrintCount,
        ddi.remark deliveryItemRemark,
        ddi.delete_flag deliveryItemDeleteFlag,
        ddi.big_pack_standard_num bigPackStandardNum,
        ddi.small_pack_standard_num smallPackStandardNum,
        ddi.big_pack_label_num bigPackLabelNum,
        ddi.small_pack_mantissa smallPackMantissa,
        ddi.purchase_confirm purchaseConfirm,
        ddi.vendor_doc_url vendorDocUrl,/* 供货方文档URL */
        ddi.vendor_doc_name vendorDocName,
        ddi.gst_amount gstAmount,
        ddi.tax_amount taxAmount,
        ddi.order_type orderType,
        ddi.quality_type qualityType,
        ddi.werks,
        ddi.order_price_uom orderPriceUom,
        ddi.price_uom priceUom,
        ddi.dev_num devNum,/*已送货数量*/
        ddi.tem_num temNum,/*暂收数量*/
        ddi.un_num unNum,/*未收数量*/
        ddi.order_num orderNum, /*测试字段 - 总数量*/
        ddi.inv_num invNum,/*验收已入库数量*/
        ddi.un_inv_num unInvNum,/*未验收数量*/
        ddi.barcode_type barcodeType,
        ddi.small_pack_label_num smallPackLabelNum,
        ddi.big_pack_mantissa bigPackMantissa,
        ddi.warehouse_code warehouseCode,
        ddi.warehouse_name warehouseName, /*仓库名称*/
        ddi.plan_item_id planItemId /*sap字段*/
        from
        dm_delivery dd,dm_delivery_item ddi
        <where>
            and dd.id=ddi.de_id
            <if test="params.deNo != null and params.deNo != '' ">
                and dd.de_no = #{params.deNo}
            </if>
        </where>
        order by dd.id desc ,ddi.seq desc
    </select>

    <select id="findByDeNoJudge" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        select
        dd.tenant_p_id deliveryTenantPId,/*送货单号*/
        dd.tenant_id deliveryTenantId, /* 送货主表采购方id */
        dd.id deliveryId,
        dd.de_no deNo,/*送货单号*/
        dd.vendor_id deliveryVendorId, /* 送货主表供应商id */
        dd.vendor_code deliveryVendorCode, /* 送货主表供应商id */
        dd.vendor_name deliveryVendorName, /* 送货主表供应商id */
        dd.dept_name deptName,
        dd.is_print isPrint,
        dd.print_count printCount,
        dd.de_stat deStat,/*单据状态:1-送货制单;2-提交送货;3暂收入库*/
        dd.is_ele isEle,
        dd.logistics_name logisticsName,
        dd.ele_no eleNo,
        dd.remark,
        dd.delete_flag deleteFlag,
        dd.see,
        dd.tenant_name tenantName,/*客户名称*/
        dd.create_id createId,
        dd.creater,
        dd.create_date createDate,
        dd.modifi_id modifiId,
        dd.modifier,
        dd.modify_date modifyDate,
        dd.delivery_date deliveryDate,
        dd.document_date documentDate,
        /*dd.sap_de_no sapDeNo,*/
        dd.is_compromise,/*是否让步接收：0_否 ，1_是*/
        ddi.tenant_p_id deliveryItemTenantPId,
        ddi.tenant_id deliveryItemTenantId,
        ddi.de_id deId,
        ddi.sale_id saleId,
        ddi.sale_item_id saleItemId,
        ddi.sale_no saleNo,
        ddi.id deliveryItemId,
        ddi.seq seq,/*序号*/
        ddi.delivery_date deliveryDate,/*送货日期*/
        ddi.taxes_type taxesType,
        ddi.invoice_type invoiceType,
        ddi.tem_date temDate,
        ddi.goods_id goodsId,
        ddi.goods_erp_code goodsErpCode,
        ddi.goods_code goodsCode,
        ddi.goods_name goodsName,
        ddi.goods_model goodsModel,
        ddi.goods_class_name goodsClassName,
        ddi.goods_class_code goodsClassCode,
        ddi.sale_seq saleSeq,
        ddi.uom_id uomId,
        ddi.uom_code uomCode,
        ddi.uom_name uomName,/*单位*/
        ddi.rate_id rateId,
        ddi.rate_name rateName,/*税别*/
        ddi.rate_val rateVal,/*税率*/
        ddi.currency_id currencyId,
        ddi.currency_name currencyName,/*币别*/
        ddi.pur_employee_name purEmployee,/*采购方业务*/
        ddi.sale_employee_name saleEmployee,/*销售方业务*/
        ddi.soure_no sourceNo,/*来源单号*/
        ddi.gst_price gstPrice,/*含税单价*/
        ddi.tax_price taxPrice,/*不含税单价*/
        ddi.doc_name docName,
        ddi.doc_url docUrl,
        ddi.is_print deliveryItemIsPrint,
        ddi.print_count deliveryItemPrintCount,
        ddi.remark deliveryItemRemark,
        ddi.delete_flag deliveryItemDeleteFlag,
        ddi.big_pack_standard_num bigPackStandardNum,
        ddi.small_pack_standard_num smallPackStandardNum,
        ddi.big_pack_label_num bigPackLabelNum,
        ddi.small_pack_mantissa smallPackMantissa,
        ddi.purchase_confirm purchaseConfirm,
        ddi.vendor_doc_url vendorDocUrl,/* 供货方文档URL */
        ddi.vendor_doc_name vendorDocName,
        ddi.gst_amount gstAmount,
        ddi.tax_amount taxAmount,
        ddi.order_type orderType,
        ddi.quality_type qualityType,
        ddi.werks,
        ddi.order_price_uom orderPriceUom,
        ddi.price_uom priceUom,
        ddi.dev_num devNum,/*已送货数量*/
        ddi.tem_num temNum,/*暂收数量*/
        ddi.un_num unNum,/*未收数量*/
        ddi.order_num orderNum, /*测试字段 - 总数量*/
        ddi.inv_num invNum,/*验收已入库数量*/
        ddi.un_inv_num unInvNum,/*未验收数量*/
        ddi.barcode_type barcodeType,
        ddi.small_pack_label_num smallPackLabelNum,
        ddi.big_pack_mantissa bigPackMantissa,
        ddi.warehouse_code warehouseCode,
        ddi.warehouse_name warehouseName, /*仓库名称*/
        ddi.plan_item_id planItemId, /*sap字段*/
        ddi.is_compromise itemIsCompromise,/*送货单明细行让步接收标识：0_否 ，1_是*/
        ddi.is_compromise_create itemIsCompromiseCreate,/*是否创建让步接收单：0_未创建，1_已创建*/
        ddi.ret_num retNum,
        ddi.compromise_num compromiseNum,
        ddi.submit_stat submitStat,
        ddi.competent_num competentNum,
        ddi.un_competent_num unCompetentNum
        from
        dm_delivery dd,dm_delivery_item ddi
        <where>
            and dd.id=ddi.de_id
            <if test="params.tenantId != null and params.tenantId != ''">
                and dd.tenant_id = #{params.tenantId}
            </if>
            <if test="params.deNo != null and params.deNo != '' ">
                and dd.de_no = #{params.deNo}
            </if>
            <if test="params.saleNo != null and params.saleNo != ''">
                and ddi.sale_no = #{params.saleNo}
            </if>
            <if test="params.saleSeq != null and params.saleSeq != ''">
                and ddi.sale_seq = #{params.saleSeq}
            </if>
            <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
                and ddi.goods_erp_code = #{params.goodsErpCode}
            </if>
        </where>
        order by dd.id desc ,ddi.seq desc
    </select>

    <select id="getStartDateAndWaitNum" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        select * from ( SELECT reply_date as startDate
        FROM order_pur_item
        WHERE wait_num>0 AND item_stat =4
--         AND reply_date IS NOT null
        <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
            and goods_erp_code like CONCAT('%',#{params.goodsErpCode},'%')
            <if test='params.goodsErpCode.contains(",")'>
                or goods_erp_code in
                <foreach item="item" index="index" collection='params.goodsErpCode.split(",")' open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            or goods_name like CONCAT('%',#{params.goodsErpCode},'%')
        </if>
        AND pur_id IN (
        SELECT id
        FROM order_pur
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                and tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                and vendor_id = #{params.vendorId}
            </if>
        </where>
        <if test="params.purNo != null and params.purNo != ''">
            and pur_no like CONCAT('%',#{params.purNo},'%')
        </if>
        ) UNION select plan_date as startDate from dm_delivery_plan_item
        where delete_flag=0 and plan_date IS NOT null
        <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
            and goods_erp_code like CONCAT('%',#{params.goodsErpCode},'%')
            <if test='params.goodsErpCode.contains(",")'>
                or goods_erp_code in
                <foreach item="item" index="index" collection='params.goodsErpCode.split(",")' open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            or goods_name like CONCAT('%',#{params.goodsErpCode},'%')
        </if>
        AND sale_id IN (
        SELECT id
        FROM order_pur
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                and tenant_id = #{params.tenantId}
            </if>
            <if test="params.vendorId != null and params.vendorId != ''">
                and vendor_id = #{params.vendorId}
            </if>
        </where>
        )) od
        ORDER BY od.startDate asc LIMIT 1
    </select>
    <select id="getBuyer" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        select user_code,user_name,user_mobile from sys_user where id in(
            select pur_id from base_vendor where soure_id  = (
              SELECT s.soure_id  from base_vendor s WHERE s.vendor_code=#{params.vendorCode} and s.tenant_id = #{params.tenantId}
            )
        )
    </select>

    <select id="standardLastUpdateTimeDeliverySearch" resultMap="deliveryMap">
        <include refid="querySqlExport"/>
        from dm_delivery de left join dm_delivery_item it
        on de.id = it.de_id
        where de.tenant_id=#{params.tenantId}
        AND de.de_stat =2
        <if test="params.lastUpdateTime != null and params.lastUpdateTime != ''">
            <![CDATA[
              and de.modify_date >=STR_TO_DATE(#{params.lastUpdateTime},'%Y-%m-%d %H:%i:%s')
           ]]>
        </if>
        order by de.modify_date desc
    </select>

    <!-- 按计划送货气泡数 -->
    <select id="getDeliveryCount" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        SELECT
        COUNT(*) AS deliveryHomeCount
        FROM
        order_pur so,order_pur_item soi,dm_delivery_plan_item it
        WHERE
        so.id = soi.pur_id
        and it.sale_item_id=soi.id
        and it.sale_id=so.id
        and it.delete_Flag&lt;&gt;2
        AND((so.change_count=0 AND soi.item_stat=4) OR so.change_count>0 AND soi.item_stat in (1,4))
        AND soi.delete_flag =0
        <!--<if test="params.startDate != null and params.startDate != '' ">-->
        <!--and date_format(soi.reply_date,'%Y%m%d')&gt;=#{params.startDate}-->
        <!--</if>-->
        <if test="params.startRequiredDeliveryTime != null and params.startRequiredDeliveryTime != '' ">
            <![CDATA[ and it.plan_date >= STR_TO_DATE(#{params.startRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.endRequiredDeliveryTime != null and params.endRequiredDeliveryTime != '' ">
            <![CDATA[ and it.plan_date <= STR_TO_DATE(#{params.endRequiredDeliveryTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="params.deliveryType != null and params.deliveryType != '' ">
            and soi.delivery_type=#{params.deliveryType}
        </if>
        <if test="params.tenantId != null and params.tenantId != '' ">
            and so.tenant_id=#{params.tenantId}

        </if>
        <if test="params.vendorId != null and params.vendorId != '' ">
            and so.vendor_id=#{params.vendorId}
        </if>
        <if test="params.searchStr != null and params.searchStr != '' ">
            and (so.pur_no like CONCAT('%',#{params.searchStr},'%')
            or so.vendor_code like CONCAT('%',#{params.searchStr},'%')
            or so.vendor_name like CONCAT('%',#{params.searchStr},'%')
            or soi.goods_erp_code like CONCAT('%',#{params.searchStr},'%')
            or soi.goods_name like CONCAT('%',#{params.searchStr},'%')
            or soi.goods_model like CONCAT('%',#{params.searchStr},'%')
            <if test='params.searchStr.contains(",")'>
                or soi.goods_erp_code in
                <foreach item="item" index="index" collection='params.searchStr.split(",")' open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        <if test="params.orderNo != null and params.orderNo != '' ">
            and (so.pur_no like CONCAT('%',#{params.orderNo},'%'))
        </if>
        <if test="params.dept != null and params.dept != '' ">
            and (so.dept_name like CONCAT('%',#{params.dept},'%'))
        </if>
        <if test="params.sortMode != null and params.sortMode != ''">
            <choose>
                <!-- 默认序号排序 -->
                <when test="params.sortMode == 1 or params.sortMode == '1'">
                    order by so.id desc , soi.seq desc
                </when>
                <!-- 物料排序 -->
                <when test="params.sortMode == 2 or params.sortMode == '2'">
                    ORDER BY soi.goods_erp_code desc
                </when>
                <!-- 可制单数排序 - 显示可制单数量大于0 -->
                <when test="params.sortMode == 3 or params.sortMode == '3'">
                    and (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)+ifnull(it.refund_num, 0 )+ifnull(it.un_competent_num, 0 )) > 0
                    ORDER BY (ifnull(it.match_num, 0 )-ifnull(it.make_num, 0)+ifnull(it.refund_num, 0 )+ifnull(it.un_competent_num, 0 )) > 0 DESC
                </when>
            </choose>
        </if>
    </select>

    <sql id="querySqlExport">
      SELECT
        de.id  as  delivery_id,
        de.de_no,
        de.vendor_id,
        de.vendor_code,
        de.vendor_name,
        de.dept_name,
        de.is_print,
        de.print_count,
        de.de_stat,
        de.is_ele,
        de.delivery_date,
        de.document_date,
        de.logistics_name,
        de.ele_no,
        de.remark,
        de.delete_flag,
        de.see,
        de.tenant_name,
        de.create_id,
        de.creater,
        de.create_date,
        de.modifi_id,
        de.modifier,
        de.modify_date,
        it.de_id,
        it.sale_id,
        it.sale_no,
        it.sale_item_id,
        it.id,
        it.goods_id,
        it.goods_erp_code,
        it.goods_code,
        it.goods_name,
        it.goods_model,
        it.uom_id,
        it.uom_name,
        it.rate_id,
        it.rate_name,
        it.rate_val,
        it.currency_id,
        it.currency_name,
        it.taxes_type,
        it.invoice_type,
        it.gst_price,
        it.tax_price,
        it.tem_date,
        it.dev_num,
        it.tem_num,
        it.un_num,
        it.inv_num,
        it.un_inv_num,
        it.order_num,
        it.barcode_type,
        it.doc_name,
        it.doc_url,
        it.is_print,
        it.print_count,
        it.remark,
        it.delete_flag,
        it.seq,
        it.big_pack_standard_num,
        it.small_pack_standard_num,
        it.big_pack_label_num,
        it.small_pack_label_num,
        it.big_pack_mantissa,
        it.small_pack_mantissa,
        it.purchase_confirm,
        it.vendor_doc_url,
        it.vendor_doc_name,
        it.gst_amount,
        it.tax_amount,
        it.soure_no,
        it.pur_employee_name,
        it.sale_employee_name,
        it.goods_class_name,
        it.warehouse_code,
        it.warehouse_name,
        it.order_type,
        it.quality_type,
        it.plan_item_id
    </sql>

    <select id="findVendorByCode" resultType="java.util.Map">
        select
          id,vendor_name,vendor_code,pur_id,vendor_full_name
        from
          base_vendor
        where
          vendor_erp_code = #{params.vendorCode} and tenant_id = #{params.tenantId};
    </select>
    <select id="findUserEmail" parameterType="java.util.Map" resultType="java.util.Map">
        select
        id,tenant_id,dept_id,user_code,IFNULL(user_name,'') user_name,IFNULL(user_mobile,'') user_mobile,user_email
        from
        sys_user
        where
        tenant_id = #{params.tenantId}
        <if test="params.purId">
            and id = #{params.purId}
        </if>
    </select>

    <select id="findGoodsDrawingNo" resultType="java.util.Map">
        select
        goods_erp_code,goods_name,goods_model,drawing_no
        from
        base_goods
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
               and tenant_id = #{params.tenantId}
            </if>
            <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
               and goods_erp_code = #{params.goodsErpCode}
            </if>
        </where>
    </select>

    <select id="findVendorGoodsDeliveryByGoodsErpCode" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        dd.tenant_id,dd.de_no,dd.id,dd.vendor_id,dd.vendor_id,dd.vendor_code,dd.vendor_name,dd.de_stat,
        dd.delivery_date as deliveryDate,
        ddi.goods_erp_code,ddi.goods_id,ddi.goods_name,ddi.goods_model,ddi.dev_num,ddi.sale_no,ddi.sale_seq,
        ddi.drawing_no,ddi.delivery_date as requirementDeliveryDate,ddi.inv_num
        FROM
        dm_delivery dd,dm_delivery_item ddi
        WHERE
        dd.id = ddi.de_id
        <if test="params.tenantIds != null and params.tenantIds != ''">
            AND dd.tenant_id = #{params.tenantIds}
            <if test="params.vendorId != null and params.vendorId != ''">
                AND dd.vendor_id = #{params.vendorId}
            </if>
            <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
                AND ddi.goods_erp_code = #{params.goodsErpCode}
            </if>
            <if test="params.planDate != null and params.planDate != ''">
                <![CDATA[ and ddi.delivery_date = STR_TO_DATE(#{params.planDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
        </if>
        <if test="params.vendorIds != null and params.vendorIds != ''">
            AND dd.vendor_id = #{params.vendorIds}
            <if test="params.goodsErpCode != null and params.goodsErpCode != ''">
                AND ddi.goods_erp_code = #{params.goodsErpCode}
            </if>
            <if test="params.planDate != null and params.planDate != ''">
                <![CDATA[ and ddi.delivery_date = STR_TO_DATE(#{params.planDate},'%Y-%m-%d %H:%i:%s') ]]>
            </if>
        </if>
        order by dd.id desc
    </select>


    <select id="dataList" resultType="java.util.HashMap">
        SELECT '送货单'as type, dl.vendor_code,dl.vendor_name,dl.tenant_name,it.goods_erp_code,it.drawing_no,it.dev_num,0
        as un_competent_num, dl.de_no
        FROM dm_delivery dl,dm_delivery_item it
        WHERE dl.id = it.de_id
        <if test="params.saleItemId != null and params.saleItemId != ''">
            and it.sale_item_id=#{params.saleItemId}
        </if>
        <if test="params.id != null and params.id != ''">
            AND it.plan_srm_line_id=#{params.id}
        </if>
        UNION all
        select '送货计划'as type, it.vendor_code,it.vendor_name,
        (select pr.tenant_name from order_pur pr where pr.id = it.sale_id ) as tenant_name
        ,it.goods_erp_code,it.drawing_no, 0 as dev_num , ifnull(it.un_competent_num,0) as un_competent_num,it.plan_no as
        de_no from dm_delivery_plan_item it
        <where>
            <if test="params.id != null and params.id != ''">
                AND it.id=#{params.id}
            </if>
        </where>
    </select>

    <select id="data" resultType="java.util.HashMap">
        select ifnull(dd.dev_total,0) as dev_total , dd.un_competent_num ,(ifnull(dd.dev_total,0)-dd.un_competent_num)
        as actual_num from ( SELECT
        (select sum(ifnull(it.dev_num,0)) from dm_delivery_item it
        <where>
            <if test="params.saleItemId != null and params.saleItemId != ''">
                and it.sale_item_id=#{params.saleItemId}
            </if>
            <if test="params.id != null and params.id != ''">
                AND it.plan_srm_line_id=#{params.id}
            </if>
        </where>
        ) as dev_total,
        (select sum(ifnull(it.un_competent_num,0)) from dm_delivery_plan_item it
        <where>
            <if test="params.id != null and params.id != ''">
                AND it.id=#{params.id}
            </if>
        </where>
        ) as un_competent_num
        from dm_delivery_plan_item it
        <where>
            <if test="params.id != null and params.id != ''">
                AND it.id=#{params.id}
            </if>
        </where>
        ) dd
    </select>

    <select id="listDeli" resultType="java.util.HashMap">
        SELECT '送货单'as type,
        dl.vendor_code,dl.vendor_name,dl.tenant_name,it.goods_erp_code,it.drawing_no,it.dev_num,dl.de_no
        FROM dm_delivery dl,dm_delivery_item it
        WHERE dl.id = it.de_id
        <if test="params.saleItemId != null and params.saleItemId != ''">
            and it.sale_item_id=#{params.saleItemId}
        </if>
        <if test="params.id != null and params.id != ''">
            AND it.plan_srm_line_id=#{params.id}
        </if>
    </select>

    <select id="listOrder" resultType="java.util.HashMap">
        SELECT '送货单'as type,
        dl.vendor_code,dl.vendor_name,dl.tenant_name,it.goods_erp_code,it.drawing_no,it.dev_num,dl.de_no
        FROM dm_delivery dl,dm_delivery_item it
        WHERE dl.id = it.de_id
        <if test="params.saleItemId != null and params.saleItemId != ''">
            and it.sale_item_id=#{params.saleItemId}
        </if>
        <if test="params.id != null and params.id != ''">
            AND it.plan_srm_line_id=#{params.id}
        </if>
    </select>

    <select id="dataOrder" resultType="java.util.HashMap">
    select
    (select sum( ifnull(di.dev_num,0)) from  dm_delivery_item di where di.sale_item_id= it.id)  dev_total,
    it.erp_reject_num as un_competent_num,
    it.make_num as  actual_num
    from   order_pur_item  it  where  it.id =#{params.saleItemId}

    </select>
    <select id="queryDeliveryTracking" resultType="java.util.HashMap">
     SELECT
          de.de_no,
            de.tenant_name,
            de.vendor_code,
            de.vendor_name,
          it.goods_erp_code,
          it.goods_model,
          it.goods_name,
          it.drawing_no,
          it.dev_num,
          it.id as item_id ,
          (SELECT
            IFNULL(SUM(ma.master_num), 0)
          FROM
            dm_master d,
            dm_master_item ma
          WHERE d.id = ma.master_id
            AND d.master_type = 3
            AND ma.de_item_id = it.id) AS master_num,
          (SELECT
            IFNULL(SUM(ma.master_num), 0)
          FROM
            dm_master d,
            dm_master_item ma
          WHERE d.id = ma.master_id
            AND d.master_type = 1
            AND ma.de_item_id = it.id) AS collect_num,
          (SELECT
            IFNULL(SUM(ma.master_num), 0)
          FROM
            dm_master d,
            dm_master_item ma
          WHERE d.id = ma.master_id
            AND d.master_type = 2
            AND ma.de_item_id = it.id) AS retreat_num,
          (SELECT
            IFNULL(SUM(li.competent_num), 0)
          FROM
            qua_sheet_head sh,
            qua_sheet_line li
          WHERE sh.id = li.sheet_head_id
            AND sh.delivery_item_id = it.id) AS competent_num,
          (SELECT
            IFNULL(SUM(li.un_competent_num), 0)
          FROM
            qua_sheet_head sh,
            qua_sheet_line li
          WHERE sh.id = li.sheet_head_id
            AND sh.delivery_item_id = it.id) AS un_competent_num
        FROM
          dm_delivery de,
          dm_delivery_item it
        WHERE de.id = it.de_id
          AND de.de_stat = 2
        <if test="params.vendorId != null and params.vendorId != ''">
            AND de.vendor_id = #{params.vendorId}
        </if>
         <if test="params.tenantId != null and params.tenantId != ''">
            AND de.tenant_id = #{params.tenantId}
        </if>
        <if test="params.keyword != null and params.keyword != '' ">
        and ( de.de_no like CONCAT('%',#{params.keyword},'%')
            OR de.vendor_code like CONCAT('%',#{params.keyword},'%')
           or de.vendor_name like CONCAT('%',#{params.keyword},'%')
            )
        </if>
           <if test="params.goodsErpCode != null and params.goodsErpCode != '' ">
        and (    it.goods_erp_code like CONCAT('%',#{params.keyword},'%')
               OR it.goods_model like CONCAT('%',#{params.keyword},'%')
               OR it.goods_name like CONCAT('%',#{params.keyword},'%')
            )
        </if>
         <if test="params.drawingNo != null and params.drawingNo != '' ">
        and    it.drawing_no like CONCAT('%',#{params.drawingNo},'%')
        </if>
        ORDER BY de.id DESC
    </select>
    <select id="getDeliveryCartList" resultType="java.util.HashMap">
        SELECT    COUNT(*) AS deliveryCartList FROM  dm_delivery_shop_cart ca  WHERE ca.vendor_id=
        #{params.vendorId}  AND ca.user_id=#{params.userId}
    </select>

    <select id="findDeliveryItemMergeList" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
          (@i:= @i+1) AS deSeq,dd.id,ddi.id as itemId,ddi.goods_erp_code,
          ddi.drawing_no,ddi.goods_name,cast(sum( ddi.dev_num ) AS DECIMAL(10,2)) devSumNum,
          ddi.goods_model
        FROM
          dm_delivery dd,dm_delivery_item ddi,(SELECT @i:=0) AS i
        WHERE
	      dd.id = ddi.de_id
        AND dd.id = #{params.id}
        GROUP BY ddi.goods_erp_code
        ORDER BY deSeq
    </select>

    <!-- 按送货单主表维度查询供应商送货达标率基础数据 -->
    <select id="queryDeCompRateBaseDataList" resultType="com.dian.modules.dm.vo.DeliveryCompRateReportVO">
        SELECT * FROM (
            SELECT vendor_id,vendor_code,vendor_name,COUNT(*) delivery_count
            FROM dm_delivery
            WHERE
                de_stat = 2 AND delete_flag = 0
                <if test="params.startDate != null and params.startDate != '' and params.endDate != null and params.endDate != ''">
                    AND delivery_date BETWEEN #{params.startDate} AND #{params.endDate}
                </if>
                <if test="params.vendor != null and params.vendor != ''">
                    AND (
                    vendor_code LIKE CONCAT('%',#{params.vendor},'%')
                    OR vendor_name LIKE CONCAT('%',#{params.vendor},'%')
                    )
                </if>
                <if test="params.deptIdList != null and params.deptIdList.size() > 0">
                    AND dept_id IN
                    <foreach collection="params.deptIdList" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
            GROUP BY vendor_id
        ) dcrb
        <where>
            <if test="params.deliveryCount != null and params.deliveryCount != ''">
                AND <![CDATA[ dcrb.delivery_count >= #{params.deliveryCount} ]]>
            </if>
        </where>
    </select>

    <!-- 按送货单明细行维度查询供应商送货达标率基础数据 -->
    <select id="queryDeItemCompRateBaseDataList" resultType="com.dian.modules.dm.vo.DeliveryCompRateReportVO">
        SELECT * FROM (
            SELECT dd.vendor_id,dd.vendor_code,dd.vendor_name,COUNT(*) delivery_count
            FROM dm_delivery dd,dm_delivery_item ddi
            WHERE dd.id = ddi.de_id
                AND dd.de_stat = 2
                AND dd.delete_flag = 0
            <if test="params.startDate != null and params.startDate != '' and params.endDate != null and params.endDate != ''">
                AND dd.delivery_date BETWEEN #{params.startDate} AND #{params.endDate}
            </if>
            <if test="params.vendor != null and params.vendor != ''">
                AND (
                    dd.vendor_code LIKE CONCAT('%',#{params.vendor},'%')
                    OR dd.vendor_name LIKE CONCAT('%',#{params.vendor},'%')
                )
            </if>
            <if test="params.deptIdList != null and params.deptIdList.size() > 0">
                AND dd.dept_id IN
                <foreach collection="params.deptIdList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            GROUP BY dd.vendor_id
        ) dcrb
        <where>
            <if test="params.deliveryCount != null and params.deliveryCount != ''">
                AND <![CDATA[ dcrb.delivery_count >= #{params.deliveryCount} ]]>
            </if>
        </where>
    </select>
</mapper>
