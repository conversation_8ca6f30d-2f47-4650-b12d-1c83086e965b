package com.dian.api.jindie;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.dian.api.common.param.*;
import com.dian.api.common.result.GeneralResult;
import com.dian.api.jindie.param.*;
import com.dian.api.jindie.result.GetOrderItemMatchedPlanNumResult;
import com.dian.api.mboSap.result.OrderResult;
import com.dian.client.base.BaseClient;
import com.dian.client.dm.DmClient;
import com.dian.client.order.OrderClient;
import com.dian.client.sm.SmClient;
import com.dian.client.sys.SysClient;
import com.dian.common.dto.UserDto;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.utils.BeanConverter;
import com.dian.common.validator.Assert;
import com.dian.enums.MasterTypeEnum;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.modules.base.query.GoodsReqVo;
import com.dian.modules.base.query.VendorReqVo;
import com.dian.modules.base.vo.*;
import com.dian.modules.dm.vo.*;
import com.dian.modules.enums.common.*;
import com.dian.modules.enums.dm.MasterSourceTypeEnum;
import com.dian.modules.enums.dm.ReturnMethodTypeEnum;
import com.dian.modules.order.query.PurQuery;
import com.dian.modules.order.query.SaleQuery;
import com.dian.modules.order.vo.PurEntityVO;
import com.dian.modules.order.vo.PurItemLineVO;
import com.dian.modules.order.vo.SaleItemVo;
import com.dian.modules.order.vo.SaleVo;
import com.dian.modules.sm.vo.PaymentRecItemVo;
import com.dian.modules.sm.vo.PaymentRecordVo;
import com.dian.modules.sys.vo.DeptVO;
import com.gitee.easyopen.ApiContext;
import com.gitee.easyopen.annotation.Api;
import com.gitee.easyopen.annotation.ApiService;
import com.gitee.easyopen.doc.annotation.ApiDoc;
import com.gitee.easyopen.doc.annotation.ApiDocMethod;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.log4j.Log4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApiService
@ApiDoc(value = "金蝶ERP对接接口", order = 3)
@Log4j
public class JinDieApi {
    protected Logger logger = TraceLoggerFactory.getLogger(JinDieApi.class);

    /**
     * 定义非销售订单相关出入库信息类型SET
     * 1-入库单 2-退货单 3-收料单 4-暂退单 5-手工入库单 6-手工退货单
     */
    private static final Set<Integer> NOT_SALE_TYPES = new HashSet<>(Arrays.asList(
            MasterTypeEnum.REC.getValue(),
            MasterTypeEnum.RET.getValue(),
            MasterTypeEnum.PROREC.getValue(),
            MasterTypeEnum.RETTEM.getValue(),
            MasterTypeEnum.SPECIAL.getValue(),
            MasterTypeEnum.MANRET.getValue()
    ));

    private static final Set<Integer> RETURN_TYPES = new HashSet<>(Arrays.asList(
            MasterTypeEnum.RET.getValue(),
            MasterTypeEnum.RETTEM.getValue(),
            MasterTypeEnum.MANRET.getValue()
    ));

    @Lazy
    @Autowired
    private BaseClient baseClient;

    @Lazy
    @Autowired
    private OrderClient orderClient;

    @Lazy
    @Autowired
    private DmClient dmClient;

    @Lazy
    @Autowired
    private SysClient sysClient;

    @Lazy
    @Autowired
    private SmClient smClient;


    /**
     * 根据金蝶ERP采购订单明细id查询SRM订单数据
     * @param param
     * @return
     */
    @Api(name = "jindie.order.getMatchedPlanNumByItemId")
    @ApiDocMethod(description = "根据金蝶ERP采购订单明细id查询SRM订单数据")
    @GlobalTransactional(rollbackFor = Exception.class)
    public GetOrderItemMatchedPlanNumResult getMatchedPlanNumByItemId(GetOrderItemMatchedPlanNumParam param) {
        GetOrderItemMatchedPlanNumResult getOrderItemMatchedPlanNumResult = new GetOrderItemMatchedPlanNumResult();
        if (!StrUtil.isEmptyIfStr(param.getErpOrderItemId())){
            PurItemLineVO purLineVo = orderClient.getPurItemVoBySourceItemId(param.getErpOrderItemId());
            if (purLineVo != null){
                if (purLineVo.getMatchedPlanNum() == null){
                    getOrderItemMatchedPlanNumResult.setMatchedPlanNum(BigDecimal.ZERO);
                } else {
                    getOrderItemMatchedPlanNumResult.setMatchedPlanNum(purLineVo.getMatchedPlanNum());
                }
            } else {
//                throw new RRException(String.format("ERP采购订单明细ID[%s],查询不到对应数据",param.getErpOrderItemId()));
                getOrderItemMatchedPlanNumResult.setMatchedPlanNum(BigDecimal.ZERO);
            }
        } else {
            throw new RRException("ERP采购订单明细id不能为空");
        }
        return getOrderItemMatchedPlanNumResult;
    }

    @Api(name = "jindie.order.sync")
    @ApiDocMethod(description = "金蝶ERP下发采购订单同步至SRM")
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderResult purOrderInsert(PurOrderParamsList params){
        // 开始时间
        long startTime = System.currentTimeMillis();
        if (ObjectUtil.isEmpty(params) || CollectionUtil.isEmpty(params.getList())){
            throw new RRException("采购订单数据不能为空");
        }
        // 获取采购订单明细为空的采购订单
        List<PurOrderParams> isEmptyDetailList = params.getList().stream().filter(purOrderParams -> CollectionUtil.isEmpty(purOrderParams.getDetailList())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isEmptyDetailList)){
            throw new RRException("采购订单明细存在为空的数据");
        }
        // 采购订单数据
        List<PurOrderParams> list = params.getList();
        // 获取供应商编码集合数据
        List<String> vendorErpCodeList = list.stream().filter(purOrderParams -> !StrUtil.isEmptyIfStr(purOrderParams.getVendorErpCode())).map(PurOrderParams::getVendorErpCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(vendorErpCodeList)){
            throw new RRException("存在供应商编码为空的数据");
        }
        // 获取用户编码(采购员编码)集合数据
        List<String> userCodeList = list.stream().filter(purOrderParams -> !StrUtil.isEmptyIfStr(purOrderParams.getPurCode())).map(PurOrderParams::getPurCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userCodeList)){
            throw new RRException("存在采购员编码为空数据");
        }
        // 获取部门编码集合数据
        List<String> deptCodeList = list.stream().filter(purOrderParams -> !StrUtil.isEmptyIfStr(purOrderParams.getPurDeptCode())).map(PurOrderParams::getPurDeptCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deptCodeList)){
            throw new RRException("存在采购部门编码为空数据");
        }
        // 查询供应商信息
        List<VendorVO> vendorVOList = queryVendorList(vendorErpCodeList);
        if (CollectionUtil.isEmpty(vendorVOList)){
            throw new RRException("当前接口传参中的供应商编码数据均找不到对应的数据");
        }
        // 查询用户信息
        List<UserDto> userDtoList = queryUserList(userCodeList);
        if (CollectionUtil.isEmpty(userDtoList)){
            throw new RRException("当前接口传参中的采购员编码数据均找不到对应的数据");
        }
        // 获取部门信息
        List<DeptVO> deptVOList = queryDeptList(deptCodeList);
        if (CollectionUtil.isEmpty(deptVOList)){
            throw new RRException("当前接口传参中的采购部门编码数据均找不到对应数据");
        }
        // 获取单位信息
        List<UomVO> uomVoList = queryUomList();
        if (CollectionUtil.isEmpty(uomVoList)){
            throw new RRException("系统中的单位信息为空");
        }
        List<PurEntityVO> orderVoList = new ArrayList<>();
        list.forEach(purOrderParams -> {
            if (StrUtil.isEmptyIfStr(purOrderParams.getOperationType())){
                throw new RRException(String.format("采购订单%s的操作类型不能为空", purOrderParams.getPurNo()));
            }
            if (StrUtil.isEmptyIfStr(purOrderParams.getOrderType())){
                throw new RRException(String.format("采购订单%s的订单类型不能为空", purOrderParams.getPurNo()));
            }
            if (ObjectUtil.isEmpty(purOrderParams.getOrderDate())){
                throw new RRException(String.format("采购订单%s的订单日期不能为空", purOrderParams.getPurNo()));
            }
            // 获取采购订单明细行序号为空的采购订单明细数据
            List<PurOrderDetailParam> isNullSeqItems = purOrderParams.getDetailList().stream().filter(purOrderDetailParam -> ObjectUtil.isEmpty(purOrderDetailParam.getSeq())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isNullSeqItems)){
                throw new RRException(String.format("采购订单%s的明细行序号存在为空的数据", purOrderParams.getPurNo()));
            }
            // 查询对应的供应商信息
            VendorVO vendor = vendorVOList.stream().filter(vendorVO -> vendorVO.getVendorErpCode().equals(purOrderParams.getVendorErpCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(vendor)){
                throw new RRException(String.format("当前接口传参中的供应商编码数据%s找不到对应数据", purOrderParams.getVendorErpCode()));
            }
            // 查询对应的用户信息
            UserDto user = userDtoList.stream().filter(userDto -> userDto.getUserCode().equals(purOrderParams.getPurCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(user)){
                throw new RRException(String.format("当前接口传参中的采购员编码数据%s找不到对应数据", purOrderParams.getPurCode()));
            }
            // 查询对应的部门信息
            DeptVO dept = deptVOList.stream().filter(deptVO -> deptVO.getDeptCode().equals(purOrderParams.getPurDeptCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(dept)){
                throw new RRException(String.format("当前接口传参中的采购部门编码数据%s找不到对应数据", purOrderParams.getPurDeptCode()));
            }
            PurEntityVO orderVo = assemblePurOrder(purOrderParams, vendor, user, dept);
            // 获取物料编码集合
            List<String> goodsErpCodeList = purOrderParams.getDetailList()
                    .stream()
                    .filter(orderDetail -> !StrUtil.isEmptyIfStr(orderDetail.getGoodsErpCode()))
                    .map(PurOrderDetailParam::getGoodsErpCode).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(goodsErpCodeList)){
                throw new RRException(String.format("采购订单号%s的明细中存在物料编码为空的数据", purOrderParams.getPurNo()));
            }
            // 查询当前采购订单所有物料信息
            List<GoodsVO> goodsVoList = this.queryGoodsList(goodsErpCodeList);
            if (CollectionUtil.isEmpty(goodsVoList)){
                throw new RRException(String.format("采购订单号%s下均查询不到对应的物料信息", purOrderParams.getPurNo()));
            }
            List<PurItemLineVO> purItemList = new ArrayList<>();
            purOrderParams.getDetailList().forEach(orderDetail -> {
                if (ObjectUtil.isEmpty(orderDetail.getPurItemId())){
                    throw new RRException(String.format("采购订单号%s的明细行序号为%s的行数据采购订单明细id不能为空", purOrderParams.getPurNo(),orderDetail.getSeq()));
                }
                if (ObjectUtil.isEmpty(orderDetail.getDeliveryDate())){
                    throw new RRException(String.format("采购订单号%s的序号为%s的行数据交货日期不能为空", purOrderParams.getPurNo(),orderDetail.getSeq()));
                }
                if (ObjectUtil.isEmpty(orderDetail.getOrderNum())){
                    throw new RRException(String.format("采购订单号%s的序号为%s的行数据订单数量为空", purOrderParams.getPurNo(),orderDetail.getSeq()));
                }
                // 查询订单行对应的物料信息
                GoodsVO goodsVO = goodsVoList.stream().filter(detail -> detail.getGoodsErpCode().equals(orderDetail.getGoodsErpCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(goodsVO)){
                    throw new RRException(String.format("当前接口传参中的采购订单号%s-序号%s的明细中物料编码%s找不到对应的数据", purOrderParams.getPurNo(),orderDetail.getSeq(),orderDetail.getGoodsErpCode()));
                }
                // 添加订单行数据
                purItemList.add(assemblePurOrderLine(orderVo,orderDetail,vendor,goodsVO,uomVoList));
            });
            orderVo.setPurItemEntityList(purItemList);
            orderVoList.add(orderVo);
        });
        // 调用新增/更新订单接口
        orderClient.synchronizePurOrderBySrm(orderVoList);
        // 结束时间
        long endTime = System.currentTimeMillis();
        // 计算执行时间
        logger.info("执行时长:"+(endTime - startTime)+" 毫秒.");
        return new OrderResult();
    }

    @Api(name = "jindie.sale.sync")
    @ApiDocMethod(description = "金蝶ERP下发销售订单同步至SRM")
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderResult saleOrderInsert(SaleOrderParamsList params){
        // 开始时间
        long startTime = System.currentTimeMillis();
        if (ObjectUtil.isEmpty(params) || CollectionUtil.isEmpty(params.getList())){
            throw new RRException("销售订单数据不能为空");
        }
        // 获取销售订单明细为空的数据
        List<SaleOrderParams> isEmptyDetailList = params.getList().stream().filter(orderParams -> CollectionUtil.isEmpty(orderParams.getDetailList())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isEmptyDetailList)){
            throw new RRException("销售订单明细存在为空的数据");
        }
        // 销售订单数据
        List<SaleOrderParams> list = params.getList();
        // 获取供应商编码集合数据
        List<String> customerCodeList = list.stream().filter(orderParams -> !StrUtil.isEmptyIfStr(orderParams.getCustomerCode())).map(SaleOrderParams::getCustomerCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerCodeList)){
            throw new RRException("存在供应商编码为空的数据");
        }
        // 获取用户编码(销售员编码)集合数据
        List<String> userCodeList = list.stream().filter(orderParams -> !StrUtil.isEmptyIfStr(orderParams.getSalesmanCode())).map(SaleOrderParams::getSalesmanCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userCodeList)){
            throw new RRException("存在销售员编码为空数据");
        }
        // 获取部门编码集合数据
        List<String> deptCodeList = list.stream().filter(orderParams -> !StrUtil.isEmptyIfStr(orderParams.getSaleDeptCode())).map(SaleOrderParams::getSaleDeptCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deptCodeList)){
            throw new RRException("存在销售部门编码为空数据");
        }
        // 查询客户编码对应的供应商数据
        List<VendorVO> vendorVOList = queryVendorList(customerCodeList);
        if (CollectionUtil.isEmpty(vendorVOList)){
            throw new RRException("当前接口传参中的客户编码数据均找不到对应的供应商数据");
        }
        // 查询用户信息
        List<UserDto> userDtoList = queryUserList(userCodeList);
        if (CollectionUtil.isEmpty(userDtoList)){
            throw new RRException("当前接口传参中的销售员编码数据均找不到对应的数据");
        }
        // 获取部门信息
        List<DeptVO> deptVOList = queryDeptList(deptCodeList);
        if (CollectionUtil.isEmpty(deptVOList)){
            throw new RRException("当前接口传参中的销售部门编码数据均找不到对应数据");
        }
        // 获取单位信息
        List<UomVO> uomVoList = queryUomList();
        if (CollectionUtil.isEmpty(uomVoList)){
            throw new RRException("系统中的单位信息为空");
        }
        List<SaleVo> orderVoList = new ArrayList<>();
        list.forEach(orderParams -> {
            if (StrUtil.isEmptyIfStr(orderParams.getOperationType())){
                throw new RRException(String.format("销售订单%s的操作类型不能为空", orderParams.getSaleNo()));
            }
            if (StrUtil.isEmptyIfStr(orderParams.getOrderType())){
                throw new RRException(String.format("销售订单%s的订单类型不能为空", orderParams.getSaleNo()));
            }
            if (ObjectUtil.isEmpty(orderParams.getOrderDate())){
                throw new RRException(String.format("销售订单%s的订单日期不能为空", orderParams.getSaleNo()));
            }
            // 获取销售订单明细行序号为空的明细数据
            List<SaleOrderDetailParam> isNullSeqItems = orderParams.getDetailList().stream().filter(orderDetailParam -> ObjectUtil.isEmpty(orderDetailParam.getSeq())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isNullSeqItems)){
                throw new RRException(String.format("销售订单%s的明细行序号存在为空的数据", orderParams.getSaleNo()));
            }
            // 查询对应的供应商信息
            VendorVO vendor = vendorVOList.stream().filter(vendorVO -> vendorVO.getVendorErpCode().equals(orderParams.getCustomerCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(vendor)){
                throw new RRException(String.format("当前接口传参中的供应商编码数据%s找不到对应的供应商数据", orderParams.getCustomerCode()));
            }
            // 查询对应的用户信息
            UserDto user = userDtoList.stream().filter(userDto -> userDto.getUserCode().equals(orderParams.getSalesmanCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(user)){
                throw new RRException(String.format("当前接口传参中的销售员编码数据%s找不到对应数据", orderParams.getSalesmanCode()));
            }
            // 查询对应的部门信息
            DeptVO dept = deptVOList.stream().filter(deptVO -> deptVO.getDeptCode().equals(orderParams.getSaleDeptCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(dept)){
                throw new RRException(String.format("当前接口传参中的销售部门编码数据%s找不到对应数据", orderParams.getSaleDeptCode()));
            }
            SaleVo orderVo = assembleSaleOrder(orderParams, vendor, user, dept);
            // 获取物料编码集合
            List<String> goodsErpCodeList = orderParams.getDetailList()
                    .stream()
                    .filter(orderDetail -> !StrUtil.isEmptyIfStr(orderDetail.getGoodsErpCode()))
                    .map(SaleOrderDetailParam::getGoodsErpCode).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(goodsErpCodeList)){
                throw new RRException(String.format("销售订单号%s的明细中存在物料编码为空的数据", orderParams.getSaleNo()));
            }
            // 查询当前采购订单所有物料信息
            List<GoodsVO> goodsVoList = this.queryGoodsList(goodsErpCodeList);
            if (CollectionUtil.isEmpty(goodsVoList)){
                throw new RRException(String.format("销售订单号%s下均查询不到对应的物料信息", orderParams.getSaleNo()));
            }
            List<SaleItemVo> itemList = new ArrayList<>();
            orderParams.getDetailList().forEach(orderDetail -> {
                if (ObjectUtil.isEmpty(orderDetail.getSaleItemId())){
                    throw new RRException(String.format("采购订单号%s的明细行序号为%s的行数据采购订单明细id不能为空", orderParams.getSaleNo(),orderDetail.getSeq()));
                }
                if (ObjectUtil.isEmpty(orderDetail.getDeliveryDate())){
                    throw new RRException(String.format("采购订单号%s的序号为%s的行数据交货日期不能为空", orderParams.getSaleNo(),orderDetail.getSeq()));
                }
                if (ObjectUtil.isEmpty(orderDetail.getOrderNum())){
                    throw new RRException(String.format("采购订单号%s的序号为%s的行数据订单数量为空", orderParams.getSaleNo(),orderDetail.getSeq()));
                }
                // 查询订单行对应的物料信息
                GoodsVO goodsVO = goodsVoList.stream().filter(detail -> detail.getGoodsErpCode().equals(orderDetail.getGoodsErpCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(goodsVO)){
                    throw new RRException(String.format("当前接口传参中的采购订单号%s-序号%s的明细中物料编码%s找不到对应的数据", orderParams.getSaleNo(),orderDetail.getSeq(),orderDetail.getGoodsErpCode()));
                }
                // 添加订单行数据
                itemList.add(assembleSaleOrderLine(orderVo,orderDetail,vendor,goodsVO,uomVoList));
            });
            orderVo.setSaleItemList(itemList);
            orderVoList.add(orderVo);
        });
        orderClient.batchSaveSaleOrderByErp(orderVoList);
        // 结束时间
        long endTime = System.currentTimeMillis();
        // 计算执行时间
        logger.info("执行时长:"+(endTime - startTime)+" 毫秒.");
        return new OrderResult();
    }

    @Api(name = "jindie.plan.sync")
    @ApiDocMethod(description = "金蝶ERP下发回货计划数据同步至SRM进行分配订单计划")
    @GlobalTransactional(rollbackFor = Exception.class)
    public GeneralResult planInsert(PlanMainParam data) {
        if (ObjectUtil.isEmpty(data) || CollectionUtil.isEmpty(data.getList())){
            throw new RRException("计划数据入参不能为空");
        }
        data.getList().forEach(param -> {
            DeliveryPlanHostVO deliveryPlanHostVO = new DeliveryPlanHostVO();
            if (ObjectUtil.isEmpty(param.getOperationType())){
                throw new RRException("操作类型不能为空");
            }
            if (CollectionUtil.isEmpty(param.getDetailList())){
                throw new RRException("计划数据不能为空");
            }
            // 获取供应商编码集合数据
            List<String> vendorErpCodeList = param.getDetailList().stream().filter(params -> !StrUtil.isEmptyIfStr(params.getVendorErpCode())).map(PlanItemParam::getVendorErpCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(vendorErpCodeList)){
                throw new RRException("存在供应商编码为空的数据");
            }
            // 获取用户编码(采购员编码)集合数据
            List<String> userCodeList = param.getDetailList().stream().filter(params -> !StrUtil.isEmptyIfStr(params.getPurCode())).map(PlanItemParam::getPurCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(userCodeList)){
                throw new RRException("存在采购员编码为空数据");
            }
            // 获取操作类型枚举
            InterfaceOperationEnumeration operationEnum = InterfaceOperationEnumeration.getByCode(param.getOperationType());
            deliveryPlanHostVO.setOperationType(operationEnum.getName());
            // 关闭送货计划范围日期不为空
            if (ObjectUtil.isNotEmpty(param.getCloseStartDate()) && ObjectUtil.isNotEmpty(param.getCloseEndDate())){
                deliveryPlanHostVO.setCloseStartDate(DateUtil.format(param.getCloseStartDate(),"yyyy-MM-dd")+" 00:00:00");
                deliveryPlanHostVO.setCloseEndDate(DateUtil.format(param.getCloseEndDate(),"yyyy-MM-dd")+" 23:59:59");
            }
            // 校验计划数据
            this.checkPlanParamData(param.getDetailList());
            // 获取物料编码集合
            List<String> goodsErpCodeList = param.getDetailList()
                    .stream()
                    .filter(orderDetail -> !StrUtil.isEmptyIfStr(orderDetail.getGoodsErpCode()))
                    .map(PlanItemParam::getGoodsErpCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(goodsErpCodeList)){
                throw new RRException(String.format("存在物料编码为空的数据"));
            }
            // 查询供应商信息
            List<VendorVO> vendorVOList = this.queryVendorList(vendorErpCodeList);
            if (CollectionUtil.isEmpty(vendorVOList)){
                throw new RRException(String.format("当前回货计划中的供应商信息均找不到对应数据"));
            }
            // 查询用户信息
            List<UserDto> userDtoList = this.queryUserList(userCodeList);
            if (CollectionUtil.isEmpty(userDtoList)){
                throw new RRException(String.format("当前回货计划中的采购员信息均找不到对应数据"));
            }
            // 查询物料信息
            List<GoodsVO> goodsVOList = this.queryGoodsList(goodsErpCodeList);
            if (CollectionUtil.isEmpty(goodsVOList)){
                throw new RRException(String.format("当前回货计划中的物料信息均找不到对应数据"));
            }
            List<DeliveryPlanVO> deliveryPlanVOList = new ArrayList<>();
            param.getDetailList().forEach(planItemParam -> {
                JinDieOrderTypeEnum orderTypeEnum = JinDieOrderTypeEnum.getEnumByName(planItemParam.getOrderType());
                DeliveryPlanVO deliveryPlanVO = new DeliveryPlanVO();
                deliveryPlanVO.setOrderType(orderTypeEnum.getValue());
                deliveryPlanVO.setPlanNo(planItemParam.getPlanNo());
                // 计划回货日期
                deliveryPlanVO.setPlanDate(planItemParam.getPlanDate());
                deliveryPlanVO.setDeliveryDate(deliveryPlanVO.getPlanDate());
                // TODO 目前冠宇达只有一个组织架构
                // MRP区域
                deliveryPlanVO.setMrpRegion("101");
                //采购组织信息
                deliveryPlanVO.setDeptId(28905L);
                deliveryPlanVO.setDeptCode("101");
                deliveryPlanVO.setDeptName("佛山市顺德区冠宇达电源有限公司");
                // 采购组
                deliveryPlanVO.setPurchaserGroup(planItemParam.getPurGroup());
                // 供应商
                VendorVO vendor = vendorVOList.stream().filter(vendorVO -> vendorVO.getVendorErpCode().equals(planItemParam.getVendorErpCode())).findFirst().orElse(null);
                Assert.isNull(vendor,String.format("查询不到供应商编码%s对应的数据", planItemParam.getVendorErpCode()));
                deliveryPlanVO.setVendorId(vendor.getSoureId());
                deliveryPlanVO.setVendorCode(vendor.getVendorErpCode());
                deliveryPlanVO.setVendorName(vendor.getVendorName());
                // 采购员信息
                UserDto userDto = userDtoList.stream().filter(userDto1 -> userDto1.getUserCode().equals(planItemParam.getPurCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(userDto)){
                    throw new RRException(String.format("查询不到采购员编码%s对应的数据", planItemParam.getPurCode()));
                }
                deliveryPlanVO.setPurchaserId(userDto.getId());
                deliveryPlanVO.setPurchaserName(userDto.getUserName());
                // 物料信息
                GoodsVO goodsVO = goodsVOList.stream().filter(goods -> goods.getGoodsErpCode().equals(planItemParam.getGoodsErpCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(goodsVO)){
                    throw new RRException(String.format("查询不到物料编码%s对应数据", planItemParam.getGoodsErpCode()));
                }
                deliveryPlanVO.setGoodsId(goodsVO.getId());
                deliveryPlanVO.setGoodsCode(goodsVO.getGoodsErpCode());
                deliveryPlanVO.setGoodsErpCode(goodsVO.getGoodsErpCode());
                deliveryPlanVO.setGoodsName(goodsVO.getGoodsName());
                deliveryPlanVO.setGoodsModel(goodsVO.getGoodsModel());
                // 计划数量
                deliveryPlanVO.setPlanNum(planItemParam.getPlanNum());
                // 缺料数
                deliveryPlanVO.setShortageNum(ObjectUtil.isNotEmpty(planItemParam.getShortageNum()) ? planItemParam.getShortageNum() : BigDecimal.ZERO);
                deliveryPlanVO.setOperationType(operationEnum.getValue());
                deliveryPlanVOList.add(deliveryPlanVO);
            });
            deliveryPlanHostVO.setDeliveryPlanVOList(deliveryPlanVOList);
            logger.info("需要进行分配计划的数据="+ JSONObject.toJSONString(deliveryPlanHostVO));
            dmClient.synchronousDistributionPlanBySAPOrErp(deliveryPlanHostVO);
        });
        return new GeneralResult();
    }

    @Api(name = "jindie.plan.syncV2")
    @ApiDocMethod(description = "金蝶ERP下发回货计划数据同步至SRM")
    @GlobalTransactional(rollbackFor = Exception.class)
    public GeneralResult planInsertV2(PlanMainV2Param data){
        if (ObjectUtil.isEmpty(data)){
            throw new RRException("参数不能为空");
        }
        if (CollectionUtil.isEmpty(data.getList())){
            throw new RRException("回货计划数据集合不能为空");
        }
        List<DeliveryPlanSaveVo> deliveryPlanSaveVoList = new ArrayList<>();
        data.getList().forEach(param  -> {
            DeliveryPlanSaveVo deliveryPlanSaveVo = new DeliveryPlanSaveVo();
            if (ObjectUtil.isEmpty(param.getOperationType())){
                throw new RRException("操作类型不能为空");
            }
            if (StrUtil.isEmptyIfStr(param.getPlanNo())){
                throw new RRException("回货计划单号不能为空");
            }
            if (ObjectUtil.isEmpty(param.getErpSourceType())){
                throw new RRException("ERP来源类型不能为空");
            }
            if (StrUtil.isEmptyIfStr(param.getCreaterCode())){
                throw new RRException("创建人编码不能为空");
            }
            if (CollectionUtil.isEmpty(param.getDetailList())){
                throw new RRException("回货计划明细数据集合不能为空");
            }
            // 校验ERP计划行ID是否存在为空的数据
            List<PlanItemV2Param> isNullPlanItemIds = param.getDetailList().stream().filter(planItemV2Param -> ObjectUtil.isEmpty(planItemV2Param.getErpDocRowId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isNullPlanItemIds)){
                throw new RRException("回货计划明细数据集合中存在ERP计划行ID为空的数据");
            }
            // 校验ERP订单行ID是否存在为空
            List<PlanItemV2Param> isNullOrderItemIds = param.getDetailList().stream().filter(planItemV2Param -> ObjectUtil.isEmpty(planItemV2Param.getOrderRowId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isNullOrderItemIds)){
                throw new RRException("回货计划明细数据集合中存在ERP订单行ID为空的数据");
            }
            List<Long> orderItemIds = param.getDetailList().stream().map(PlanItemV2Param::getOrderRowId).distinct().collect(Collectors.toList());
            List<PurItemLineVO> purItemLineList = queryPurOrderSlaveList(orderItemIds);
            // 送货计划明细数据集合
            List<DeliveryPlanItemSaveVo> deliveryPlanItemSaveVoList = new ArrayList<>();
            // 使用stream().collect()替代forEach修改为最终不可变Map
            Map<Long, PurItemLineVO> orderMap = purItemLineList.stream()
                    .filter(vo -> ObjectUtil.isNotEmpty(vo.getSourceItemId()))
                    .collect(Collectors.toMap(PurItemLineVO::getSourceItemId, Function.identity(),
                            (existing, replacement) -> existing
                    ));
            deliveryPlanSaveVo.setOperationType(param.getOperationType());
            param.getDetailList().forEach(itemParam -> {
                DeliveryPlanItemSaveVo deliveryPlanItemSaveVo = new DeliveryPlanItemSaveVo();
                deliveryPlanItemSaveVo.setTenantId(getTenantId());
                deliveryPlanItemSaveVo.setTenantPId(0L);
                PurItemLineVO purItemLineVO = orderMap.get(itemParam.getOrderRowId());
                Assert.isNull(purItemLineVO,String.format("ERP订单行ID%s,找不到对应的ERP订单行数据", itemParam.getErpDocRowId()));
                // 计划行基本信息
                deliveryPlanItemSaveVo.setPlanLineId(itemParam.getErpDocRowId());
                deliveryPlanItemSaveVo.setPlanId(param.getErpDocId());
                deliveryPlanItemSaveVo.setPlanNo(param.getPlanNo());
                deliveryPlanItemSaveVo.setPlanDate(itemParam.getPlanDate());
                deliveryPlanItemSaveVo.setDeliveryDate(itemParam.getPlanDate());
                deliveryPlanItemSaveVo.setMatchNum(itemParam.getPlanNum());
                deliveryPlanItemSaveVo.setErpSourceType(param.getErpSourceType());
                // 供应商信息
                deliveryPlanItemSaveVo.setVendorId(purItemLineVO.getVendorId());
                deliveryPlanItemSaveVo.setVendorCode(purItemLineVO.getVendorCode());
                deliveryPlanItemSaveVo.setVendorName(purItemLineVO.getVendorName());
                // 计划行订单信息
                deliveryPlanItemSaveVo.setSaleId(purItemLineVO.getPurId());
                deliveryPlanItemSaveVo.setSaleItemId(purItemLineVO.getId());
                deliveryPlanItemSaveVo.setSaleNo(purItemLineVO.getSoureNo());
                deliveryPlanItemSaveVo.setSaleSeq(purItemLineVO.getSeq());
                deliveryPlanItemSaveVo.setReplyDate(purItemLineVO.getReplyDate());
                deliveryPlanItemSaveVo.setAddress(purItemLineVO.getVendorDeliveryAddress());
                deliveryPlanItemSaveVo.setShippingAddress(purItemLineVO.getShippingAddress());
                deliveryPlanItemSaveVo.setPurchaserId(purItemLineVO.getPurUserId());
                deliveryPlanItemSaveVo.setPurchaserName(purItemLineVO.getPurUserName());
                deliveryPlanItemSaveVo.setPurchasingGroup(purItemLineVO.getPurchasingGroup());
                // 组织机构信息
                deliveryPlanItemSaveVo.setDeptId(28905L);
                deliveryPlanItemSaveVo.setDeptCode("101");
                deliveryPlanItemSaveVo.setMrpRegion("101");
                deliveryPlanItemSaveVo.setDeptName("佛山市顺德区冠宇达电源有限公司");
                // 计划行物料信息
                deliveryPlanItemSaveVo.setGoodsId(purItemLineVO.getGoodsId());
                deliveryPlanItemSaveVo.setGoodsCode(purItemLineVO.getGoodsErpCode());
                deliveryPlanItemSaveVo.setGoodsErpCode(purItemLineVO.getGoodsErpCode());
                deliveryPlanItemSaveVo.setGoodsName(purItemLineVO.getGoodsName());
                deliveryPlanItemSaveVo.setGoodsModel(purItemLineVO.getGoodsModel());
                deliveryPlanItemSaveVo.setUomId(purItemLineVO.getUomId());
                deliveryPlanItemSaveVo.setUomCode(purItemLineVO.getUomCode());
                deliveryPlanItemSaveVo.setUomName(purItemLineVO.getUomName());
                deliveryPlanItemSaveVo.setIsClosed(itemParam.getIsClosed());
                deliveryPlanItemSaveVoList.add(deliveryPlanItemSaveVo);
            });
            deliveryPlanSaveVo.setPlanItemList(deliveryPlanItemSaveVoList);
            deliveryPlanSaveVoList.add(deliveryPlanSaveVo);
        });
        logger.info("金蝶ERP回货计划数据 =====>"+JSONObject.toJSONString(deliveryPlanSaveVoList));
        dmClient.syncPlanOrder(deliveryPlanSaveVoList);
        return new GeneralResult();
    }

    @Api(name = "jindie.plan.syncV3")
    @ApiDocMethod(description = "金蝶ERP下发回货计划数据同步至SRM-V3")
    public GeneralResult planInsertV3(PlanMainV2Param data){
        if (ObjectUtil.isEmpty(data)){
            throw new RRException("参数不能为空");
        }
        if (CollectionUtil.isEmpty(data.getList())){
            throw new RRException("回货计划数据集合不能为空");
        }
        List<DeliveryPlanSaveVo> deliveryPlanSaveVoList = new ArrayList<>();
        for (PlanV2Param erpPlan:data.getList()){
            List<String> isEmptyVendorCodeList = erpPlan.getDetailList().stream().filter(item -> StrUtil.isEmpty(item.getVendorCode())).map(PlanItemV2Param::getVendorCode).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isEmptyVendorCodeList)){
                throw new RRException("回货计划数据集合中存在供应商编码为空的数据");
            }
            List<String> isEmptyGoodsCodeList = erpPlan.getDetailList().stream().filter(item -> StrUtil.isEmpty(item.getGoodsErpCode())).map(PlanItemV2Param::getGoodsErpCode).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isEmptyGoodsCodeList)){
                throw new RRException("回货计划数据集合中存在物料编码为空的数据");
            }
            List<String> vendorCodeList = erpPlan.getDetailList().stream().filter(item -> StrUtil.isNotEmpty(item.getVendorCode())).map(PlanItemV2Param::getVendorCode).collect(Collectors.toList());
            List<VendorVO> vendorList = this.queryVendorList(vendorCodeList);
            if (CollectionUtil.isEmpty(vendorList)){
                throw new RRException("均查询不到供应商");
            }
            Map<String, VendorVO> vendorMap = vendorList.stream()
                    .filter(vo -> ObjectUtil.isNotEmpty(vo.getVendorErpCode()))
                    .collect(Collectors.toMap(VendorVO::getVendorErpCode, Function.identity(),
                            (existing, replacement) -> existing
                    ));
            List<String> goodsCodeList = erpPlan.getDetailList().stream().filter(item -> StrUtil.isNotEmpty(item.getGoodsErpCode())).map(PlanItemV2Param::getGoodsErpCode).collect(Collectors.toList());
            List<GoodsVO> goodsList = this.queryGoodsList(goodsCodeList);
            if (CollectionUtil.isEmpty(goodsList)){
                throw new RRException("均查询不到物料");
            }
            Map<String, GoodsVO> goodsMap = goodsList.stream()
                    .filter(vo -> ObjectUtil.isNotEmpty(vo.getGoodsErpCode()))
                    .collect(Collectors.toMap(GoodsVO::getGoodsErpCode, Function.identity(),
                            (existing, replacement) -> existing
                    ));
            erpPlan.getDetailList().forEach(item -> {
                VendorVO vendorVO = vendorMap.get(item.getVendorCode());
                Assert.isNull(vendorVO, StrUtil.format("供应商编码为{}的供应商查询不对对应数据", item.getVendorCode()));
                GoodsVO goodsVO = goodsMap.get(item.getGoodsErpCode());
                Assert.isNull(goodsVO, StrUtil.format("物料编码为{}的物料查询不对应数据", item.getGoodsErpCode()));
                if (ObjectUtil.isEmpty(item.getPlanNum())){
                    throw new RRException("计划数量不能为空");
                }
                if (ObjectUtil.isEmpty(item.getPlanDate())){
                    throw new RRException("要求回货日期不能为空");
                }
                DeliveryPlanSaveVo deliveryPlanSaveVo = new DeliveryPlanSaveVo();
                deliveryPlanSaveVo.setOperationType(erpPlan.getOperationType());
                deliveryPlanSaveVo.setTenantId(getTenantId());
                deliveryPlanSaveVo.setTenantPId(0L);
                deliveryPlanSaveVo.setDeptId(28905L);
                deliveryPlanSaveVo.setDeptCode("101");
                deliveryPlanSaveVo.setDeptName("佛山市顺德区冠宇达电源有限公司");
                deliveryPlanSaveVo.setVendorId(vendorVO.getSoureId());
                deliveryPlanSaveVo.setVendorCode(vendorVO.getVendorErpCode());
                deliveryPlanSaveVo.setVendorName(vendorVO.getVendorName());
                deliveryPlanSaveVo.setGoodsId(goodsVO.getId());
                deliveryPlanSaveVo.setGoodsCode(goodsVO.getGoodsCode());
                deliveryPlanSaveVo.setGoodsErpCode(goodsVO.getGoodsErpCode());
                deliveryPlanSaveVo.setGoodsName(goodsVO.getGoodsName());
                deliveryPlanSaveVo.setGoodsModel(goodsVO.getGoodsModel());
                deliveryPlanSaveVo.setPlanNum(item.getPlanNum());
                deliveryPlanSaveVo.setPlanDate(item.getPlanDate());
                deliveryPlanSaveVoList.add(deliveryPlanSaveVo);
            });
        }
        if (CollectionUtil.isEmpty(deliveryPlanSaveVoList)){
            throw new RRException("回货计划数据集合为空");
        }
        logger.info("金蝶ERP回货计划数据 =====>"+JSONObject.toJSONString(deliveryPlanSaveVoList));
        dmClient.syncPlanByErpV3(deliveryPlanSaveVoList);
        return new GeneralResult();
    }

    @Api(name = "jindie.dm.syncOutPick")
    @ApiDocMethod(description = "金蝶ERP下发委外领料单数据同步至SRM")
    @GlobalTransactional(rollbackFor = Exception.class)
    public GeneralResult outPickInsert(OutPickListParam param){
        if (ObjectUtil.isEmpty(param)){
            throw new RRException("参数不能为空");
        }
        if (CollectionUtil.isEmpty(param.getList())){
            throw new RRException("委外领料单数据集合不能为空");
        }
        // 校验是否存在操作类型为空的数据
        List<OutPickParam> isNullOperationTypeList = param.getList().stream().filter(outPickParam -> ObjectUtil.isEmpty(outPickParam.getOperationType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullOperationTypeList)){
            throw new RRException("存在操作类型为空的数据");
        }
        // 校验是否存在委外领料单号为空的数据
        List<OutPickParam> isNullOutPickNoList = param.getList().stream().filter(outPickParam -> StrUtil.isEmptyIfStr(outPickParam.getOutPickNo())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullOutPickNoList)){
            throw new RRException("存在委外领料单号为空的数据");
        }
        // 校验是否存在单据类型为空的数据
        List<OutPickParam> isNullDocTypeList = param.getList().stream().filter(outPickParam -> StrUtil.isEmptyIfStr(outPickParam.getDocType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullDocTypeList)){
            throw new RRException("存在单据类型为空的数据");
        }
        // 校验是否存在单据日期数据为空数据
        List<OutPickParam> isNullDocDateList = param.getList().stream().filter(outPickParam -> ObjectUtil.isEmpty(outPickParam.getDocDate())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullDocDateList)){
            throw new RRException("存在单据日期数据为空数据");
        }
        // 校验是否存在供应商编码为空的数据
        List<OutPickParam> isNullVendorErpCodeList = param.getList().stream().filter(outPickParam -> StrUtil.isEmptyIfStr(outPickParam.getVendorErpCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullVendorErpCodeList)){
            throw new RRException("存在供应商编码为空数据");
        }
        List<String> vendorErpCodeList = param.getList().stream().map(OutPickParam::getVendorErpCode).collect(Collectors.toList());
        // 查询供应商信息
        List<VendorVO> vendorVOList = this.queryVendorList(vendorErpCodeList);
        if (CollectionUtil.isEmpty(vendorVOList)){
            throw new RRException("当前接口传参中的供应商编码数据均找不到对应的数据");
        }
        // 校验是否存在来源单据号为空数据
        List<OutPickParam> isNullSourceNoList = param.getList().stream().filter(outPickParam -> StrUtil.isEmptyIfStr(outPickParam.getSourceNo())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullSourceNoList)){
            throw new RRException("存在来源单据号为空数据");
        }
        // 校验是否存在委外领料单明细为空的数据
        List<OutPickParam> isNullDetailList = param.getList().stream().filter(outPickParam -> CollectionUtil.isEmpty(outPickParam.getDetailList())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullDetailList)){
            throw new RRException("存在委外领料单明细为空的数据");
        }
        // 查询单位
        List<UomVO> uomVOList = this.queryUomList();
        // 查询仓库
        List<WarehouseVO> warehouseVOList = this.queryWarehouseList();
        List<OutPickVo> outPickVoList = new ArrayList<>();
        param.getList().forEach(outPick -> {
            // 匹配对应的供应商
            VendorVO vendorVO = vendorVOList.stream().filter(vendor -> StrUtil.equals(vendor.getVendorErpCode(), outPick.getVendorErpCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(vendorVO)){
                throw new RRException(String.format("当前接口传参中的供应商编码%s找不到对应数据", outPick.getVendorErpCode()));
            }
            OutPickVo outPickVo = new OutPickVo();
            outPickVo.setTenantPId(0L);
            outPickVo.setTenantId(getTenantId());
            outPickVo.setOutPickNo(outPick.getOutPickNo());
            // 操作类型
            outPickVo.setOperationType(outPick.getOperationType());
            // 单据状态/单据日期
            JinDieOutPickEnum outPickType = JinDieOutPickEnum.getEnumByName(outPick.getDocType());
            Assert.isNull(outPickType, String.format("当前接口传参中的单据类型%s找不到对应数据", outPick.getDocType()));
            outPickVo.setDocType(outPickType.getValue());
            outPickVo.setDocDate(outPick.getDocDate());
            // 发料组织信息
            outPickVo.setIssuingOrgId(28905L);
            outPickVo.setIssuingOrgCode("101");
            outPickVo.setIssuingOrgName("佛山市顺德区冠宇达电源有限公司");
            // 委外组织信息
            outPickVo.setOutOrgId(28905L);
            outPickVo.setOutOrgCode("101");
            outPickVo.setOutOrgName("佛山市顺德区冠宇达电源有限公司");
            // 供应商
            outPickVo.setVendorId(vendorVO.getSoureId());
            outPickVo.setVendorCode(vendorVO.getVendorErpCode());
            outPickVo.setVendorName(vendorVO.getVendorName());
            // 来源单号
            outPickVo.setSourceNo(outPick.getSourceNo());
            // 校验是否存在物料编码为空的数据
            List<OutPickDetailParam> isNullGoodsErpCodeList = outPick.getDetailList().stream().filter(detail -> StrUtil.isEmptyIfStr(detail.getGoodsErpCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isNullGoodsErpCodeList)){
                throw new RRException(String.format("委外领料单号%s存在物料编码为空数据", outPick.getOutPickNo()));
            }
            List<String> goodsErpCodeList = outPick.getDetailList().stream().filter(detail -> StrUtil.isNotEmpty(detail.getGoodsErpCode())).map(OutPickDetailParam::getGoodsErpCode).collect(Collectors.toList());
            List<GoodsVO> goodsVOList = this.queryGoodsList(goodsErpCodeList);
            if (CollectionUtil.isEmpty(goodsVOList)){
                throw new RRException("当前接口传参中的物料编码数据均找不到对应的数据");
            }
            List<OutPickDetailVo> outPickDetailVoList = new ArrayList<>();
            outPick.getDetailList().forEach(outPickDetail -> {
                OutPickDetailVo outPickDetailVo = BeanConverter.convert(outPickDetail, OutPickDetailVo.class);
                outPickDetailVo.setTenantPId(0L);
                outPickDetailVo.setTenantId(getTenantId());
                outPickDetailVo.setOutPickNo(outPick.getOutPickNo());
                // 匹配对应的物料编码
                GoodsVO goodsVO = goodsVOList.stream().filter(goods -> StrUtil.equals(goods.getGoodsErpCode(), outPickDetail.getGoodsErpCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(goodsVO)){
                    throw new RRException(String.format("当前接口传参中的物料编码%s找不到对应数据", outPickDetail.getGoodsErpCode()));
                }
                // 物料信息
                outPickDetailVo.setGoodsId(goodsVO.getId());
                outPickDetailVo.setGoodsCode(goodsVO.getGoodsErpCode());
                outPickDetailVo.setGoodsErpCode(goodsVO.getGoodsErpCode());
                outPickDetailVo.setGoodsName(goodsVO.getGoodsName());
                outPickDetailVo.setGoodsModel(goodsVO.getGoodsModel());

                if (!StrUtil.isEmptyIfStr(outPickDetail.getUomCode())){
                    // 匹配对应的单位
                    UomVO uomVO = uomVOList.stream().filter(uom -> StrUtil.equals(uom.getUomCode(), outPickDetail.getUomCode())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(uomVO)){
                        outPickDetailVo.setUomId(uomVO.getId());
                        outPickDetailVo.setUomCode(uomVO.getUomCode());
                        outPickDetailVo.setUomName(uomVO.getUomName());
                    }
                }

                if (!StrUtil.isEmptyIfStr(outPickDetail.getItemWarehouseCode())){
                    // 匹配对应的仓库
                    WarehouseVO warehouseVO = warehouseVOList.stream().filter(warehouse -> StrUtil.equals(warehouse.getWarehouseCode(), outPickDetail.getItemWarehouseCode())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(warehouseVO)){
                        outPickDetailVo.setWarehouseId(warehouseVO.getId());
                        outPickDetailVo.setWarehouseCode(warehouseVO.getWarehouseCode());
                        outPickDetailVo.setWarehouseName(warehouseVO.getWarehouseName());
                    }
                }

                if (StrUtil.isEmptyIfStr(outPickDetailVo.getPurOrderNo()) || ObjectUtil.isEmpty(outPickDetailVo.getPurOrderSeq())){
                    throw new RRException(String.format("当前接口传参中委外领料单号%s的采购订单号%s或采购订单行号%s为空",outPick.getOutPickNo(), outPickDetail.getPurOrderNo(), outPickDetail.getPurOrderSeq()));
                }
                // 查询订单信息
                PurItemLineVO purItemVo = orderClient.getPurItemVoBySeqAndSourceNo(outPickDetailVo.getPurOrderNo(), outPickDetailVo.getPurOrderSeq().toString());
                if (ObjectUtil.isEmpty(purItemVo)){
                    throw new RRException(String.format("委外领料单号%s查询不到采购订单行信息,订单号%s-行项目号%s",outPick.getOutPickNo(),outPickDetailVo.getPurOrderNo(),outPickDetailVo.getPurOrderSeq()));
                }
                // 采购订单信息
                outPickDetailVo.setPurOrderId(purItemVo.getPurId());
                outPickDetailVo.setPurOrderItemId(purItemVo.getId());
                outPickDetailVo.setPurOrderNo(purItemVo.getSoureNo());
                outPickDetailVo.setPurOrderSeq(purItemVo.getSeq());
                outPickDetailVoList.add(outPickDetailVo);
            });
            outPickVo.setOutPickDetailList(outPickDetailVoList);
            outPickVoList.add(outPickVo);
        });
        logger.info("委外领料单JSON数据 ====》"+JSONObject.toJSONString(outPickVoList));
        dmClient.batchSaveOutPickByErp(outPickVoList);
        return new GeneralResult();
    }

    @Api(name = "jindie.dm.syncMaster")
    @ApiDocMethod(description = "金蝶ERP下发入库、出库、收料、退料等单据数据同步至SRM")
    @GlobalTransactional(rollbackFor = Exception.class)
    public GeneralResult receiveGoodsInsert(ReceiveGoodsMainParam data) {
        if (ObjectUtil.isEmpty(data) || CollectionUtil.isEmpty(data.getList())){
            throw new RRException("参数为空");
        }
        data.getList().forEach(param -> {
            if (ObjectUtil.isEmpty(param.getOperationType())){
                throw new RRException("操作类型为空");
            }
            if (ObjectUtil.isEmpty(param.getDocType())){
                throw new RRException("单据类型为空");
            }
            if (ObjectUtil.isEmpty(param.getMasterNo())){
                throw new RRException("ERP单据编号不能为空");
            }
            if (ObjectUtil.isEmpty(param.getErpDocId())){
                throw new RRException("ERP单据表头ID不能为空");
            }
            if (StrUtil.isEmptyIfStr(param.getVendorErpCode())){
                throw new RRException("供应商ERP编码不能为空");
            }
            List<VendorVO> vendorVOList = new ArrayList<>();
            List<String> vendorCodeList = Collections.singletonList(param.getVendorErpCode());
            vendorVOList = this.queryVendorList(vendorCodeList);
            if (CollectionUtil.isEmpty(vendorVOList)){
                throw new RRException(String.format("当前供应商编码%s找不到对应数据", param.getVendorErpCode()));
            }
            if (ObjectUtil.isEmpty(param.getDocDate())){
                throw new RRException("单据日期不能为空");
            }
            if (StrUtil.isEmpty(param.getDocYear())){
                param.setDocYear(DateUtil.format(param.getDocDate(), "yyyy"));
            }
            // 是否为销售单类型的数据
            boolean isNotSale = NOT_SALE_TYPES.contains(param.getDocType());
            MasterVO masterVO = new MasterVO();
            masterVO.setIsNotSale(isNotSale);
            masterVO.setMasterNo(param.getMasterNo());
            masterVO.setMasterType(param.getDocType());
            masterVO.setOperType(param.getOperationType());
            // 供应商
            masterVO.setVendorId(vendorVOList.get(0).getSoureId());
            masterVO.setVendorCode(vendorVOList.get(0).getVendorErpCode());
            masterVO.setVendorName(vendorVOList.get(0).getVendorName());
            // 单据年份/时间/过账日期
            masterVO.setDocumentYear(Integer.parseInt(param.getDocYear()));
            masterVO.setDocumentDate(param.getDocDate());
            masterVO.setPostDate(param.getDocDate());
            // 当前组织机构
            masterVO.setDeptId(28905L);
            masterVO.setDeptCode("101");
            masterVO.setDeptName("佛山市顺德区冠宇达电源有限公司");
            masterVO.setMasterStat(3);
            if (CollectionUtil.isEmpty(param.getDetailList())){
                throw new RRException("明细数据为空");
            }
            // 判断是否退料单数据
            if (RETURN_TYPES.contains(param.getDocType())){
                Assert.isNull(param.getRetMethodType(), "退料方式不能为空");
                masterVO.setRetMethodType(param.getRetMethodType());
            }
            // 按照送货单入库/退货
            int byDeliverySize = param.getDetailList().stream().filter(item -> !StrUtil.isEmptyIfStr(item.getDeliveryNo()) && !StrUtil.isEmptyIfStr(item.getOrderNo())).collect(Collectors.toList()).size();
            // 按照订单入库/退货
            int byOrderSize = param.getDetailList().stream().filter(item -> !StrUtil.isEmptyIfStr(item.getOrderNo()) && StrUtil.isEmptyIfStr(item.getDeliveryNo())).collect(Collectors.toList()).size();
            // 按无来源入库/退货
            int byNoSourceSize = param.getDetailList().stream().filter(item -> StrUtil.isEmptyIfStr(item.getOrderNo()) && StrUtil.isEmptyIfStr(item.getDeliveryNo())).collect(Collectors.toList()).size();
            if (byDeliverySize > 0){
                masterVO.setSourceType(MasterSourceTypeEnum.DELIVERY_ORDER.getValue());
            }
            if (byOrderSize > 0){
                masterVO.setSourceType(MasterSourceTypeEnum.PUR_ORDER.getValue());
            }
            if (!isNotSale){
                masterVO.setSourceType(MasterSourceTypeEnum.SALE_ORDER.getValue());
            }
            if (byNoSourceSize > 0){
                masterVO.setSourceType(MasterSourceTypeEnum.NO_SOURCE.getValue());
            }
            List<MasterItemVO> masterItemVOList = new ArrayList<>();
            // 校验是否存在物料编码为空的数据
            List<ReceiveGoodsItemParam> isEmptyGoodsCodeList = param.getDetailList().stream().filter(item -> StrUtil.isEmptyIfStr(item.getGoodsErpCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isEmptyGoodsCodeList)){
                throw new RRException("存在物料编码为空的数据");
            }
            // 查询物料信息
            List<String> goodsErpCodeList = param.getDetailList().stream().map(ReceiveGoodsItemParam::getGoodsErpCode).distinct().collect(Collectors.toList());
            List<GoodsVO> goodsVOList = this.queryGoodsList(goodsErpCodeList);
            if (CollectionUtil.isEmpty(goodsVOList)){
                throw new RRException("当前单据下的物料编码信息均找不到对应数据");
            }
            // 校验ERP单据明细ID是否存在为空的数据
            List<ReceiveGoodsItemParam> isEmptyErpRowIdList = param.getDetailList().stream().filter(item -> ObjectUtil.isEmpty(item.getErpDocRowId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isEmptyErpRowIdList)){
                throw new RRException("存在ERP单据明细ID为空的数据");
            }
            // 校验是否存在数量为空的数据
            List<ReceiveGoodsItemParam> isEmptyNumList = param.getDetailList().stream().filter(item -> ObjectUtil.isEmpty(item.getMasterNum())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(isEmptyNumList)){
                throw new RRException("存在数量为空的数据");
            }
            // 不为销售单类型的数据时
            if (isNotSale){
                if (MasterSourceTypeEnum.DELIVERY_ORDER.getValue().equals(masterVO.getSourceType())){
                    // 校验送货单号是否存在空数据
                    List<ReceiveGoodsItemParam> isEmptyDeNoList = param.getDetailList().stream().filter(item -> StrUtil.isEmptyIfStr(item.getDeliveryNo())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(isEmptyDeNoList)){
                        throw new RRException("存在送货单号为空数据");
                    }
                    // 校验送货单明细ID是否存在为空的数据
                    List<ReceiveGoodsItemParam> isEmptyDeRowIdList = param.getDetailList().stream().filter(item -> ObjectUtil.isEmpty(item.getDeliveryRowId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(isEmptyDeRowIdList)){
                        throw new RRException("存在送货单明细ID为空的数据");
                    }
                }
                if (MasterSourceTypeEnum.PUR_ORDER.getValue().equals(masterVO.getSourceType())){
                    List<ReceiveGoodsItemParam> isEmptyOrderRowIdList = param.getDetailList().stream().filter(item -> ObjectUtil.isEmpty(item.getOrderRowId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(isEmptyOrderRowIdList)){
                        throw new RRException("存在订单明细ID为空的数据");
                    }
                }
            }
            List<SaleItemVo> saleItemVos = new ArrayList<>();
            // 为销售单类型的数据且不为无来源时
            if (!isNotSale && !MasterSourceTypeEnum.NO_SOURCE.getValue().equals(masterVO.getSourceType())){
                // 校验订单号是否存在空数据
                List<ReceiveGoodsItemParam> isEmptyOrderNoList = param.getDetailList().stream().filter(item -> StrUtil.isEmptyIfStr(item.getOrderNo())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(isEmptyOrderNoList)){
                    throw new RRException("存在订单号为空的数据");
                }
                // 校验订单明细ID是否存在为空的数据
                List<ReceiveGoodsItemParam> isEmptyOrderRowIdList= param.getDetailList().stream().filter(item -> ObjectUtil.isEmpty(item.getOrderRowId())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(isEmptyOrderRowIdList)){
                    throw new RRException("存在订单明细ID为空数据");
                }
                List<Long> orderSourceItemIds = param.getDetailList().stream().map(ReceiveGoodsItemParam::getOrderRowId).distinct().collect(Collectors.toList());
                saleItemVos = this.querySaleItemVoListByErpItemIds(orderSourceItemIds);
            }
            List<SaleItemVo> finalSaleItemVos = saleItemVos;
            List<VendorVO> finalVendorVOList = vendorVOList;
            param.getDetailList().forEach(item -> {
                MasterItemVO masterItemVO = new MasterItemVO();
                masterItemVO.setSeq(item.getDocRowNo());
                masterItemVO.setSourceItemId(item.getErpDocRowId());
                // ERP单据号/单据ID/单据明细ID
                masterItemVO.setSourceErpNo(param.getMasterNo());
                masterItemVO.setSourceErpId(param.getErpDocId());
                masterItemVO.setSourceErpItemId(item.getErpDocRowId());
                // 单据日期
                masterItemVO.setMasterDate(param.getDocDate());
                PurItemLineVO purItemVo = new PurItemLineVO();
                if (isNotSale){
                    if (ObjectUtil.isNotEmpty(item.getDeliveryRowId())){
                        DeliveryItemVO deliveryItemVo = dmClient.getDeliveryItemVoById(item.getDeliveryRowId());
                        Assert.isNull(deliveryItemVo, String.format("送货单明细行ID%s找不到对应的送货数据", item.getDeliveryRowId()));
                        if (!deliveryItemVo.getDeliveryNo().equals(item.getDeliveryNo())){
                            throw new RRException(String.format("当前送货单%s与送货单明细行ID%s对应的送货单号数据不一致", item.getDeliveryNo(), item.getDeliveryRowId()));
                        }
                        masterItemVO.setDeNo(deliveryItemVo.getDeliveryNo());
                        masterItemVO.setDeSeq(deliveryItemVo.getSeq());
                        masterItemVO.setDeId(deliveryItemVo.getDeId());
                        masterItemVO.setDeItemId(deliveryItemVo.getId());
                        masterItemVO.setPlanId(deliveryItemVo.getPlanId());
                        masterItemVO.setPlanItemId(deliveryItemVo.getPlanSrmLineId());
                        purItemVo = orderClient.getPurItemVoById(deliveryItemVo.getSaleItemId());
                        Assert.isNull(purItemVo, String.format("查询不到采购订单行信息,行id%s",deliveryItemVo.getSaleItemId()));
                        if (!purItemVo.getSourceItemId().equals(item.getOrderRowId())){
                            throw new RRException(String.format("当前ERP订单行id{%s}与送货单明细行对应的ERP订单行ID不一致",item.getErpDocRowId()));
                        }
                    } else {
                        purItemVo = orderClient.getPurItemVoBySourceItemId(item.getOrderRowId());
                    }
                    if (!MasterSourceTypeEnum.NO_SOURCE.getValue().equals(masterVO.getSourceType()) && ObjectUtil.isNotEmpty(purItemVo)){
                        //对应的采购订单信息
                        masterItemVO.setSaleId(purItemVo.getPurId());
                        masterItemVO.setSaleItemId(purItemVo.getId());
                        //采购订单号
                        masterItemVO.setSaleNo(purItemVo.getSoureNo());
                        //采购订单行项目号
                        masterItemVO.setSaleSeq(Integer.toString(purItemVo.getSeq()));
                        //订单类型
                        masterItemVO.setOrderType(purItemVo.getOrderType());
                        // 送货类型
                        masterItemVO.setDeliveryType(purItemVo.getDeliveryType());
                        //物料信息
                        masterItemVO.setGoodsId(purItemVo.getGoodsId());
                        masterItemVO.setGoodsCode(purItemVo.getGoodsCode());
                        masterItemVO.setGoodsErpCode(purItemVo.getGoodsErpCode());
                        masterItemVO.setGoodsName(purItemVo.getGoodsName());
                        masterItemVO.setGoodsModel(purItemVo.getGoodsModel());
                        // 单位
                        masterItemVO.setUomId(purItemVo.getUomId());
                        masterItemVO.setUomName(purItemVo.getUomName());
                        //税相关信息
                        masterItemVO.setRateId(purItemVo.getRateId());
                        masterItemVO.setRateName(purItemVo.getRateName());
                        masterItemVO.setRateVal(purItemVo.getRateVal());
                    }
                    GoodsVO goodsVO = goodsVOList.stream().filter(goods -> goods.getGoodsErpCode().equals(item.getGoodsErpCode())).findFirst().orElse(null);
                    Assert.isNull(goodsVO, String.format("物料编码%s找不到对应的数据", masterItemVO.getGoodsErpCode()));
                    if (MasterSourceTypeEnum.NO_SOURCE.getValue().equals(masterVO.getSourceType())){
                        //物料信息
                        masterItemVO.setGoodsId(goodsVO.getId());
                        masterItemVO.setGoodsCode(goodsVO.getGoodsCode());
                        masterItemVO.setGoodsErpCode(goodsVO.getGoodsErpCode());
                        masterItemVO.setGoodsName(goodsVO.getGoodsName());
                        masterItemVO.setGoodsModel(goodsVO.getGoodsModel());
                        // 单位
                        masterItemVO.setUomId(goodsVO.getUomId());
                        masterItemVO.setUomName(goodsVO.getUomName());
                        //税相关信息
                        masterItemVO.setRateId(finalVendorVOList.get(0).getRateId());
                        masterItemVO.setRateName(finalVendorVOList.get(0).getRateName());
                        masterItemVO.setRateVal(finalVendorVOList.get(0).getRateVal());
                    }
                } else {
                    if (!MasterSourceTypeEnum.NO_SOURCE.getValue().equals(masterVO.getSourceType())){
                        if (CollectionUtil.isEmpty(finalSaleItemVos)){
                            throw new RRException("销售订单信息为空");
                        }
                        SaleItemVo saleItemVo = finalSaleItemVos.stream().filter(saleItem -> saleItem.getSourceItemId().equals(item.getOrderRowId())).findFirst().orElse(null);
                        Assert.isNull(saleItemVo, String.format("查询不到销售订单行信息,行id%s",item.getOrderRowId()));
                        //对应的采购订单信息
                        masterItemVO.setSaleId(saleItemVo.getSaleId());
                        masterItemVO.setSaleItemId(saleItemVo.getId());
                        //采购订单号
                        masterItemVO.setSaleNo(saleItemVo.getSourceNo());
                        //采购订单行项目号
                        masterItemVO.setSaleSeq(Integer.toString(saleItemVo.getSeq()));
                        //订单类型
                        masterItemVO.setOrderType(saleItemVo.getOrderType());
                        //物料信息
                        masterItemVO.setGoodsId(saleItemVo.getGoodsId());
                        masterItemVO.setGoodsCode(saleItemVo.getGoodsCode());
                        masterItemVO.setGoodsErpCode(saleItemVo.getGoodsErpCode());
                        masterItemVO.setGoodsName(saleItemVo.getGoodsName());
                        masterItemVO.setGoodsModel(saleItemVo.getGoodsModel());
                        // 单位
                        masterItemVO.setUomId(saleItemVo.getUomId());
                        masterItemVO.setUomName(saleItemVo.getUomName());
                        //税相关信息
                        masterItemVO.setRateId(saleItemVo.getRateId());
                        masterItemVO.setRateName(saleItemVo.getRateName());
                        masterItemVO.setRateVal(saleItemVo.getRateVal());
                    } else {
                        GoodsVO goodsVO = goodsVOList.stream().filter(goods -> goods.getGoodsErpCode().equals(item.getGoodsErpCode())).findFirst().orElse(null);
                        Assert.isNull(goodsVO, String.format("物料编码%s找不到对应的数据", item.getGoodsErpCode()));
                        //物料信息
                        masterItemVO.setGoodsId(goodsVO.getId());
                        masterItemVO.setGoodsCode(goodsVO.getGoodsCode());
                        masterItemVO.setGoodsErpCode(goodsVO.getGoodsErpCode());
                        masterItemVO.setGoodsName(goodsVO.getGoodsName());
                        masterItemVO.setGoodsModel(goodsVO.getGoodsModel());
                        // 单位
                        masterItemVO.setUomId(goodsVO.getUomId());
                        masterItemVO.setUomName(goodsVO.getUomName());
                        //税相关信息
                        masterItemVO.setRateId(finalVendorVOList.get(0).getRateId());
                        masterItemVO.setRateName(finalVendorVOList.get(0).getRateName());
                        masterItemVO.setRateVal(finalVendorVOList.get(0).getRateVal());
                    }
                }
                //收货(退货)数量
                masterItemVO.setMasterNum(item.getMasterNum());
                if (RETURN_TYPES.contains(masterVO.getMasterType())){
                    masterItemVO.setMasterNum(masterItemVO.getMasterNum().negate());
                }
                //SAP出入库物料凭证年份
                masterItemVO.setSapDocYear(masterItemVO.getSapDocYear());
                masterItemVOList.add(masterItemVO);
            });
            masterVO.setMasterItemEntityList(masterItemVOList);
            logger.info("需要进行保存的入库/出库数据 =====》"+JSONObject.toJSONString(masterVO));
            dmClient.synchronousMasterInErp(masterVO);
        });
        return new GeneralResult();
    }

    @Api(name = "jindie.sm.syncPayment")
    @ApiDocMethod(description = "金蝶ERP下发付款单数据同步至SRM")
    public GeneralResult paymentInsert(PaymentMainParam data){
        if (ObjectUtil.isEmpty(data) || CollectionUtil.isEmpty(data.getList())){
            throw new RRException("请求参数不能为空");
        }
        data.getList().forEach(param -> {
            if (StrUtil.isEmptyIfStr(param.getPaymentNo())){
                throw new RRException("付款单号不能为空");
            }
            if (ObjectUtil.isEmpty(param.getPaymentType())){
                throw new RRException("单据类型不能为空");
            }
            if (ObjectUtil.isEmpty(param.getErpDocId())){
                throw new RRException("ERP单据ID不能为空");
            }
            if (StrUtil.isEmptyIfStr(param.getVendorErpCode())){
                throw new RRException("供应商编码不能为空");
            }
            if (ObjectUtil.isEmpty(param.getPayableAmount())){
                throw new RRException("应付金额不能为空");
            }
            if (ObjectUtil.isEmpty(param.getPaymentAmount())){
                throw new RRException("付款金额不能为空");
            }
            if (ObjectUtil.isEmpty(param.getPaymentDate())){
                throw new RRException("付款日期不能为空");
            }
            if (CollectionUtil.isEmpty(param.getDetailList())){
                throw new RRException("付款单明细不能为空");
            }
            PaymentRecordVo paymentRecordVo = BeanConverter.convert(param, PaymentRecordVo.class);
            paymentRecordVo.setTenantPId(0L);
            paymentRecordVo.setTenantId(getTenantId());
            paymentRecordVo.setSourceErpId(param.getErpDocId());
            paymentRecordVo.setPayDate(param.getPaymentDate());
            paymentRecordVo.setPayAmount(param.getPaymentAmount());
            List<PaymentRecItemVo> paymentRecItemVoList = new ArrayList<>();
            param.getDetailList().forEach(item -> {
                PaymentRecItemVo paymentRecItemVo = BeanConverter.convert(item, PaymentRecItemVo.class);
                paymentRecItemVo.setSourceErpId(param.getErpDocId());
                paymentRecItemVo.setSourceErpItemId(item.getErpDocItemId());
                paymentRecItemVo.setSourceErpNo(param.getPaymentNo());
                paymentRecItemVoList.add(paymentRecItemVo);
            });
            paymentRecordVo.setPaymentRecItemList(paymentRecItemVoList);
            logger.info("数据传参 ====> "+JSONObject.toJSONString(paymentRecordVo));
            smClient.syncPaymentByErp(paymentRecordVo);
        });

        return new GeneralResult();
    }

    /**
     * 校验计划数据
     * @param list
     */
    private void checkPlanParamData(List<PlanItemParam> list){
        // 校验是否存在计划单号为空的数据
        List<PlanItemParam> isNullPlanNoList = list.stream().filter(planItemParam -> ObjectUtil.isEmpty(planItemParam.getPlanNo())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullPlanNoList)){
            throw new RRException("存在计划单号为空的数据");
        }
        // 校验是否存在采购组为空数据
        List<PlanItemParam> isNullPurGroupList = list.stream().filter(planItemParam -> ObjectUtil.isEmpty(planItemParam.getPurGroup())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullPurGroupList)){
            throw new RRException("存在采购组为空的数据");
        }
        // 校验是否存在采购订单类型为空的数据
        List<PlanItemParam> isNullOrderTypeList = list.stream().filter(planItemParam -> ObjectUtil.isEmpty(planItemParam.getOrderType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullOrderTypeList)){
            throw new RRException("存在采购订单类型为空的数据");
        }
        // 校验是否存在物料编码为空的数据
        List<PlanItemParam> isNullGoodsErpCodeList = list.stream().filter(planItemParam -> ObjectUtil.isEmpty(planItemParam.getGoodsErpCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullGoodsErpCodeList)){
            throw new RRException("存在物料编码为空的数据");
        }
        // 校验是否存在计划数量为空的数据
        List<PlanItemParam> isNullPlanNumList = list.stream().filter(planItemParam -> ObjectUtil.isEmpty(planItemParam.getPlanNum())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(isNullPlanNumList)){
            throw new RRException("存在计划数量为空的数据");
        }
    }

    private Long getTenantId() {
        return ApiContext.getApiConfig()
                .getAppSecretManager()
                .getTenantId(ApiContext.getApiParam().fatchAppKey());
    }

    /**
     * 根据金蝶ERP供应商编码查询供应商信息
     * @param vendorCodeList
     * @return
     */
    private List<VendorVO> queryVendorList(List<String> vendorCodeList) {
        List<VendorVO> vendorVOList = new ArrayList<>();
        // 供应商编码为空
        if (CollectionUtil.isEmpty(vendorCodeList)) {
            return vendorVOList;
        }
        // 每次批量查询的供应商数量
        int batchSize = 10;
        // 批量查询供应商信息
        for (int i = 0; i < vendorCodeList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, vendorCodeList.size());
            List<String> subList = vendorCodeList.subList(i, end);

            VendorReqVo vendorReqVo = new VendorReqVo();
            vendorReqVo.setVendorErpCodeList(subList);
            List<VendorVO> result = baseClient.queryVendorList(vendorReqVo);
            // 合并添加供应商信息
            if (CollectionUtil.isNotEmpty(result)) {
                vendorVOList.addAll(result);
            }
        }
        return vendorVOList;
    }

    /**
     * 根据客户编码查询供应商信息
     * @param customerCodeList
     * @return
     */
    private List<VendorVO> queryVendorListByCustomer(List<String> customerCodeList) {
        List<VendorVO> vendorVOList = new ArrayList<>();
        // 供应商编码为空
        if (CollectionUtil.isEmpty(customerCodeList)) {
            return vendorVOList;
        }
        // 每次批量查询的供应商数量
        int batchSize = 10;
        // 批量查询供应商信息
        for (int i = 0; i < customerCodeList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, customerCodeList.size());
            List<String> subList = customerCodeList.subList(i, end);

            VendorReqVo vendorReqVo = new VendorReqVo();
            vendorReqVo.setCustomerCodeList(subList);
            List<VendorVO> result = baseClient.queryVendorList(vendorReqVo);
            // 合并添加供应商信息
            if (CollectionUtil.isNotEmpty(result)) {
                vendorVOList.addAll(result);
            }
        }
        return vendorVOList;
    }

    /**
     * 根据金蝶ERP用户编码查询用户信息
     * @param userCodeList
     * @return
     */
    private List<UserDto> queryUserList(List<String> userCodeList) {
        List<UserDto> userDtoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(userCodeList)){
            return userDtoList;
        }
        // 每次批量查询的用户数量
        int batchSize = 10;
        for (int i = 0; i < userCodeList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, userCodeList.size());
            List<String> subList = userCodeList.subList(i, end);
            Map<String, Object> params = new HashMap<>();
            params.put("userCodeList", subList);
            List<UserDto> result = baseClient.queryUserList(params);
            // 合并添加用户信息
            if (CollectionUtil.isNotEmpty(result)) {
                userDtoList.addAll(result);
            }
        }
        return userDtoList;
    }

    /**
     * 根据金蝶ERP部门编码查询部门信息
     * @param deptCodeList
     * @return
     */
    private List<DeptVO> queryDeptList(List<String> deptCodeList){
        List<DeptVO> deptVOList = new ArrayList<>();
        if (CollectionUtil.isEmpty(deptCodeList)){
            return deptVOList;
        }
        deptCodeList.forEach(deptCode -> {
            DeptVO deptVO = sysClient.queryByDeptCode(deptCode, getTenantId());
            if (ObjectUtil.isNotEmpty(deptVO)){
                deptVOList.add(deptVO);
            }
        });
        return deptVOList;
    }

    /**
     * 查询所有单位信息
     * @return
     */
    private List<UomVO> queryUomList(){
        return baseClient.queryAllUomByTenantId(getTenantId());
    }

    /**
     * 查询所有仓库信息
     * @return
     */
    private List<WarehouseVO> queryWarehouseList(){
        return baseClient.queryAllWarehouseByTenantId(getTenantId());
    }

    /**
     * 查询所有税率信息
     * @return
     */
    private List<RateVO> queryRateList(){
        return baseClient.queryAllRateByTenantId(getTenantId());
    }

    /**
     * 查询采购订单明细信息
     */
    private List<PurItemLineVO> queryPurOrderSlaveList(List<Long> sourceItemIds){
        List<PurItemLineVO> purItemLineList = new ArrayList<>();
        if (CollectionUtil.isEmpty(sourceItemIds)){
            return new ArrayList<>();
        }
        // 每次批量查询的供应商数量
        int batchSize = 10;
        // 批量查询供应商信息
        for (int i = 0; i < sourceItemIds.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sourceItemIds.size());
            List<Long> subList = sourceItemIds.subList(i, end);
            PurQuery purQuery = new PurQuery();
            purQuery.setTenantId(getTenantId());
            purQuery.setSourceItemIds(subList);
            List<PurItemLineVO> result = orderClient.queryPurOrderSlaveList(purQuery);
            // 合并添加信息
            if (CollectionUtil.isNotEmpty(result)) {
                purItemLineList.addAll(result);
            }
        }
        return purItemLineList;
    }

    /**
     * 根据金蝶ERP物料编码查询物料信息
     * @param goodsErpCodeList
     * @return
     */
    private List<GoodsVO> queryGoodsList(List<String> goodsErpCodeList){
        List<GoodsVO> goodsVoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsErpCodeList)){
            return goodsVoList;
        }
        // 每次批量查询的物料数量
        int batchSize = 10;
        for (int i = 0; i < goodsErpCodeList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, goodsErpCodeList.size());
            List<String> subList = goodsErpCodeList.subList(i, end);
            GoodsReqVo goodsReqVo = new GoodsReqVo();
            goodsReqVo.setGoodsErpCodeList(subList);
            List<GoodsVO> goodsList = baseClient.queryGoodsList(goodsReqVo);
            // 合并添加物料信息
            if (CollectionUtil.isNotEmpty(goodsList)){
                goodsVoList.addAll(goodsList);
            }
        }
        return goodsVoList;
    }

    /**
     * 根据金蝶ERP销售订单明细id查询SRM销售订单数据
     * @param sourceItemIds
     * @return
     */
    private List<SaleItemVo> querySaleItemVoListByErpItemIds(List<Long> sourceItemIds){
        SaleQuery saleQuery = new SaleQuery();
        if (CollectionUtil.isEmpty(sourceItemIds)){
            return new ArrayList<>();
        }
        saleQuery.setSourceItemIds(sourceItemIds);
        return orderClient.querySaleItemVoList(saleQuery);
    }

    /**
     * 封装采购订单表头信息
     * @param param
     * @param vendor
     * @param user
     * @param dept
     * @return
     */
    private PurEntityVO assemblePurOrder(PurOrderParams param,
                                         VendorVO vendor,
                                         UserDto user,
                                         DeptVO dept) {
        Long gveTenantId = 24739L;
        PurEntityVO orderVo = new PurEntityVO();
        orderVo.setTenantId(getTenantId());
        orderVo.setTenantPId(getTenantId());
        orderVo.setTenantName("佛山市顺德区冠宇达电源有限公司");
        if (getTenantId().equals(gveTenantId)) {
            orderVo.setDeptId(28905L);
            orderVo.setDeptCode("101");
            orderVo.setFactoryCode("101");
            orderVo.setDeptName("佛山市顺德区冠宇达电源有限公司");
        }
        orderVo.setSourceId(param.getOrderErpId());
        orderVo.setPurNo(param.getPurNo());
        orderVo.setOrderDate(param.getOrderDate());
        // 根据ERP订单类型编码获取订单类型枚举
        JinDieOrderTypeEnum orderType = JinDieOrderTypeEnum.getEnumByName(param.getOrderType());
        orderVo.setOrderType(orderType.getValue());
        orderVo.setVendorId(Long.valueOf(vendor.getSoureId()+""));
        orderVo.setVendorCode(vendor.getVendorErpCode()+"");
        orderVo.setVendorName(vendor.getVendorName()+"");
        orderVo.setCurrencyCode(vendor.getCurrencyCode());
        orderVo.setCurrencyName(vendor.getCurrencyName());
        orderVo.setPurId(user.getUserId());
        orderVo.setPurName(user.getUserName());
        orderVo.setPayName("");
        // TODO 目前冠宇达目前只有一个组织架构 101
        orderVo.setBukrs("101");
        orderVo.setOperType(param.getOperationType());
        orderVo.setRateId(vendor.getRateId());
        orderVo.setRateCode(vendor.getRateCode());
        orderVo.setRateName(vendor.getRateName());
        orderVo.setRateVal(vendor.getRateVal());
        //交货地址
        orderVo.setShippingAddress(param.getDeliveryAddress());
        //送货地址
        orderVo.setVendorDeliveryAddress(param.getShippingAddress());
        //采购组
        orderVo.setPurchasingGroup(param.getPurGroup());
        return orderVo;
    }

    /**
     * 封装销售订单表头信息
     * @param param
     * @param vendor
     * @param user
     * @param dept
     * @return
     */
    private SaleVo assembleSaleOrder(SaleOrderParams param,
                                         VendorVO vendor,
                                         UserDto user,
                                         DeptVO dept) {
        Long gveTenantId = 24739L;
        SaleVo orderVo = new SaleVo();
        orderVo.setTenantId(getTenantId());
        orderVo.setSourceId(param.getOrderErpId());
        orderVo.setTenantPId(getTenantId());
        if (getTenantId().equals(gveTenantId)) {
            orderVo.setDeptId(28905L);
            orderVo.setDeptCode("101");
            orderVo.setDeptName("佛山市顺德区冠宇达电源有限公司");
        }
        orderVo.setSaleNo(param.getSaleNo());
        orderVo.setOrderDate(param.getOrderDate());
        // 根据ERP订单类型编码获取订单类型枚举
        JinDieOrderTypeEnum orderType = JinDieOrderTypeEnum.getEnumByName(param.getOrderType());
        orderVo.setOrderType(orderType.getValue());
        orderVo.setVendorId(Long.valueOf(vendor.getSoureId()+""));
        orderVo.setVendorCode(vendor.getVendorErpCode()+"");
        orderVo.setVendorName(vendor.getVendorName()+"");
        orderVo.setSalemanId(user.getUserId());
        orderVo.setSalemanCode(user.getUserCode());
        orderVo.setSalemanName(user.getUserName());
        orderVo.setOperType(param.getOperationType());
        orderVo.setRateId(vendor.getRateId());
        orderVo.setRateCode(vendor.getRateCode());
        orderVo.setRateName(vendor.getRateName());
        orderVo.setRateVal(vendor.getRateVal());
        //销售组
        orderVo.setSaleGroup(param.getSaleGroup());
        return orderVo;
    }

    /**
     * 封装采购订单表体信息
     * @param orderVo
     * @param detail
     * @param vendor
     * @param goodsVO
     * @param uomList
     * @return
     */
    private PurItemLineVO assemblePurOrderLine(PurEntityVO orderVo,
                                               PurOrderDetailParam detail,
                                               VendorVO vendor,
                                               GoodsVO goodsVO,
                                               List<UomVO> uomList){
        PurItemLineVO orderLineVo = new PurItemLineVO();
        orderLineVo.setTenantId(orderVo.getTenantId());
        orderLineVo.setTenantPId(orderVo.getTenantPId());
        orderLineVo.setMrpRegion(orderVo.getDeptCode());
        // 采购订单来源ID（ERP采购订单ID）
        orderLineVo.setSourceId(orderVo.getSourceId());
        // 采购订单来源明细ID（ERP采购订单明细行ID）
        orderLineVo.setSourceItemId(detail.getPurItemId());
        // 序号(ERP订单行的行号)
        orderLineVo.setSeq(detail.getSeq());
        orderLineVo.setErpChangeType(detail.getErpChangeType());
        // 物料信息
        orderLineVo.setGoodsId(goodsVO.getId());
        orderLineVo.setGoodsCode(goodsVO.getGoodsErpCode());
        orderLineVo.setGoodsErpCode(goodsVO.getGoodsErpCode());
        orderLineVo.setGoodsName(goodsVO.getGoodsName());
        orderLineVo.setGoodsModel(goodsVO.getGoodsModel());
        orderLineVo.setGoodsClassName(goodsVO.getClassName());
        // 采购单位
        UomVO purUomVo = uomList.stream().filter(uom -> uom.getUomCode().equals(detail.getPurUomCode())).findFirst().orElse(null);
        orderLineVo.setUomId(ObjectUtil.isNotEmpty(purUomVo) ? purUomVo.getId() : null);
        orderLineVo.setUomCode(ObjectUtil.isNotEmpty(purUomVo) ? purUomVo.getUomCode() : null);
        orderLineVo.setUomName(ObjectUtil.isNotEmpty(purUomVo) ? purUomVo.getUomName() : null);
        // 库存单位
        UomVO invUomVo = uomList.stream().filter(uom -> uom.getUomCode().equals(detail.getInvUomCode())).findFirst().orElse(null);
        orderLineVo.setAuxUomId(ObjectUtil.isNotEmpty(invUomVo) ? invUomVo.getId() : null);
        orderLineVo.setAuxUomCode(ObjectUtil.isNotEmpty(invUomVo) ? invUomVo.getUomCode() : null);
        orderLineVo.setAuxUomName(ObjectUtil.isNotEmpty(invUomVo) ? invUomVo.getUomName() : null);
        // 价格单位
        UomVO priceUomVo = uomList.stream().filter(uom -> uom.getUomCode().equals(detail.getPriceUomCode())).findFirst().orElse(null);
        orderLineVo.setOrderPriceUom(ObjectUtil.isNotEmpty(priceUomVo) ? priceUomVo.getUomCode() : null);
        orderLineVo.setPriceUom(ObjectUtil.isNotEmpty(detail.getPriceUomQty()) ? detail.getPriceUomQty() : null);
        // 税率
        orderLineVo.setRateId(vendor.getRateId());
        orderLineVo.setRateCode(vendor.getRateCode());
        orderLineVo.setRateName(vendor.getRateName());
        orderLineVo.setRateVal(vendor.getRateVal());
        // 单价及金额
        orderLineVo.setTaxPrice(detail.getTaxPrice());
        orderLineVo.setTaxAmount(detail.getTaxAmount());
        orderLineVo.setGstPrice(detail.getGstPrice());
        orderLineVo.setGstAmount(detail.getGstAmount());
        // 交货日期
        orderLineVo.setDeliveryDate(detail.getDeliveryDate());
        // 采购订单数量
        orderLineVo.setOrderNum(detail.getOrderNum());
        // 操作类型为1-创建
        if (OperTypeEnum.CREATE.getValue().equals(orderVo.getOperType())){
            //已匹配数
            orderLineVo.setMatchedPlanNum(BigDecimal.ZERO);
            //已制单数
            orderLineVo.setMakeNum(BigDecimal.ZERO);
            //待送数
            orderLineVo.setWaitNum(orderLineVo.getOrderNum());
            //已送数
            orderLineVo.setFixNum(BigDecimal.ZERO);
            //入库数
            orderLineVo.setErpMasterNum(BigDecimal.ZERO);
            //退货数
            orderLineVo.setErpRejectNum(BigDecimal.ZERO);
            //订单状态默认待答交
            orderLineVo.setItemStat(PurlineStatEnum.WAITREPLY.getValue());
        }
        // 是否关闭
        orderLineVo.setIsClose(detail.getIsClose());
        // 是否删除
        orderLineVo.setDeleteFlag(detail.getIsDeleted());
        // 是否退货
        orderLineVo.setReturnMark(detail.getIsReturn());
        // 是否免费
        orderLineVo.setFreeMark(detail.getIsFree());
        return orderLineVo;
    }

    /**
     * 封装销售订单表体信息
     * @param orderVo
     * @param detail
     * @param vendor
     * @param goodsVO
     * @param uomList
     * @return
     */
    private SaleItemVo assembleSaleOrderLine(SaleVo orderVo,
                                            SaleOrderDetailParam detail,
                                            VendorVO vendor,
                                            GoodsVO goodsVO,
                                            List<UomVO> uomList){
        SaleItemVo orderLineVo = new SaleItemVo();
        orderLineVo.setTenantId(orderVo.getTenantId());
        orderLineVo.setTenantPId(orderVo.getTenantPId());
        // 采购订单来源ID（ERP采购订单ID）
        orderLineVo.setSourceId(orderVo.getSourceId());
        // 采购订单来源明细ID（ERP采购订单明细行ID）
        orderLineVo.setSourceItemId(detail.getSaleItemId());
        orderLineVo.setSourceNo(orderVo.getSaleNo());
        // 序号(ERP订单行的行号)
        orderLineVo.setSeq(detail.getSeq());
        orderLineVo.setErpChangeType(detail.getErpChangeType());
        // 物料信息
        orderLineVo.setGoodsId(goodsVO.getId());
        orderLineVo.setGoodsCode(goodsVO.getGoodsErpCode());
        orderLineVo.setGoodsErpCode(goodsVO.getGoodsErpCode());
        orderLineVo.setGoodsName(goodsVO.getGoodsName());
        orderLineVo.setGoodsModel(goodsVO.getGoodsModel());
        // 采购单位
        UomVO purUomVo = uomList.stream().filter(uom -> uom.getUomCode().equals(detail.getSaleUomCode())).findFirst().orElse(null);
        orderLineVo.setUomId(ObjectUtil.isNotEmpty(purUomVo) ? purUomVo.getId() : null);
        orderLineVo.setUomName(ObjectUtil.isNotEmpty(purUomVo) ? purUomVo.getUomName() : null);
        // 价格单位
        UomVO priceUomVo = uomList.stream().filter(uom -> uom.getUomCode().equals(detail.getPriceUomCode())).findFirst().orElse(null);
        orderLineVo.setPriceUomId(ObjectUtil.isNotEmpty(priceUomVo) ? priceUomVo.getId() : null);
        orderLineVo.setPriceUomCode(ObjectUtil.isNotEmpty(priceUomVo) ? priceUomVo.getUomCode() : null);
        orderLineVo.setPriceUomName(ObjectUtil.isNotEmpty(priceUomVo) ? priceUomVo.getUomName() : null);
        orderLineVo.setPriceUomQty(ObjectUtil.isNotEmpty(detail.getPriceUomQty()) ? detail.getPriceUomQty() : null);
        // 税率
        orderLineVo.setRateId(vendor.getRateId());
        orderLineVo.setRateName(vendor.getRateName());
        orderLineVo.setRateVal(vendor.getRateVal());
        // 单价及金额
        orderLineVo.setTaxPrice(detail.getTaxPrice());
        orderLineVo.setTaxAmount(detail.getTaxAmount());
        orderLineVo.setGstPrice(detail.getGstPrice());
        orderLineVo.setGstAmount(detail.getGstAmount());
        // 交货日期
        orderLineVo.setDeliveryDate(detail.getDeliveryDate());
        // 采购订单数量
        orderLineVo.setOrderNum(detail.getOrderNum());
        // 操作类型为1-创建
        if (OperTypeEnum.CREATE.getValue().equals(orderVo.getOperType())){
            orderLineVo.setItemStat(PurlineStatEnum.CONFIRM.getValue());
        }
        // 是否关闭
        orderLineVo.setIsClose(detail.getIsClose());
        // 是否删除
        orderLineVo.setDeleteFlag(detail.getIsDeleted());
        return orderLineVo;
    }
}
