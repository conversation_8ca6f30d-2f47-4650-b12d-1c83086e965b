-- 功能说明:供应商soure_id增加唯一索引 提交人:肖来  提交日期12月-15日
alter table base_vendor add unique (soure_id);
-- 模板打印表增加是否有效字段
ALTER TABLE `base_print_template`
  ADD COLUMN `is_valid` INT(1) DEFAULT 1  NULL   COMMENT '是否有效' AFTER `modify_date`;

-- 2.7.1版本新加的字段 -----

-- 2023-01-10 倪林 机构部门 增加领导人及上级机构信息
ALTER TABLE sys_dept
    ADD COLUMN `this_leader_id` bigint(19) COMMENT '领导人ID' AFTER is_leaf,
	ADD COLUMN `this_leader_code` varchar(56) COMMENT '领导人编码' AFTER this_leader_id,
	ADD COLUMN `this_leader_name` varchar(255) COMMENT '领导人名称' AFTER this_leader_code,
	ADD COLUMN `super_leader_id` bigint(19) COMMENT '上级机构领导用户Id' AFTER this_leader_name,
	ADD COLUMN `super_leader_code` varchar(56) COMMENT '上级机构领导用户Id编码' AFTER super_leader_id,
	ADD COLUMN `super_leader_name` varchar(255) COMMENT '上级机构领导用户Id名称' AFTER super_leader_code;

-- 2023-01-10 倪林 机构部门 增加领导人及上级机构信息
ALTER TABLE sys_dept
    ADD COLUMN `dept_type` bigint(19) COMMENT '机构类型-来源于系统数据字典sysDist.DeptType' AFTER is_deleted;

-- 2023-02-18 倪林 增加招投标历史表
create table  `im_bid_history` (
    `tenant_p_id`  bigint(20) DEFAULT  '0' NOT NULL  COMMENT '关联组织ID，默认为0',
    `tenant_id`  bigint(20) COMMENT '组织ID',
    `id`  bigint(20) AUTO_INCREMENT  COMMENT '主键ID',
    `enquiry_id`  bigint(20) COMMENT '询价id',
    `enquiry_no`  varchar(50) COMMENT '询价单号',
    `enquiry_item_id`  bigint(20) COMMENT '询价单明细ID',
    `quotation_id`  bigint(20) COMMENT '报价单id',
    `quotation_no`  varchar(50) COMMENT '报价单号',
    `quotation_item_id`  bigint(20) COMMENT '报价单明细Id',
    `tenant_name`  varchar(255) COMMENT '所属企业名称',
    `vendor_id`  bigint(20) COMMENT '供应商表id',
    `vendor_code`  varchar(50) COMMENT '供应商表编码',
    `vendor_name`  varchar(100) COMMENT '供应商表名称',
    `goods_id`  bigint(20) COMMENT '供应商品Id',
    `goods_erp_code`  varchar(100) COMMENT '料品ERP品号',
    `goods_code`  varchar(100) COMMENT '供应商品号',
    `goods_name`  varchar(500) COMMENT '供应商品名',
    `goods_model`  varchar(500) COMMENT '供应商品型号',
    `ask_date`  datetime(0) COMMENT '开始时间',
    `complete_date`  datetime(0) COMMENT '结束时间',
    `bid_date`  datetime(0) COMMENT '投标时间',
    `price`  decimal(20, 6) COMMENT '单价',
    `need_num`  decimal(20, 6) COMMENT '数量',
    `need_amount`  decimal(20, 6) COMMENT '金额',
    `price_rank`  bigint(3) COMMENT '排名',
    `tb_count`  bigint(3) COMMENT '投标次数',
    `ipaddress`  varchar(100) COMMENT 'ip地址',
    `delete_flag`  int(1) DEFAULT  '0' COMMENT '删除标志',
    `create_id`  bigint(20) COMMENT '创建人ID',
    `creater`  Varchar(50) COMMENT '创建人姓名',
    `create_date`  Datetime COMMENT '创建时间',
    `modifi_id`  bigint(10) COMMENT '修改人ID',
    `modifier`  Varchar(20) COMMENT '修改人',
    `modify_date`  Datetime COMMENT '修改时间',
primary key  (`id`))engine=innodb auto_increment=9 default charset=utf8 comment='招投标历史表';

-- 2023-02-22 倪林 将招投标表中的排名字段(price_rank)从字符串转为数字类型
UPDATE im_enquiry_vendor SET price_rank = null WHERE price_rank = '-';
ALTER TABLE im_enquiry_vendor MODIFY COLUMN price_rank BIGINT(3) DEFAULT NULL;

-- 2023-02-22 倪林 修改报价单产品明细 排名字段(price_rank) 从字符串转为数字类型
UPDATE im_quotation_item SET price_rank = null WHERE price_rank = '-';
ALTER TABLE im_quotation_item MODIFY COLUMN price_rank BIGINT(3) DEFAULT NULL;

-- 2023-03-01 倪林 增加总排名、总金额
ALTER TABLE im_bid_history
    ADD COLUMN `total_price_rank` BIGINT(3) COMMENT '总排名(来源im_quotation.price_rank)' AFTER tb_count,
	ADD COLUMN `total_need_amount` decimal(20, 6) COMMENT '总金额(来源im_quotation.need_amount)' AFTER total_price_rank;

-- 2023-03-01 倪林 增加排名和总金额
ALTER TABLE im_quotation
    ADD COLUMN `price_rank` BIGINT(3) COMMENT '总排名' AFTER total_amount,
	ADD COLUMN `need_amount` decimal(20, 6) COMMENT '总金额' AFTER price_rank;


-- 2023-03-06 倪林 增加招标名称、物料类型、招标规则
ALTER TABLE im_enquiry
    ADD COLUMN `enquiry_name` VARCHAR(200) COMMENT '招标名称' AFTER enquiry_code,
	ADD COLUMN `goods_type` BIGINT(2) COMMENT '物料类型;1-物料;2-材质' AFTER enquiry_stat,
	ADD COLUMN `enquiry_rule` VARCHAR(200) COMMENT '招标规则' AFTER goods_type;

ALTER TABLE im_quotation
    ADD COLUMN `quotation_name` VARCHAR(200) COMMENT '报价名称(来源 im_enquiry.enquiryName )' AFTER quotation_no,
	ADD COLUMN `goods_type` BIGINT(2) COMMENT '物料类型;1-物料;2-材质' AFTER enquiry_type,
	ADD COLUMN `enquiry_rule` VARCHAR(200) COMMENT '招标规则' AFTER goods_type;

ALTER TABLE im_bid_history
	ADD COLUMN `goods_type` BIGINT(2) COMMENT '物料类型;1-物料;2-材质' AFTER tb_count;


-- 2023-03-06 倪林 增加延长时间
ALTER TABLE im_enquiry
    ADD COLUMN `time_expand` VARCHAR(200) COMMENT '延长时间' AFTER complete_date;

ALTER TABLE im_quotation
    ADD COLUMN `time_expand` VARCHAR(200) COMMENT '延长时间' AFTER complete_date;

ALTER TABLE im_bid_history
    ADD COLUMN `time_expand` VARCHAR(200) COMMENT '延长时间' AFTER complete_date;


-- 2023-03-07 倪林 增加招投标审核表
-- ----------------------------
-- Table structure for im_bid_plan
-- ----------------------------
DROP TABLE IF EXISTS `im_bid_plan`;
CREATE TABLE `im_bid_plan`  (
    `tenant_p_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '关联组织ID，默认为0',
    `tenant_id` bigint(20) NOT NULL COMMENT '组织ID',
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `bid_plan_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '招标计划单号;系统自动生成',
    `bid_plan_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '招标计划名称',
    `bid_plan_rule` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '招标计划规则',
    `dept_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购组织',
    `category` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '招标品类',
    `bid_plan_stat` int(2) NULL DEFAULT 1 COMMENT '单据状态;1-待审核;2-待发布;3-待报价;4-待审核;5-已审核;29-已作废',
    `bid_plan_date` datetime(0) NULL DEFAULT NULL COMMENT '招标时间',
    `bid_plan_way` bigint(2) NULL DEFAULT NULL COMMENT '招标方式',
    `bid_plan_round` bigint(2) NULL DEFAULT NULL COMMENT '招标轮数',
    `bid_plan_type` bigint(2) NULL DEFAULT NULL COMMENT '招标类型',
    `earnest_amount` decimal(20, 6) NULL DEFAULT NULL COMMENT '保证金金额(万元)',
    `budget_amount` decimal(20, 6) NULL DEFAULT NULL COMMENT '预算金额(万元)',
    `user_id` bigint(20) NULL DEFAULT NULL COMMENT '负责人Id',
    `user_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人编码',
    `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人名称',
    `delete_flag` int(1) NULL DEFAULT NULL COMMENT '删除标志',
    `dep_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购组',
    `pur_org_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购组织',
    `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
    `creater` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
    `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modifi_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
    `modifier` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
    `modify_date` datetime(0) NULL DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '招标方案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for im_bid_plan_item
-- ----------------------------
DROP TABLE IF EXISTS `im_bid_plan_item`;
CREATE TABLE `im_bid_plan_item`  (
    `tenant_p_id` bigint(20) NULL DEFAULT 0 COMMENT '关联组织ID，默认为0',
    `tenant_id` bigint(20) NOT NULL COMMENT '组织ID',
    `bid_plan_id` bigint(20) NOT NULL COMMENT '计划id:来源于:im_bid_plan.id',
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint(20) NULL DEFAULT NULL COMMENT '料品id:来源于:scm_bas_goods.id',
    `goods_erp_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品ERP品号:来源于:scm_bas_goods.goods_erp_code',
    `goods_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_code',
    `goods_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品名:来源于:scm_bas_goods.goods_name',
    `goods_model` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_model',
    `uom_id` bigint(20) NULL DEFAULT NULL COMMENT '计量单位ID;来源于scm_bas_uom.id',
    `uom_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计量单位编码',
    `uom_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-计量单位名称;来源于scm_bas_uom.uom_name',
    `rate_id` bigint(20) NULL DEFAULT NULL COMMENT '交易税率;来源于scm_bas_rate.id',
    `rate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-税率名称;来源于scm_bas_rate.rate_name',
    `rate_val` decimal(20, 6) NULL DEFAULT NULL COMMENT '冗余字段-税率值;来源于scm_bas_rate.rate_val',
    `currency_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段币别ID;来源于scm_bas_currency.id',
    `currency_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-币别名称;来源于scm_bas_currency.currency_name',
    `cost_price` decimal(20, 6) NULL DEFAULT NULL COMMENT '成本价',
    `gst_price` decimal(20, 6) NULL DEFAULT NULL COMMENT '含税单价',
    `tax_price` decimal(20, 6) NULL DEFAULT NULL COMMENT '不含税单价',
    `need_num` decimal(20, 6) NULL DEFAULT NULL COMMENT '需求数量',
    `need_amount` decimal(20, 6) NULL DEFAULT NULL COMMENT '需求金额',
    `invalid_date` datetime(0) NULL DEFAULT NULL COMMENT '生效日期',
    `expire_date` datetime(0) NULL DEFAULT NULL COMMENT '失效日期',
    `delete_flag` int(1) NULL DEFAULT NULL COMMENT '删除标志',
    `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
    `creater` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modifi_id` bigint(20) NULL DEFAULT NULL COMMENT '更新人id',
    `modifier` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
    `modify_date` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '招标方案明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for im_bid_plan_vendor
-- ----------------------------
DROP TABLE IF EXISTS `im_bid_plan_vendor`;
CREATE TABLE `im_bid_plan_vendor`  (
    `tenant_p_id` bigint(20) NULL DEFAULT 0 COMMENT '关联组织ID，默认为0',
    `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '组织ID',
    `bid_plan_id` bigint(20) NULL DEFAULT NULL COMMENT '招标计划id:来源于:im_bid_plan.id',
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `vendor_id` bigint(20) NULL DEFAULT NULL COMMENT '供应商表id:来源于:scm_bas_vendor.id',
    `vendor_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_name',
    `vendor_full_Name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商全称',
    `vendor_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-供应商表名称:来源于:scm_bas_vendor.vendor_name',
    `vendor_email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商Email账号',
    `register_capital` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册资本',
    `key_customer` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主要客户',
    `cooperation` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合作情况',
    `delete_flag` int(1) NULL DEFAULT NULL COMMENT '删除标志',
    `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
    `creater` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
    `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modifi_id` bigint(20) NULL DEFAULT NULL COMMENT '更新人id',
    `modifier` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
    `modify_date` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 358 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '招标方案供应商表' ROW_FORMAT = Dynamic;


-- 2023-03-08 倪林 招标审批增加招标小组字段
ALTER TABLE im_bid_plan
    ADD COLUMN `bid_team` VARCHAR(500) COMMENT '招标小组' AFTER user_name;


-- 2023-03-08 倪林 采/供 增加招标方式
ALTER TABLE im_bid_plan
    ADD COLUMN `bid_team` VARCHAR(500) COMMENT '招标小组' AFTER user_name;


-- 2023-03-08 倪林 采/供 增加招标方式
ALTER TABLE im_enquiry
    ADD COLUMN `enquiry_way` BIGINT(2) COMMENT '招标方式,1-明标;2-暗标;' AFTER enquiry_stat;

ALTER TABLE im_quotation
    ADD COLUMN `enquiry_way` BIGINT(2) COMMENT '招标方式,1-明标;2-暗标;' AFTER enquiry_stat;

ALTER TABLE im_bid_history
    ADD COLUMN `enquiry_way` BIGINT(2) COMMENT '招标方式,1-明标;2-暗标;' AFTER goods_type;


-- 2023-03-08 倪林 采/供 增加物料分类信息
ALTER TABLE im_enquiry_item
    ADD COLUMN `class_id` BIGINT(20) COMMENT '物料分类id' AFTER goods_model,
	ADD COLUMN `class_name` VARCHAR(255) COMMENT '物料分类名称' AFTER class_id;

ALTER TABLE im_quotation_item
    ADD COLUMN `class_id` BIGINT(2) COMMENT '物料分类id' AFTER goods_model,
	ADD COLUMN `class_name` VARCHAR(255) COMMENT '物料分类名称' AFTER class_id;

ALTER TABLE im_bid_history
    ADD COLUMN `class_id` BIGINT(2) COMMENT '物料分类id' AFTER goods_model,
	ADD COLUMN `class_name` VARCHAR(255) COMMENT '物料分类名称' AFTER class_id;

ALTER TABLE im_bid_plan_item
    ADD COLUMN `class_id` BIGINT(2) COMMENT '物料分类id' AFTER goods_model,
	ADD COLUMN `class_name` VARCHAR(255) COMMENT '物料分类名称' AFTER class_id;


-- 2023-03-10 倪林 询价单 增加招标方案id和编码
ALTER TABLE im_enquiry
    ADD COLUMN `bid_plan_id` bigint(19) COMMENT '招标方案id' AFTER enquiry_explain,
	ADD COLUMN `bid_plan_code` varchar(56) COMMENT '招标方案编码' AFTER bid_plan_id;


-- 2023-03-10 倪林 招投标计划 增加流程id
ALTER TABLE im_bid_plan
    ADD COLUMN `wf_id` bigint(20) COMMENT '启动流程-流程ID' AFTER delete_flag,
	ADD COLUMN `wf_status` int(2)  COMMENT '启动流程 - 流程状态' AFTER wf_id;

-- 2023/2/20 胡梓扬 base_class分类表中添加分类类型字段
ALTER TABLE base_class ADD COLUMN `class_type` int(2) DEFAULT NULL COMMENT '分类类型：从数据字典ClassType中获取' AFTER is_valid;
-- 2023/2/21 胡梓扬 base_vendor供应商表中添加分类字段
ALTER TABLE base_vendor
ADD COLUMN `class_id` bigint(21) DEFAULT NULL COMMENT '分类类型id：base_class表中获取' AFTER is_site_visit_stat,
ADD COLUMN `class_code` VARCHAR(56) DEFAULT NULL COMMENT '分类类型编码：base_class表中获取' AFTER class_id,
ADD COLUMN `class_name` VARCHAR(56) DEFAULT NULL COMMENT '分类类型名称：base_class表中获取' AFTER class_code,
ADD COLUMN `class_id_paths` VARCHAR(255) DEFAULT NULL COMMENT '分类类型id链路：base_class表中获取' AFTER class_name,
ADD COLUMN `class_name_paths` VARCHAR(1024) DEFAULT NULL COMMENT '分类类型名称链路：base_class表中获取' AFTER class_id_paths;
-- 2023/2/22 胡梓扬 新增表
-- 供应商资质文件配置表
create table  `base_quality_config` (
`id`  bigint(20) NOT NULL  AUTO_INCREMENT  COMMENT '主键ID',
`tenant_p_id`  bigint(20) DEFAULT  '0' NOT NULL  COMMENT '关联组织ID，默认为0',
`tenant_id`  bigint(20) NOT NULL  COMMENT '组织ID',
`quality_name`  Varchar(100) NOT NULL  COMMENT '资质文件名称',
`quality_seq`  bigint(20) NOT NULL  COMMENT '文件序号',
`is_req`  int DEFAULT  '1' NOT NULL  COMMENT '是否必填;0-否-NO;1-是-YES',
`file_path`  Varchar(100) NOT NULL  COMMENT '参考模板地址',
`remark`  Varchar(500) COMMENT '备注',
`create_id`  bigint(20) COMMENT '创建人Id',
`creater`  Varchar(20) COMMENT '更新人名称',
`create_date`  Datetime COMMENT '创建时间',
`modifi_id`  bigint(20) COMMENT '创建人Id',
`modifier`  Varchar(20) COMMENT '更新时间',
`modify_date`  Datetime COMMENT '更新人',
 PRIMARY KEY  (`id`))ENGINE=INNODB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='企业资质配置表';
-- 供应商需上传资质文件表
create table  `base_quality_vendor` (
`id`  bigint(20) NOT NULL  AUTO_INCREMENT  COMMENT '主键ID',
`tenant_p_id`  bigint(20) DEFAULT  '0' NOT NULL  COMMENT '关联组织ID，默认为0',
`tenant_id`  bigint(20) NOT NULL  COMMENT '组织ID',
`vendor_id`  bigint(20) NOT NULL  COMMENT '供应商表id:来源于:scm_bas_vendor.id',
`vendor_code`  Varchar(50) NOT NULL  COMMENT '冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_code',
`vendor_name`  Varchar(100) NOT NULL  COMMENT '冗余字段-供应商表名称:来源于:scm_bas_vendor.vendor_name',
`quality_id`  bigint(20) NOT NULL  COMMENT '来源于企业资质配置表base_quality_config.id',
`quality_name`  Varchar(100) NOT NULL  COMMENT '来源于企业资质配置资质文件名称base_quality_config.quality_name',
`quality_doc_path`  Varchar(100) NOT NULL  COMMENT '供应商上传文件的地址',
`remark`  Varchar(500) COMMENT '备注',
`create_id`  bigint(20) COMMENT '创建人Id',
`creater`  Varchar(20) COMMENT '更新人名称',
`create_date`  Datetime COMMENT '创建时间',
`modifi_id`  bigint(20) COMMENT '创建人Id',
`modifier`  Varchar(20) COMMENT '更新时间',
`modify_date`  Datetime COMMENT '更新人',
 PRIMARY KEY  (`id`))ENGINE=INNODB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='供应商资质记录表';
-- 2023/2/24 胡梓扬 base_vendor供应商表增加10个备用字段
ALTER TABLE base_vendor
ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER class_id_paths,
ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;

-- 2023/3/8 胡梓扬 base_goods物料表中添加单位Id字段
ALTER TABLE base_goods ADD COLUMN `uom_id` bigint(21) DEFAULT NULL COMMENT '单位id' AFTER purchuser;
-- 2023/3/8 胡梓扬 base_goods物料表中添加单位名称字段
ALTER TABLE base_goods ADD COLUMN `uom_name` VARCHAR(56) DEFAULT NULL COMMENT '单位名称' AFTER uom_code;
-- 2023/3/8 胡梓扬 base_goods物料表增加10个备用字段
ALTER TABLE base_goods
ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER material_source,
ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;
-- 2023/3/10 胡梓扬 base_quality_config供应商资质模版配置表增加参考模版文件名称
ALTER TABLE base_quality_config
ADD COLUMN `file_name` VARCHAR(100) DEFAULT NULL COMMENT '参考模版文件名称' AFTER file_path;
-- 2023/3/10 胡梓扬 base_quality_vendor供应商资质文件记录表增加参考模版文件名称
ALTER TABLE base_quality_vendor
ADD COLUMN `quality_doc_name` VARCHAR(100) DEFAULT NULL COMMENT '质检文件名称' AFTER quality_doc_path;
-- 2023/3/13 胡梓扬 base_sample供应商送样通知单表增加参考模版文件名称
ALTER TABLE base_sample
ADD COLUMN `vendor_erp_code` VARCHAR(56) DEFAULT NULL COMMENT '供应商ERP编码' AFTER vendor_code;

-- 2023-03-13 倪林 明细增加参考价(含税)
ALTER TABLE im_quotation_item
    ADD COLUMN reference_price decimal(20, 6) COMMENT '参考价(含税)' AFTER gst_price;
ALTER TABLE im_enquiry_item
    ADD COLUMN reference_price decimal(20, 6) COMMENT '参考价(含税)' AFTER gst_price;

-- 2023-03-14 倪林 主表信息增加 允许供应商投标次数
ALTER TABLE im_enquiry
    ADD COLUMN `allow_bid_count` int(2) COMMENT '允许供应商投标次数' AFTER is_urgent;
ALTER TABLE im_quotation
    ADD COLUMN `allow_bid_count` int(2) COMMENT '允许供应商投标次数' AFTER is_urgent;


-- 2023-03-15 倪林 产品信息标 增加拦标价
ALTER TABLE im_enquiry_item
    ADD COLUMN `intercept_price` decimal(20, 6) COMMENT '拦标价' AFTER cost_price;

ALTER TABLE im_quotation_item
    ADD COLUMN `intercept_price` decimal(20, 6) COMMENT '拦标价' AFTER cost_price;

ALTER TABLE im_bid_plan_item
    ADD COLUMN `intercept_price` decimal(20, 6) COMMENT '拦标价' AFTER cost_price;

-- 2023/3/15 胡梓扬 base_rate税率表增加税率编码、税率名称
ALTER TABLE base_rate
ADD COLUMN `rate_code` VARCHAR(56) DEFAULT NULL COMMENT '税率编码' AFTER id,
ADD COLUMN `rate_describe` VARCHAR(128) DEFAULT NULL COMMENT '税率编码' AFTER rate_name;
-- 2023/3/15 胡梓扬 base_rate税率表增加税率编码、税率名称
ALTER TABLE base_uom
ADD COLUMN `class_code` VARCHAR(56) DEFAULT NULL COMMENT '单位分类编码' AFTER uom_name,
ADD COLUMN `class_name` VARCHAR(128) DEFAULT NULL COMMENT '单位分类名称' AFTER class_code,
ADD COLUMN `precision_val` decimal(20,2) DEFAULT NULL COMMENT '精度' AFTER class_name,
ADD COLUMN `roundoff_type` int(2) DEFAULT NULL COMMENT '舍入类型' AFTER precision_val;


-- 2023-03-15 倪林 定价审批 修改表名
rename table im_material_pricel to im_material_price;
rename table im_material_pricel_item to im_material_price_item;


-- 2023-03-15 倪林 定价审批 修改主表单据字段名
ALTER TABLE im_material_price CHANGE `stat` `material_status` int(1) NOT NULL DEFAULT '1' COMMENT '单据状态';
-- 2023-03-15 倪林 定价审批 修改从表单据字段名
ALTER TABLE im_material_price_item CHANGE `pricel_id` `material_price_id` bigint(20) DEFAULT NULL COMMENT '主表id';

-- 2023-03-15 倪林 定价审批 定价审批表增加字段
ALTER TABLE im_material_price
    ADD COLUMN `source_type` INT(2) DEFAULT NULL COMMENT '来源单据类型' AFTER `tenant_name`,
    ADD COLUMN `source_no` VARCHAR(50) DEFAULT NULL COMMENT '来源单据单号' AFTER `source_type`,
    ADD COLUMN `source_id` BIGINT(20) DEFAULT NULL COMMENT '来源单据id' AFTER `source_no`,
    ADD COLUMN `vendor_id` BIGINT(20) DEFAULT NULL COMMENT '供应商id' AFTER `source_type`,
    ADD COLUMN `vendor_code` VARCHAR(50) DEFAULT NULL COMMENT '供应商编码' AFTER `vendor_id`,
    ADD COLUMN `vendor_name` VARCHAR(200) DEFAULT NULL COMMENT '供应商名称' AFTER `vendor_code`,
    ADD COLUMN `signature_type` INT(2) DEFAULT NULL COMMENT '签章模板' AFTER `vendor_name`,
    ADD COLUMN `invalid_date` DATETIME DEFAULT NULL COMMENT '生效日期' AFTER `signature_type`,
    ADD COLUMN `expire_date` DATETIME DEFAULT NULL COMMENT '失效日期' AFTER `invalid_date`,
    ADD COLUMN `wf_status` INT(2) DEFAULT NULL COMMENT '流程状态' AFTER `wf_id`;

ALTER TABLE im_material_price_item
    ADD COLUMN `goods_model` VARCHAR(500) DEFAULT NULL COMMENT '物料类型名称' AFTER `goods_name`,
    ADD COLUMN `class_id` BIGINT(20) DEFAULT NULL COMMENT '物料类型id' AFTER `goods_model`,
    ADD COLUMN `class_name` VARCHAR(200) DEFAULT NULL COMMENT '物料类型名称' AFTER `class_id`,
    ADD COLUMN `lower_price` DECIMAL(20, 6) DEFAULT NULL COMMENT '最低价' AFTER `approve_price`,
    ADD COLUMN `gst_price` DECIMAL(20, 6) DEFAULT NULL COMMENT '含税单价' AFTER `lower_price`,
    ADD COLUMN `tax_price` DECIMAL(20, 6) DEFAULT NULL COMMENT '不含税单价' AFTER `gst_price`,
    ADD COLUMN `original_price` DECIMAL(20, 6) DEFAULT NULL COMMENT '原含税单价' AFTER `tax_price`,
    ADD COLUMN `fluctuation_range` DECIMAL(20, 6) DEFAULT NULL COMMENT '涨跌幅' AFTER `original_price`;
-- 2023/3/18 胡梓扬 base_sample_item送样通知单明细表增加字段
ALTER TABLE base_sample_item
ADD COLUMN `tenant_file_path` VARCHAR(256) DEFAULT NULL COMMENT '采购方明细行文件' AFTER remark,
ADD COLUMN `vendor_file_path` VARCHAR(256) DEFAULT NULL COMMENT '供应商明细行文件' AFTER tenant_file_path;


-- 2023/3/20 倪林 增加供应商文档表
CREATE TABLE `im_bid_plan_doc` (
    `tenant_p_id` bigint(20) DEFAULT '0' COMMENT '关联组织ID，默认为0',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '组织ID',
    `bid_plan_id` bigint(20) DEFAULT NULL COMMENT '招标计划id:来源于:im_bid_plan.id',
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `vendor_id` bigint(20) DEFAULT NULL COMMENT '供应商表id:来源于:scm_bas_vendor.id',
    `vendor_code` varchar(50) DEFAULT NULL COMMENT '冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_code',
    `vendor_full_Name` varchar(255) DEFAULT NULL COMMENT '供应商全称',
    `vendor_name` varchar(100) DEFAULT NULL COMMENT '冗余字段-供应商表名称:来源于:scm_bas_vendor.vendor_name',
    `vendor_email` varchar(255) DEFAULT NULL COMMENT '供应商Email账号',
    `qualify_doc_name` varchar(255) DEFAULT NULL COMMENT '资质文件名称',
    `file_path` varchar(255) DEFAULT NULL COMMENT '文件地址',
    `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
    `respond_id` bigint(20) DEFAULT NULL COMMENT '应标人Id',
    `respondent` varchar(255) DEFAULT NULL COMMENT '应标人',
    `respond_date` datetime DEFAULT NULL COMMENT '应标时间',
    `respond_status` int(2) DEFAULT NULL COMMENT '应标状态;1-无操作;2-拒绝应标;3-已应标;',
    `review_id` bigint(20) DEFAULT NULL COMMENT '审核人id',
    `reviewer` varchar(255) DEFAULT NULL COMMENT '审核人',
    `review_status` int(2) DEFAULT NULL COMMENT '审核状态;1-无操作;2-已退回;3-已审核;4-重新提交',
    `review_date` datetime DEFAULT NULL COMMENT '审核时间',
    `review_reason` varchar(255) DEFAULT NULL COMMENT '原因',
    `delete_flag` int(1) DEFAULT 0 COMMENT '删除标志',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_id` bigint(20) DEFAULT NULL COMMENT '创建人Id',
    `creater` varchar(20) DEFAULT NULL COMMENT '创建人名称',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `modifi_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
    `modifier` varchar(20) DEFAULT NULL COMMENT '更新人',
    `modify_date` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='招标方案供应商文档表';


-- 2023/3/22 倪林 增加供应商字段
ALTER TABLE im_bid_plan_vendor
    ADD COLUMN `respond_id` bigint(20) DEFAULT NULL COMMENT '应标人Id' AFTER vendor_email,
    ADD COLUMN `respondent` varchar(255) DEFAULT NULL COMMENT '应标人' AFTER respond_id,
    ADD COLUMN `respond_date` datetime DEFAULT NULL COMMENT '应标时间' AFTER respondent,
    ADD COLUMN `respond_status` int(2) DEFAULT NULL COMMENT '应标状态;1-无操作;2-拒绝应标;3-已应标;' AFTER respond_date,
    ADD COLUMN `respond_reason` varchar(255) DEFAULT NULL COMMENT '应标/拒标原因' AFTER respond_status,
    ADD COLUMN `review_id` bigint(20) DEFAULT NULL COMMENT '审核人id' AFTER respond_id,
    ADD COLUMN `reviewer` varchar(255) DEFAULT NULL COMMENT '审核人' AFTER respond_id,
    ADD COLUMN `review_status` int(2) DEFAULT NULL COMMENT '审核状态;1-无操作;2-已退回;3-已审核;4-重新提交' AFTER reviewer,
    ADD COLUMN `review_date` datetime DEFAULT NULL COMMENT '审核时间' AFTER review_status,
    ADD COLUMN `review_reason` varchar(255) DEFAULT NULL COMMENT '审核/退回原因' AFTER review_date;


-- 2023/3/22 倪林 增加招标计划供应商字段
ALTER TABLE im_bid_plan_doc
    ADD COLUMN `file_temp_path` varchar(255) DEFAULT NULL COMMENT '文件模板地址' AFTER file_name,
    ADD COLUMN `file_temp_name` varchar(255) DEFAULT NULL COMMENT '文件模板名称' AFTER file_temp_path,
    ADD COLUMN `is_required` int(1) DEFAULT NULL COMMENT '是否必填;0-否;1-是;' AFTER file_temp_name,
    ADD COLUMN `bid_plan_vendor_id` bigint(20) DEFAULT NULL COMMENT '供应商主表id' AFTER is_required;


-- 2023/3/20 倪林 增加供应商文档表
CREATE TABLE `im_supply_ratio` (
   `tenant_p_id` bigint(20) DEFAULT '0' COMMENT '关联组织ID，默认为0',
   `tenant_id` bigint(20) DEFAULT NULL COMMENT '组织ID',
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `supply_code` varchar(255) DEFAULT NULL COMMENT '供货单号',
   `supply_name` varchar(255) DEFAULT NULL COMMENT '供货名称',
   `invalid_date` datetime DEFAULT NULL COMMENT '生效日期',
   `supply_stat` int(2) DEFAULT NULL COMMENT '单号状态',
   `delete_flag` int(1) DEFAULT 0 COMMENT '删除标志',
   `remark` varchar(500) DEFAULT NULL COMMENT '备注',
   `create_id` bigint(20) DEFAULT NULL COMMENT '创建人Id',
   `creater` varchar(20) DEFAULT NULL COMMENT '创建人名称',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `modifi_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
   `modifier` varchar(20) DEFAULT NULL COMMENT '更新人',
   `modify_date` datetime DEFAULT NULL COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='供货比例';


-- 2023/3/20 倪林 增加供应商文档表
CREATE TABLE `im_supply_ratio_item` (
    `tenant_p_id` bigint(20) DEFAULT '0' COMMENT '关联组织ID，默认为0',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '组织ID',
    `supply_ratio_id` bigint(20) DEFAULT NULL COMMENT '供货比例id:来源于:im_supply_ratio.id',
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint(20) NULL DEFAULT NULL COMMENT '料品id:来源于:scm_bas_goods.id',
    `goods_erp_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品ERP品号:来源于:scm_bas_goods.goods_erp_code',
    `goods_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_code',
    `goods_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品名:来源于:scm_bas_goods.goods_name',
    `goods_model` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-料品供应商品号:来源于:scm_bas_goods.goods_model',
    `class_id` bigint(20) DEFAULT NULL COMMENT '物料分类id',
    `class_code` VARCHAR(255) DEFAULT NULL COMMENT '物料分类编码',
    `class_name` VARCHAR(255) DEFAULT NULL COMMENT '物料分类名称',
    `uom_id` bigint(20) NULL DEFAULT NULL COMMENT '计量单位ID;来源于scm_bas_uom.id',
    `uom_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计量单位编码',
    `uom_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段-计量单位名称;来源于scm_bas_uom.uom_name',
    `vendor_id` bigint(20) DEFAULT NULL COMMENT '供应商表id:来源于:scm_bas_vendor.id',
    `vendor_code` varchar(50) DEFAULT NULL COMMENT '冗余字段-供应商表编码:来源于:scm_bas_vendor.vendor_code',
    `vendor_full_name` varchar(255) DEFAULT NULL COMMENT '供应商全称',
    `vendor_name` varchar(100) DEFAULT NULL COMMENT '冗余字段-供应商表名称:来源于:scm_bas_vendor.vendor_name',
    `vendor_email` varchar(255) DEFAULT NULL COMMENT '供应商Email账号',
    `supply_range` DECIMAL(20, 6) DEFAULT NULL COMMENT '供货比例',
    `delete_flag` int(1) DEFAULT 0 COMMENT '删除标志',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_id` bigint(20) DEFAULT NULL COMMENT '创建人Id',
    `creater` varchar(20) DEFAULT NULL COMMENT '创建人名称',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `modifi_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
    `modifier` varchar(20) DEFAULT NULL COMMENT '更新人',
    `modify_date` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='供货比例明细';
-- 2023/3/23 胡梓扬 order_pur采购订单表增加10个备用字段
ALTER TABLE order_pur
ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER pay_name,
ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;
-- 2023/3/23 胡梓扬 order_pur_item采购订单明细表增加10个备用字段
ALTER TABLE order_pur_item
ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER small_set_number,
ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;
-- 2023/3/23 胡梓扬 order_pur采购订单表增加10个备用字段
ALTER TABLE order_pur
ADD COLUMN `erp_approve_date` datetime DEFAULT NULL COMMENT 'ERP订单审核日期' AFTER pay_name;


-- 2023/3/24 招投标历史表增加 类型、备注
ALTER TABLE im_bid_history
    ADD COLUMN `enquiry_type` int(1) DEFAULT NULL COMMENT '是否必填;0-否;1-是;' AFTER `goods_type`,
    ADD COLUMN `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注' AFTER `total_need_amount`;

-- 2023/3/24 询价明细 增加产品分类编码
ALTER TABLE im_enquiry_item
    ADD COLUMN `class_code` VARCHAR(255) DEFAULT NULL COMMENT '物料分类编码' AFTER `class_id`;

ALTER TABLE im_quotation_item
    ADD COLUMN `class_code` VARCHAR(255) DEFAULT NULL COMMENT '物料分类编码' AFTER `class_id`;

-- 2023-03-28 倪林 招投标增加字段
ALTER TABLE im_enquiry
    ADD COLUMN `expand_remark` VARCHAR(500) DEFAULT NULL COMMENT '延长时间备注' AFTER `allow_bid_count`,
    ADD COLUMN `enquiry_round` bigint(2) DEFAULT NULL COMMENT '招标轮数' AFTER `enquiry_way`;


-- 2023-03-31 倪林 招投标管理 供应商列表增加禁止原因
ALTER TABLE im_enquiry_vendor
    ADD COLUMN `disabled_remark` VARCHAR(500) DEFAULT NULL COMMENT '禁止原因' AFTER `remark`;

ALTER TABLE im_quotation
    ADD COLUMN `is_disabled` int(2) DEFAULT NULL COMMENT '是否禁止;0-否;1-是;' AFTER `is_urgent`,
    ADD COLUMN `disabled_remark` VARCHAR(500) DEFAULT NULL COMMENT '禁止原因' AFTER `remark`;


-- 2023-03-31 倪林 招投标管理 增加招标计划方案 明细id 和 供应商明细id
ALTER TABLE im_enquiry_item
    ADD COLUMN `bid_plan_item_id` bigint(20) DEFAULT NULL COMMENT '招标方案明细id' AFTER `enquiry_id`;

ALTER TABLE im_enquiry_vendor
    ADD COLUMN `bid_plan_vendor_id` bigint(20) DEFAULT NULL COMMENT '招标方案供应商明细id' AFTER `enquiry_id`;
-- 2023/3/30 胡梓扬 dm_master_item收货单明细表增加字段
ALTER TABLE dm_master_item
ADD COLUMN `inspection_sheet_id` bigint(21) DEFAULT NULL COMMENT '检验单id' AFTER modify_date,
ADD COLUMN `inspection_sheet_item_id` bigint(21) DEFAULT NULL COMMENT '检验单明细id' AFTER inspection_sheet_id,
ADD COLUMN `inspection_sheet_no` VARCHAR(56) DEFAULT NULL COMMENT '检验单号' AFTER inspection_sheet_item_id,
ADD COLUMN `inspection_results` int(1) DEFAULT NULL COMMENT '检验结果 默认为空 1.合格 2.不合格 3.让步接收' AFTER inspection_sheet_no,
ADD COLUMN `qualified_num` DECIMAL(21,2) DEFAULT NULL COMMENT '检验合格数' AFTER inspection_results,
ADD COLUMN `un_qualified_num` DECIMAL(21,2) DEFAULT NULL COMMENT '检验不合格数' AFTER qualified_num;


-- 2023-03-31 倪林 价格库 增加审批单号
ALTER TABLE im_price_tran
    ADD COLUMN `material_no` VARCHAR(56) DEFAULT NULL COMMENT '审批单号' AFTER `quotation_no`;


-- 2023-04-01 倪林 招标管理 增加招标轮数
ALTER TABLE im_quotation
    ADD COLUMN `enquiry_round` bigint(2) DEFAULT NULL COMMENT '招标轮数' AFTER `enquiry_way`;

-- 2023-04-01 倪林 投标历史 增加招标id
ALTER TABLE im_bid_history
    ADD COLUMN `bid_plan_id` bigint(20) DEFAULT NULL COMMENT '招标计划id(用户获取招标方案中的最低价)' AFTER `enquiry_id`;

ALTER TABLE im_quotation
    ADD COLUMN `bid_plan_id` bigint(20) DEFAULT NULL COMMENT '招标计划id(用户获取招标方案中的最低价)' AFTER `enquiry_id`;

-- 2023/4/6 胡梓扬 dm_master收货单表增加字段
ALTER TABLE dm_master
ADD COLUMN `dept_id` bigint(21) DEFAULT NULL COMMENT '部门机构id' AFTER vendor_name,
ADD COLUMN `dept_code` VARCHAR(56) DEFAULT NULL COMMENT '部门机构编码' AFTER dept_id,
ADD COLUMN `currency_id` bigint(21) DEFAULT NULL COMMENT '币别id' AFTER source_qality_line_id,
ADD COLUMN `currency_code` VARCHAR(56) DEFAULT NULL COMMENT '币别编码' AFTER currency_id,
ADD COLUMN `currency_erp_code` VARCHAR(56) DEFAULT NULL COMMENT '币别erp编码' AFTER currency_code,
ADD COLUMN `currency_name` VARCHAR(56) DEFAULT NULL COMMENT '币别名称' AFTER currency_erp_code,
ADD COLUMN `rate_id` bigint(21) DEFAULT NULL COMMENT '税率id' AFTER currency_name,
ADD COLUMN `rate_code` VARCHAR(56) DEFAULT NULL COMMENT '税率编码' AFTER rate_id,
ADD COLUMN `rate_name` VARCHAR(56) DEFAULT NULL COMMENT '税率名称' AFTER rate_code,
ADD COLUMN `rate_val` DECIMAL(21,2) DEFAULT NULL COMMENT '税率值' AFTER rate_name,
ADD COLUMN `warehouse_id` bigint(21) DEFAULT NULL COMMENT '仓库id' AFTER rate_val,
ADD COLUMN `warehouse_code` VARCHAR(56) DEFAULT NULL COMMENT '仓库编码' AFTER warehouse_id,
ADD COLUMN `warehouse_name` VARCHAR(56) DEFAULT NULL COMMENT '仓库名称' AFTER warehouse_code;
-- 2023/4/6 胡梓扬 dm_master收货单表增加10个备用字段
ALTER TABLE dm_master
ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER warehouse_code,
ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;


-- 2023-04-01 倪林 招标管理 增加招标轮数
ALTER TABLE im_material_price_item
    ADD COLUMN `price_rank` bigint(2) DEFAULT NULL COMMENT '价格排名' AFTER `original_price`,
    ADD COLUMN `reference_price` bigint(2) DEFAULT NULL COMMENT '参考价(原含税单价)' AFTER `price_rank`;


-- 2023-04-11 胡梓扬 送样单增加检验报告文件字段
ALTER TABLE base_sample_item
    ADD COLUMN `inspection_report_file_name` VARCHAR(255) DEFAULT NULL COMMENT '检验报告名称' AFTER `vendor_file_path`,
    ADD COLUMN `inspection_report_file_path` VARCHAR(255) DEFAULT NULL COMMENT '检验报告文件路径' AFTER `inspection_report_file_name`;
-- 2023-04-13 胡梓扬 采购订单主表增加机构id字段
ALTER TABLE order_pur
    ADD COLUMN `dept_id` bigint(21) DEFAULT NULL COMMENT '机构id' AFTER `vendor_name`,
    ADD COLUMN `dept_code` VARCHAR(56) DEFAULT NULL COMMENT '机构编码' AFTER `dept_id`;
-- 2023-04-13 胡梓扬 送货计划主表增加机构id字段
ALTER TABLE dm_delivery_plan
    ADD COLUMN `dept_id` bigint(21) DEFAULT NULL COMMENT '机构id' AFTER `vendor_name`,
    ADD COLUMN `dept_code` VARCHAR(56) DEFAULT NULL COMMENT '机构编码' AFTER `dept_id`;
-- 2023-04-13 胡梓扬 送货计划明细表增加机构id字段
ALTER TABLE dm_delivery_plan_item
    ADD COLUMN `dept_id` bigint(21) DEFAULT NULL COMMENT '机构id' AFTER `un_competent_num`,
    ADD COLUMN `dept_code` VARCHAR(56) DEFAULT NULL COMMENT '机构编码' AFTER `dept_id`,
    ADD COLUMN `dept_name` VARCHAR(56) DEFAULT NULL COMMENT '机构名称' AFTER `dept_code`;
-- 2023-04-17 胡梓扬 收退货明细表增加字段
ALTER TABLE dm_master_item
    ADD COLUMN `source_erp_id` bigint(21) DEFAULT NULL COMMENT 'ERP来源id' AFTER `drawing_no`,
    ADD COLUMN `source_erp_item_id` bigint(21) DEFAULT NULL COMMENT 'ERP来源明细id' AFTER `source_erp_id`,
    ADD COLUMN `source_erp_no` VARCHAR(56) DEFAULT NULL COMMENT 'ERP来源单号' AFTER `source_erp_item_id`;
-- 2023-04-18 胡梓扬 收退货主表增加字段
ALTER TABLE dm_master
    ADD COLUMN `return_type` int(1) DEFAULT NULL COMMENT '退货类型：1.入库单退货；2.采购订单退货' AFTER `warehouse_name`;


-- 2023-04-13 倪林 增加签章id
ALTER TABLE `im_material_price` ADD COLUMN
    `signature_id` BIGINT(20) DEFAULT NULL COMMENT '签章id' AFTER `signature_type`;

-- 2023-04-20 倪林 租户信息
ALTER TABLE `im_material_price` ADD COLUMN
    `dept_name` VARCHAR(255) DEFAULT NULL COMMENT '租户信息' AFTER `tenant_name`;
-- 2023-04-20 胡梓扬 送货计划主表增加缺料数量字段
ALTER TABLE dm_delivery_plan
    ADD COLUMN `shortage_num` decimal(20,2) DEFAULT NULL COMMENT '缺料数量' AFTER `plan_num`;
-- 2023-04-21 胡梓扬 检验评审表增加字段
ALTER TABLE dm_inspection_review
    ADD COLUMN `source_no` VARCHAR(56) DEFAULT NULL COMMENT '来源单号' AFTER `review_no`,
    ADD COLUMN `source_id` BIGINT(21) DEFAULT NULL COMMENT '来源单据id' AFTER `source_no`,
    ADD COLUMN `source_item_id` BIGINT(21) DEFAULT NULL COMMENT '来源单据明细id' AFTER `source_id`,
    ADD COLUMN `order_id` BIGINT(21) DEFAULT NULL COMMENT '采购订单id' AFTER `source_item_id`,
    ADD COLUMN `order_item_id` BIGINT(21) DEFAULT NULL COMMENT '采购订单明细id' AFTER `order_id`,
    ADD COLUMN `order_no` VARCHAR(56) DEFAULT NULL COMMENT '采购订单号' AFTER `order_item_id`,
    ADD COLUMN `un_qualified_num` decimal(20,2) DEFAULT NULL COMMENT '不合格数' AFTER `review_describe`;
-- 2023-04-21 胡梓扬 检验单明细表增加字段
ALTER TABLE dm_inspection_sheet_item
    ADD COLUMN `un_qualified_describe` VARCHAR(2048) DEFAULT NULL COMMENT '不合格描述' AFTER `inspection_report_file_path`;
-- 2023-04-21 胡梓扬 检验单明细表增加字段
ALTER TABLE dm_inspection_sheet_item
    ADD COLUMN `un_qualified_describe` VARCHAR(2048) DEFAULT NULL COMMENT '不合格描述' AFTER `inspection_report_file_path`;
-- 2023-04-23 胡梓扬 收退货明细表增加字段
ALTER TABLE dm_master_item
    ADD COLUMN `warehouse_location` VARCHAR(256) DEFAULT NULL COMMENT '库位' AFTER `warehouse_name`,
    ADD COLUMN `external_unit` VARCHAR(256) DEFAULT NULL COMMENT '外机' AFTER `warehouse_location`,
    ADD COLUMN `internal_unit` VARCHAR(256) DEFAULT NULL COMMENT '内机' AFTER `external_unit`;
-- 2023-04-24 胡梓扬 计划明细表/订单明细表增加字段
ALTER TABLE dm_delivery_plan_item
    ADD COLUMN `refund_num` decimal(21,6) DEFAULT NULL COMMENT '暂退数' AFTER `make_num`;
ALTER TABLE order_pur_item
    ADD COLUMN `matched_plan_num` decimal(21,6) DEFAULT NULL COMMENT '已匹配计划数' AFTER `order_num`;


-- 2023-04-26 倪林 增加租户名称(用于供应商显示)
ALTER TABLE `im_bid_plan`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

ALTER TABLE `im_enquiry`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

ALTER TABLE `im_quotation`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

-- 2023-05-08 胡梓扬 供应商表增加付款方式编码字段
ALTER TABLE `base_vendor`
    ADD COLUMN `pay_code` VARCHAR(56) DEFAULT NULL COMMENT '付款方式编码' AFTER `pay_id`;


-- 2023-04-28 倪林 明细数据增加采购子组织名称
ALTER TABLE `im_enquiry_item`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_enquiry_vendor`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_quotation_item`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_history`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-04 倪林 明细数据增加总金额、总排名
ALTER TABLE `im_quotation_item`
    ADD COLUMN `total_need_amount` decimal(21,6) DEFAULT NULL COMMENT '总需求金额' AFTER `need_amount`,
    ADD COLUMN `total_price_rank` int(2) DEFAULT NULL COMMENT '总需求金额' AFTER `price_rank`;


-- 2023-05-04 倪林 明细数组增加采购组织
ALTER TABLE `im_material_price_item`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_material_price`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_price_tran`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-05 倪林 招标方案增加采购组织
ALTER TABLE `im_bid_plan_vendor`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_plan_doc`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_plan_item`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-06 倪林 维护备用字段
ALTER TABLE im_material_price
    ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER remark,
    ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
    ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
    ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
    ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
    ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
    ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
    ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
    ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
    ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;

ALTER TABLE im_material_price_item
    ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER remark,
    ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
    ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
    ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
    ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
    ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
    ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
    ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
    ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
    ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;

ALTER TABLE im_material_price_item
    ADD COLUMN `price_difference` decimal(21,6) DEFAULT NULL COMMENT '价差' AFTER price_rank;


-- 2023-05-10 倪林 增加上轮报价
ALTER TABLE im_enquiry_item
    ADD COLUMN `last_round_price` decimal(21,6) DEFAULT NULL COMMENT '上轮报价' AFTER cost_price;

ALTER TABLE im_quotation_item
    ADD COLUMN `last_round_price` decimal(21,6) DEFAULT NULL COMMENT '上轮报价' AFTER cost_price;


-- 2023-05-15 倪林 招标方案增加付款方式
ALTER TABLE im_bid_plan
    ADD COLUMN `pay_method_id` VARCHAR(255) DEFAULT NULL COMMENT '付款方式id' AFTER budget_amount,
    ADD COLUMN `pay_method_name` VARCHAR(255) DEFAULT NULL COMMENT '付款方式名称(供应商显示)' AFTER pay_method_id;
-- 2023-05-17 胡梓扬 物料价格库表增加采购组织id，采购组织编码
ALTER TABLE im_price_tran
    ADD COLUMN `pur_org_id` BIGINT(21) DEFAULT NULL COMMENT '采购组织id：来源于sys_dept.id' AFTER tenant_name,
    ADD COLUMN `pur_org_code` VARCHAR(56) DEFAULT NULL COMMENT '采购组织id：来源于sys_dept.dept_code' AFTER pur_org_id;


-- 2023-05-17 倪林 招标方案增加付款方式
ALTER TABLE im_bid_plan
    ADD COLUMN `pay_id` bigint(21) DEFAULT NULL COMMENT '付款方式id' AFTER budget_amount,
    ADD COLUMN `pay_code` VARCHAR(255) DEFAULT NULL COMMENT '付款方式编码' AFTER pay_id,
    ADD COLUMN `pay_name` VARCHAR(255) DEFAULT NULL COMMENT '付款方式名称' AFTER pay_code;


-- 2023-05-18 胡梓扬 送货单表增加采购员id
ALTER TABLE dm_delivery
    ADD COLUMN `purchaser_id` bigint(21) DEFAULT NULL COMMENT '采购员id' AFTER audit_time;


-- 2023-04-26 倪林 增加租户名称(用于供应商显示)
ALTER TABLE `im_bid_plan`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

ALTER TABLE `im_enquiry`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

ALTER TABLE `im_quotation`
    ADD COLUMN `tenant_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `dept_name`;

-- 2023-05-08 胡梓扬 供应商表增加付款方式编码字段
ALTER TABLE `base_vendor`
    ADD COLUMN `pay_code` VARCHAR(56) DEFAULT NULL COMMENT '付款方式编码' AFTER `pay_id`;


-- 2023-04-28 倪林 明细数据增加采购子组织名称
ALTER TABLE `im_enquiry_item`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_enquiry_vendor`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_quotation_item`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_history`
    ADD COLUMN `tenant_p_name` VARCHAR(255) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-04 倪林 明细数据增加总金额、总排名
ALTER TABLE `im_quotation_item`
    ADD COLUMN `total_need_amount` decimal(21,6) DEFAULT NULL COMMENT '总需求金额' AFTER `need_amount`,
    ADD COLUMN `total_price_rank` int(2) DEFAULT NULL COMMENT '总需求金额' AFTER `price_rank`;


-- 2023-05-04 倪林 明细数组增加采购组织
ALTER TABLE `im_material_price_item`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_material_price`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_price_tran`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-05 倪林 招标方案增加采购组织
ALTER TABLE `im_bid_plan_vendor`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_plan_doc`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;

ALTER TABLE `im_bid_plan_item`
    ADD COLUMN `tenant_p_name` VARCHAR(500) DEFAULT NULL COMMENT '采购组织名称' AFTER `tenant_p_id`;


-- 2023-05-06 倪林 维护备用字段
ALTER TABLE im_material_price
    ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER remark,
    ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
    ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
    ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
    ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
    ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
    ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
    ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
    ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
    ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;

ALTER TABLE im_material_price_item
    ADD COLUMN `reserved01` bigint(21) DEFAULT NULL COMMENT '备用字段01' AFTER remark,
    ADD COLUMN `reserved02` bigint(21) DEFAULT NULL COMMENT '备用字段02' AFTER reserved01,
    ADD COLUMN `reserved03` bigint(21) DEFAULT NULL COMMENT '备用字段03' AFTER reserved02,
    ADD COLUMN `reserved04` bigint(21) DEFAULT NULL COMMENT '备用字段04' AFTER reserved03,
    ADD COLUMN `reserved05` bigint(21) DEFAULT NULL COMMENT '备用字段05' AFTER reserved04,
    ADD COLUMN `reserved06` VARCHAR(255) DEFAULT NULL COMMENT '备用字段06' AFTER reserved05,
    ADD COLUMN `reserved07` VARCHAR(255) DEFAULT NULL COMMENT '备用字段07' AFTER reserved06,
    ADD COLUMN `reserved08` VARCHAR(255) DEFAULT NULL COMMENT '备用字段08' AFTER reserved07,
    ADD COLUMN `reserved09` VARCHAR(255) DEFAULT NULL COMMENT '备用字段09' AFTER reserved08,
    ADD COLUMN `reserved10` VARCHAR(255) DEFAULT NULL COMMENT '备用字段10' AFTER reserved09;

ALTER TABLE im_material_price_item
    ADD COLUMN `price_difference` decimal(21,6) DEFAULT NULL COMMENT '价差' AFTER price_rank;


-- 2023-05-10 倪林 增加上轮报价
ALTER TABLE im_enquiry_item
    ADD COLUMN `last_round_price` decimal(21,6) DEFAULT NULL COMMENT '上轮报价' AFTER cost_price;

ALTER TABLE im_quotation_item
    ADD COLUMN `last_round_price` decimal(21,6) DEFAULT NULL COMMENT '上轮报价' AFTER cost_price;


-- 2023-05-15 倪林 招标方案增加付款方式
ALTER TABLE im_bid_plan
    ADD COLUMN `pay_method_id` VARCHAR(255) DEFAULT NULL COMMENT '付款方式id' AFTER budget_amount,
    ADD COLUMN `pay_method_name` VARCHAR(255) DEFAULT NULL COMMENT '付款方式名称(供应商显示)' AFTER pay_method_id;
-- 2023-05-17 胡梓扬 物料价格库表增加采购组织id，采购组织编码
ALTER TABLE im_price_tran
    ADD COLUMN `pur_org_id` BIGINT(21) DEFAULT NULL COMMENT '采购组织id：来源于sys_dept.id' AFTER tenant_name,
    ADD COLUMN `pur_org_code` VARCHAR(56) DEFAULT NULL COMMENT '采购组织id：来源于sys_dept.dept_code' AFTER pur_org_id;


-- 2023-05-17 倪林 招标方案增加付款方式
ALTER TABLE im_bid_plan
    ADD COLUMN `pay_id` bigint(21) DEFAULT NULL COMMENT '付款方式id' AFTER budget_amount,
    ADD COLUMN `pay_code` VARCHAR(255) DEFAULT NULL COMMENT '付款方式编码' AFTER pay_id,
    ADD COLUMN `pay_name` VARCHAR(255) DEFAULT NULL COMMENT '付款方式名称' AFTER pay_code;

-- 2023-05-23 胡梓扬 供应商联系人增加字段
ALTER TABLE base_vendor_contacts
    ADD COLUMN `erp_contacts_code` VARCHAR(56) DEFAULT NULL COMMENT 'ERP联系人编码' AFTER vendor_name,
    ADD COLUMN `sex` int(1) DEFAULT NULL COMMENT '性别' AFTER contact_name,
    ADD COLUMN `company_type` int(1) DEFAULT NULL COMMENT '公司类型' AFTER is_default,
    ADD COLUMN `erp_location_code` VARCHAR(56) DEFAULT NULL COMMENT 'ERP地点编码' AFTER company_type,
    ADD COLUMN `location_name` VARCHAR(255) DEFAULT NULL COMMENT '地点名称' AFTER erp_location_code,
    ADD COLUMN `adderss` VARCHAR(255) DEFAULT NULL COMMENT '详细地址' AFTER location_name;
-- 2023-05-23 胡梓扬 供应商联系人增加字段
ALTER TABLE base_vendor_contacts
    ADD COLUMN `erp_contacts_id` BIGINT(21) DEFAULT NULL COMMENT 'ERP联系人id' AFTER vendor_name;


-- 2023-05-23 倪林 询价管理增加流程id和流程状态
ALTER TABLE `im_enquiry`
    ADD COLUMN `wf_id` bigint(20) COMMENT '启动流程-流程ID' AFTER delete_flag,
    ADD COLUMN `wf_status` int(2)  COMMENT '启动流程 - 流程状态' AFTER wf_id;

-- 2023-06-01 胡梓扬 供货比例增加采购组织信息
ALTER TABLE `im_supply_ratio`
    ADD COLUMN `dept_id` bigint(20) COMMENT '采购组织机构id' AFTER supply_name,
    ADD COLUMN `dept_code` varchar(56) COMMENT '采购组织机构编码' AFTER dept_id,
    ADD COLUMN `dept_name` varchar(56) COMMENT '采购组织机构名称' AFTER dept_code;

-- 2023-06-09 胡梓扬 供货比例增加是否异常字段
ALTER TABLE `im_supply_ratio_item`
    ADD COLUMN `is_abnormal` int(1) DEFAULT NULL COMMENT '是否异常数据 0否,1是' AFTER delete_flag;

-- 2023-06-12 胡梓扬 供货比例增加流程id/流程状态
ALTER TABLE `im_supply_ratio`
    ADD COLUMN `wf_id` bigint(20) COMMENT '流程id' AFTER supply_stat,
    ADD COLUMN `wf_status` int(2) COMMENT '流程状态' AFTER dept_id;

-- 2024-04015 朱兴安 费用单表添加字段
ALTER TABLE sm_account
    ADD COLUMN `account_type`  int(2) COMMENT '费用类型：1-UN_LOAD-卸货 2-RETRIEVE-回收 3-SUBSIDY_INTEREST-贴息 4-PROPERTY-物业管理及租金 5-CHECK-考核 6-OTHER-其他',
    ADD COLUMN `account_date`  datetime COMMENT '费用日期',
	ADD COLUMN `doc_type`  int(1) COMMENT '单据类型：1-SYSTEM-系统录入 2-MANUAL-手工录入',
	ADD COLUMN `rate_code`  varchar(50) COMMENT '税率编码',
	ADD COLUMN `tax_limit`  decimal(20,4) COMMENT '税额',
	ADD COLUMN `abnormal_id`  bigint(20) COMMENT '异常ID',
	ADD COLUMN `abnormal_no`  varchar(50) COMMENT '异常单号',
	ADD COLUMN `abnormal_type`  int(1) COMMENT '异常原因类型：1-UN_QUA-入厂检验不合格 2-NOT_TIMELY-来货不及时 3-PROCESS-过程质量异常 4-AFTER_SALES-售后不良 5-HONEST-廉洁违约 6-TEST_UN_QUA-型式试验不合格 7-OTHER-其他',
    ADD COLUMN `is_statement`  Int(1) DEFAULT  '0' COMMENT '是否已对账:0-否-NO;1-是-YES';

-- 2024-04015 朱兴安 费用单明细表添加字段
ALTER TABLE sm_account_item
    ADD COLUMN `abnormal_item_id`  bigint(20) COMMENT '质量异常单据ID',
	ADD COLUMN `goods_id`  bigint(20) COMMENT '物料ID',
	ADD COLUMN `goods_code`  Varchar(20) COMMENT '物料编码',
	ADD COLUMN `goods_erp_code`  Varchar(20) COMMENT 'ERP物料编码',
	ADD COLUMN `goods_name`  varchar(150) COMMENT '物料名称',
	ADD COLUMN `goods_model`  varchar(150) COMMENT '物料规格型号',
	ADD COLUMN `rate_id`  bigint(20) COMMENT '税率ID;来源于scm_bas_rate.id',
	ADD COLUMN `rate_code`  varchar(20) COMMENT '税率编码',
	ADD COLUMN `rate_name`  varchar(20) COMMENT '税率名称',
	ADD COLUMN `tax_price`  decimal(20,4) COMMENT '不含税单价',
	ADD COLUMN `tax_amount`  decimal(20,4) COMMENT '不含税金额';

-- 2024-0423 zhuxingan 对账单明细表添加表字段
ALTER TABLE sm_statements_item
    ADD COLUMN `tax_amount_ratio`  decimal(20,2) DEFAULT  '0' COMMENT '不含税金额占比',
	ADD COLUMN `share_tax_amount`  decimal(20,4) DEFAULT  '0' COMMENT '需分摊的不含税金额',
	ADD COLUMN `adj_tax_amount`  decimal(20,4) DEFAULT  '0' COMMENT '调整后不含税金额',
	ADD COLUMN `adj_tax_limit`  decimal(20,4) DEFAULT  '0' COMMENT '调整后税额';

-- 2024-0406 朱兴安 送货单物料表添加字段
ALTER TABLE dm_delivery_item
    ADD COLUMN `prod_date`  datetime COMMENT '生产天数',
	ADD COLUMN `prod_batch_no`  Varchar(50) COMMENT '生产批次号',
	ADD COLUMN `warranty_period`  int(10) COMMENT '质保期(质保天数)';

-- 20240430 倪林 通知公告明细加入索引
ALTER TABLE `base_vendor_notice_item`
    ADD INDEX `idx_vendor_id` (`vendor_id`),
    ADD INDEX `idx_is_read` (`is_read`);

-- ******** zhuxingan 供应商变更表字段添加
ALTER TABLE base_vendor_update
    ADD COLUMN `sale_name` VARCHAR ( 50 ) COMMENT '企业负责人',
ADD COLUMN `pur_id` INT ( 10 ) COMMENT '采购员Id',
ADD COLUMN `pur_name` VARCHAR ( 50 ) COMMENT '采购员名称',
ADD COLUMN `vendor_erp_code` VARCHAR ( 50 ) COMMENT '供应商ERP编码',
ADD COLUMN `vendor_full_address` VARCHAR ( 50 ) COMMENT '供应商详细地址',
ADD COLUMN `vendor_tel` VARCHAR ( 50 ) COMMENT '联系人电话',
ADD COLUMN `vendor_bank_number` VARCHAR ( 50 ) COMMENT '供应商银行账号',
ADD COLUMN `bank_line_no` VARCHAR ( 50 ) COMMENT '银行行号',
ADD COLUMN `vendor_account_name` VARCHAR ( 50 ) COMMENT '供应商银行账号名称',
ADD COLUMN `vendor_bank_name` VARCHAR ( 50 ) COMMENT '供应商银行开户行',
ADD COLUMN `sap_title` VARCHAR ( 50 ) COMMENT 'SAP抬头信息',
ADD COLUMN `currency_id` INT ( 10 ) COMMENT 'SAP抬头信息',
ADD COLUMN `currency_name` VARCHAR ( 50 ) COMMENT '交易币别',
ADD COLUMN `rate_id` INT ( 10 ) COMMENT '交易税率',
ADD COLUMN `rate_name` VARCHAR ( 50 ) COMMENT '税率名称',
ADD COLUMN `rate_val` decimal(20,4)  COMMENT '税率值',
ADD COLUMN `soure_id` INT ( 10 ) COMMENT '平台供应商ID',
ADD COLUMN `shortUrl` VARCHAR ( 50 ) COMMENT '供应商邀请短连接';

ALTER TABLE sm_account_item
ADD COLUMN `adj_tax_amount`  decimal(20,4) COMMENT '调整后不含税金额',
ADD COLUMN `adj_tax_limit`  decimal(20,4) COMMENT '调整后税额',
ADD COLUMN `adj_gst_amount`  decimal(20,4) COMMENT '调整后含税金额';

ALTER TABLE sm_account
ADD COLUMN `adj_tax_amount`  decimal(20,4) COMMENT '调整后不含税金额',
ADD COLUMN `adj_tax_limit`  decimal(20,4) COMMENT '调整后税额',
ADD COLUMN `adj_gst_amount`  decimal(20,4) COMMENT '调整后含税金额';

ALTER TABLE base_vendor_update
ADD COLUMN `is_allow_place_order`  int(1) COMMENT '是否允许下订单 0-否 1-是',
ADD COLUMN `is_allow_statement`  int(1) COMMENT '是否允许对账 0-否 1-是';

ALTER TABLE sm_statements
ADD COLUMN `adj_tax_amount`  decimal(20,4) DEFAULT  '0' COMMENT '调整后入库含税金额',
ADD COLUMN `adj_tax_limit`  decimal(20,4) DEFAULT  '0' COMMENT '调整后税额';

/**
  核价参数属性表增加材质ID
 */
ALTER TABLE pr_price_parameter_attr
    ADD COLUMN `material_id` bigint(20) DEFAULT NULL COMMENT '材质ID' AFTER id;
ALTER TABLE pr_price_parameter_attr_change
    ADD COLUMN `material_id` bigint(20) DEFAULT NULL COMMENT '材质ID' AFTER id;

/**
  添加核价参数ID和核价参数单号
 */
ALTER TABLE pr_price_adj
    ADD COLUMN `parameter_id` bigint(20) DEFAULT NULL COMMENT '核价参数ID' AFTER price_model_id,
    ADD COLUMN `price_parameter_no` Varchar(50) DEFAULT NULL COMMENT '核价参数单号' AFTER parameter_id;

/**
  添加来源单据创建人ID和来源单据创建人名称
 */
ALTER TABLE im_price_tran
    ADD COLUMN `source_doc_create_id` bigint(20) DEFAULT NULL COMMENT '来源单据创建人ID' AFTER `remark`,
    ADD COLUMN `source_doc_creater` Varchar(20) DEFAULT NULL COMMENT '来源单据创建人名称' AFTER `source_doc_create_id`;

/**
  添加采购组织ID、编码、名称
 */
ALTER TABLE pr_price_parameter_goods
    ADD COLUMN `dept_id` bigint(20) DEFAULT NULL COMMENT '采购组织ID' AFTER parameter_id,
    ADD COLUMN `dept_code` Varchar(25) DEFAULT NULL COMMENT '采购组织编码' AFTER dept_id,
    ADD COLUMN `dept_name` Varchar(50) DEFAULT NULL COMMENT '采购组织名称' AFTER dept_code;
ALTER TABLE pr_price_parameter_goods_change
    ADD COLUMN `dept_id` bigint(20) DEFAULT NULL COMMENT '采购组织ID' AFTER id,
    ADD COLUMN `dept_code` Varchar(25) DEFAULT NULL COMMENT '采购组织编码' AFTER dept_id,
    ADD COLUMN `dept_name` Varchar(50) DEFAULT NULL COMMENT '采购组织名称' AFTER dept_code;

/**
  添加核价模型单号
 */
ALTER TABLE pr_price_parameter
    ADD COLUMN `price_model_no` varchar (25) DEFAULT NULL COMMENT '核价模型单号' AFTER price_model_id;


/**
  添加映射类型-大宗材质ID、编码、名称
 */
ALTER TABLE pr_grade_difference
    ADD COLUMN `mapping_type_id` bigint(20) DEFAULT NULL COMMENT '映射类型-大宗材质ID' AFTER material_name,
    ADD COLUMN `mapping_type_code` varchar(25) DEFAULT NULL COMMENT '映射类型-大宗材质编码' AFTER price_model_id,
    ADD COLUMN `mapping_type_name` varchar(50) DEFAULT NULL COMMENT '映射类型-大宗材质名称' AFTER mapping_type_code;

/**
  添加是否供货资格调整
 */
ALTER TABLE base_vendor_update
    ADD COLUMN `is_sup_qua_update` int(1) DEFAULT 0 COMMENT '是否供货资格调整 0-否 1-是';

/**
  添加对账业务人员信息
 */
ALTER TABLE base_company
    ADD COLUMN `sup_rec_personnel` varchar(20) DEFAULT NULL COMMENT '供方对账业务人员名称',
    ADD COLUMN `sup_rec_phone` varchar(50) DEFAULT NULL COMMENT '供方对账业务联系电话',
    ADD COLUMN `sup_rec_qq` varchar(50) DEFAULT NULL COMMENT '供方对账业务QQ账号',
    ADD COLUMN `sup_rec_wechat` varchar(50) DEFAULT NULL COMMENT '供方对账业务企业微信/个人微信账号',
    ADD COLUMN `sup_rec_e_mail` varchar(100) DEFAULT NULL COMMENT '供方对账业务电子邮箱';

/**
  添加对账业务人员信息
 */
ALTER TABLE base_vendor
    ADD COLUMN `sup_rec_personnel` varchar(20) DEFAULT NULL COMMENT '供方对账业务人员名称',
    ADD COLUMN `sup_rec_phone` varchar(50) DEFAULT NULL COMMENT '供方对账业务联系电话',
    ADD COLUMN `sup_rec_qq` varchar(50) DEFAULT NULL COMMENT '供方对账业务QQ账号',
    ADD COLUMN `sup_rec_wechat` varchar(50) DEFAULT NULL COMMENT '供方对账业务企业微信/个人微信账号',
    ADD COLUMN `sup_rec_e_mail` varchar(100) DEFAULT NULL COMMENT '供方对账业务电子邮箱';

/**
  添加对账业务人员信息
 */
ALTER TABLE base_vendor_update
    ADD COLUMN `sup_rec_personnel` varchar(20) DEFAULT NULL COMMENT '供方对账业务人员名称',
    ADD COLUMN `sup_rec_phone` varchar(50) DEFAULT NULL COMMENT '供方对账业务联系电话',
    ADD COLUMN `sup_rec_qq` varchar(50) DEFAULT NULL COMMENT '供方对账业务QQ账号',
    ADD COLUMN `sup_rec_wechat` varchar(50) DEFAULT NULL COMMENT '供方对账业务企业微信/个人微信账号',
    ADD COLUMN `sup_rec_e_mail` varchar(100) DEFAULT NULL COMMENT '供方对账业务电子邮箱';

/**
  增加来源单据创建人ID和来源单据创建人名称
 */
ALTER TABLE qua_supply_black_archives
    ADD COLUMN `source_doc_create_id` bigint(20) DEFAULT NULL COMMENT '来源单据创建人ID' AFTER remark,
    ADD COLUMN `source_doc_creater` varchar(20) DEFAULT NULL COMMENT '来源单据创建人名称' AFTER source_doc_create_id;


/**
  增加来源单据创建人ID和来源单据创建人名称
 */
ALTER TABLE pr_material_price_archives
    ADD COLUMN `source_doc_create_id` bigint(20) DEFAULT NULL COMMENT '来源单据创建人ID' AFTER remark,
    ADD COLUMN `source_doc_creater` varchar(20) DEFAULT NULL COMMENT '来源单据创建人名称' AFTER source_doc_create_id;


/**
  添加材质具体名称
 */
ALTER TABLE pr_price_parameter_attr
    ADD COLUMN `material_specific_name` Varchar(255) DEFAULT NULL COMMENT '材质具体名称' AFTER material_id;

/*
  添加变更人员ID、名称、日期
 */
ALTER TABLE pr_price_parameter
    ADD COLUMN `change_personnel_id` bigint(20) DEFAULT NULL COMMENT '变更人员ID' AFTER remark,
    ADD COLUMN `change_personnel_name` varchar(20) DEFAULT NULL COMMENT '变更人员名称' AFTER change_personnel_id,
    ADD COLUMN `change_date` datetime DEFAULT NULL COMMENT '变更日期' AFTER change_personnel_name;

/**
  添加变更发起人员ID、名称、日期
 */
ALTER TABLE pr_parameter_history
    ADD COLUMN `change_promoter_id` bigint(20) DEFAULT NULL COMMENT '变更发起人员ID' AFTER remark,
    ADD COLUMN `change_promoter` varchar(25) DEFAULT NULL COMMENT '变更发起人员名称' AFTER change_promoter_id,
    ADD COLUMN `change_initiation_date` datetime DEFAULT NULL COMMENT '变更发起日期' AFTER change_promoter;


/**
  添加是否手工费用创建
 */
ALTER TABLE sm_account
    ADD COLUMN `is_manual` int(1) DEFAULT 0 COMMENT '是否手工费用创建 0-否 1-是' AFTER is_statement;

/**
  添加费用日期
 */
ALTER TABLE sm_account_price
    ADD COLUMN `account_date` datetime DEFAULT NULL COMMENT '费用日期' AFTER vendor_name;

/**
  添加退货数量
 */
ALTER TABLE dm_delivery_item
    ADD COLUMN `ret_qty` DECIMAL(20,6) DEFAULT 0 COMMENT '退货数量' AFTER inv_num;


/**
  添加流程类型、是否有效、有效开始日期、有效结束日期
 */
ALTER TABLE base_vendor
    ADD COLUMN `flow_type` int(2) DEFAULT NULL COMMENT '流程类型 1-普通 2-特供' AFTER wf_status,
    ADD COLUMN `is_valid` int(1) DEFAULT 1 COMMENT '是否有效（暂时只做显示）' AFTER flow_type,
    ADD COLUMN `eff_start_date` datetime DEFAULT NULL COMMENT '有效开始日期' AFTER is_valid,
    ADD COLUMN `eff_end_date` datetime DEFAULT NULL COMMENT '有效结束日期' AFTER eff_start_date;

/**
  添加接口状态、接口信息、接口日期
 */
ALTER TABLE im_supply_ratio_item
    ADD COLUMN `interface_status` int(1) DEFAULT 0 COMMENT '接口状态 0-未同步 1-同步成功 -1-同步失败' AFTER if_afferent_sap,
    ADD COLUMN `interface_msg` varchar(512) DEFAULT NULL COMMENT '接口信息' AFTER interface_status,
    ADD COLUMN `interface_date` datetime DEFAULT NULL COMMENT '接口同步日期' AFTER interface_msg;

ALTER TABLE dm_inspection_review
    ADD COLUMN `inspection_report_file_name` VARCHAR(255) DEFAULT NULL COMMENT '检验报告附件名称' AFTER `account_no`,
    ADD COLUMN `inspection_report_file_path` VARCHAR(255) DEFAULT NULL COMMENT '检验报告附件路径' AFTER `inspection_report_file_name`;

-- 材质基础表添加是否基准价
ALTER TABLE pr_material_basis
    ADD COLUMN `is_bese_price` INT(1) DEFAULT NULL COMMENT '是否基准价 0-否 1-是' AFTER `grade_difference`;

-- 材质价格申请单增加是否基准价
ALTER TABLE pr_material_price_apply
    ADD COLUMN `is_bese_price` INT(1) DEFAULT NULL COMMENT '是否基准价 0-否 1-是' AFTER `is_raw_material`;

-- 材质价格申请单明细增加金额范围
ALTER TABLE pr_material_price_apply_item
    ADD COLUMN `start_amount` decimal(20,4) DEFAULT NULL COMMENT '开始金额范围' AFTER `rf_range`,
    ADD COLUMN `end_amount` decimal(20,4) DEFAULT NULL COMMENT '结束金额范围' AFTER `start_amount`;

-- 材质价格档案库中增加是否基准价和金额范围
ALTER TABLE pr_material_price_archives
    ADD COLUMN `is_base_price` INT(1) DEFAULT NULL COMMENT '是否基准价 0-否 1-是' AFTER `price_apply_no`,
    ADD COLUMN `start_amount` decimal(20,4) DEFAULT NULL COMMENT '开始金额范围' AFTER `rf_range`,
    ADD COLUMN `end_amount` decimal(20,4) DEFAULT NULL COMMENT '结束金额范围' AFTER `start_amount`;

-- 新增档位基数表
create table  `pr_gear_position` (
    `tenant_p_id`  bigint(20) DEFAULT  '0' NOT NULL  COMMENT '关联组织ID，默认为0',
    `tenant_id`  bigint(20) NOT NULL  COMMENT '组织ID',
    `id`  bigint(20) NOT NULL  AUTO_INCREMENT  COMMENT '主键ID',
    `material_id`  bigint(20) COMMENT '材质ID',
    `material_code`  Varchar(25) COMMENT '材质编码',
    `material_name`  Varchar(50) COMMENT '材质名称',
    `is_basis`  int(1) COMMENT '是否基础类型',
    `float_price`  decimal(20,4) COMMENT '浮动价',
    `range_interval`  decimal(20,4) COMMENT '范围区间',
    `is_valid`  int(1) DEFAULT  '1' COMMENT '是否有效 0-NO-否 1-YES-是',
    `delete_flag`  int(1) DEFAULT  '0' COMMENT '是否已删除 0-未删除 1-已删除',
    `reserved01`  bigint(20) COMMENT '备用字段01',
    `reserved02`  bigint(20) COMMENT '备用字段02',
    `reserved03`  bigint(20) COMMENT '备用字段03',
    `reserved04`  bigint(20) COMMENT '备用字段04',
    `reserved05`  bigint(20) COMMENT '备用字段05',
    `reserved06`  Varchar(64) COMMENT '备用字段06',
    `reserved07`  Varchar(64) COMMENT '备用字段07',
    `reserved08`  Varchar(64) COMMENT '备用字段08',
    `reserved09`  Varchar(64) COMMENT '备用字段09',
    `reserved10`  Varchar(64) COMMENT '备用字段10',
    `remark`  Varchar(500) COMMENT '备注',
    `create_id`  bigint(20) COMMENT '创建人Id',
    `creater`  Varchar(20) COMMENT '更新人名称',
    `create_date`  Datetime COMMENT '创建时间',
    `modifi_id`  bigint(20) COMMENT '创建人Id',
    `modifier`  Varchar(20) COMMENT '更新时间',
    `modify_date`  Datetime COMMENT '更新人',
primary key  (`id`))engine=innodb auto_increment=9 default charset=utf8 comment='档位基数表';




-- 材质价格申请单明细增加金额范围
ALTER TABLE pr_material_price_apply_item
    ADD COLUMN `float_price` decimal(20,4) DEFAULT NULL COMMENT '浮动价' AFTER `end_date`,
    ADD COLUMN `range_interval` decimal(20,4) DEFAULT NULL COMMENT '区间范围' AFTER `float_price`;

-- 材质价格档案库中增加是否基准价和金额范围
ALTER TABLE pr_material_price_archives
    ADD COLUMN `float_price` decimal(20,4) DEFAULT NULL COMMENT '浮动价' AFTER `end_date`,
    ADD COLUMN `range_interval` decimal(20,4) DEFAULT NULL COMMENT '区间范围' AFTER `float_price`;



ALTER TABLE `dm_master`
    ADD COLUMN `ret_method_type` int(2) NULL COMMENT '退料方式：1-退料补料 2-退料并扣款' AFTER `warehouse_code`;
ALTER TABLE `dm_master`
    ADD COLUMN `source_type` int(2) NULL COMMENT '来源类型：1-按送货单 2-按采购订单 3-按销售订单 4-按无来源' AFTER `ret_method_type`;


ALTER TABLE `srm_uat`.`order_sale_item`
    ADD COLUMN `gst_amount` decimal(20, 6) NULL COMMENT '含税金额' AFTER `gst_price`,
    ADD COLUMN `tax_amount` decimal(20, 6) NULL COMMENT '不含税金额' AFTER `tax_price`;

ALTER TABLE `srm_uat`.`order_pur_item`
    ADD COLUMN `erp_change_type` varchar(20) NULL COMMENT 'ERP变更类型' AFTER `delivery_type`;
ALTER TABLE `srm_uat`.`order_sale_item`
    ADD COLUMN `erp_change_type` varchar(20) NULL COMMENT 'ERP变更类型';

/**
  im_material_price 添加外部系统单据号和ID
 */
ALTER TABLE `im_material_price`
    ADD COLUMN `external_no` varchar(30) NULL COMMENT '外部系统单据号' AFTER `special_approval_name`,
    ADD COLUMN `external_id` bigint(20) NULL COMMENT '外部系统单据ID' AFTER `external_no`;

ALTER TABLE `im_material_price_item`
    ADD COLUMN `external_no` varchar(30) NULL COMMENT '外部系统单据号' AFTER `special_approval_name`,
    ADD COLUMN `external_id` bigint(20) NULL COMMENT '外部系统单据ID' AFTER `external_no`,
    ADD COLUMN `external_row_id` bigint(20) NULL COMMENT '外部系统单据行ID' AFTER `external_id`;


ALTER TABLE `im_price_tran`
    ADD COLUMN `external_no` varchar(30) NULL COMMENT '外部系统单据号' AFTER `price_adj_no`,
    ADD COLUMN `external_id` bigint(20) NULL COMMENT '外部系统单据ID' AFTER `external_no`,
    ADD COLUMN `external_row_id` bigint(20) NULL COMMENT '外部系统单据行ID' AFTER `external_id`;

-- 送样需求单物料明细需求状态字段（case_stat）
ALTER TABLE `srm_uat`.`base_sample_demand_item`
    ADD COLUMN `case_stat` int(2) NULL COMMENT '需求状态：1-制单 2-已下发 3-已拒绝 4-已退回 5-已完成';

-- 送样需求单物料明细行退回原因字段（return_cause）
ALTER TABLE `srm_uat`.`base_sample_demand_item`
    ADD COLUMN `return_cause` varchar(500) NULL COMMENT '退回原因';

-- 送样需求单供应商明细行分配人字段（assigner）
ALTER TABLE `srm_uat`.`base_sample_demand_vendor`
    ADD COLUMN `assigner` varchar(20) NULL COMMENT '分配人';

-- 送样需求单供应商明细行分配时间字段（assign_date）
ALTER TABLE `srm_uat`.`base_sample_demand_vendor`
    ADD COLUMN `assign_date` Datetime NULL COMMENT '分配时间';

-- 送样通知单单据类型字段（demand_class_type）
ALTER TABLE `srm_uat`.`base_sample`
    ADD COLUMN `demand_class_type` int(2) NULL COMMENT '1-内部打样 2-采购打样';

-- 库存填报单基本单位字段（uom_name）
ALTER TABLE `srm_uat`.`dm_inventory`
    ADD COLUMN `uom_name` varchar(56) NULL COMMENT '基本单位';

-- 送样通知单回复数量字段（reply_quantity）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `reply_quantity` decimal(20,2) NULL COMMENT '回复数量';

-- 送样通知单回复交期字段（reply_delivery_date）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `reply_delivery_date` Datetime NULL COMMENT '回复交期';

-- 送样通知单物料明细行采购员名称字段（pur_name）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `pur_name` varchar(50) NULL COMMENT '采购员名称';

-- 送样通知单物料明细行采购员编码字段（pur_code）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `pur_code` varchar(50) NULL COMMENT '采购员编码';

-- 送样通知单物料明细行采购员id字段（pur_id）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `pur_id` bigint(20) NULL COMMENT '采购员id';

-- 送样通知单答交状态字段（reply_state）
ALTER TABLE `srm_uat`.`base_sample_item`
    ADD COLUMN `reply_state` int(2) NULL COMMENT '答交状态：1-未答交 2-已答交';

ALTER TABLE `srm_uat`.`dm_delivery_item`
    ADD COLUMN `is_source_to_plan` int(1) NULL COMMENT '是否来源于计划 0-否 1-是' AFTER `warranty_period`;

-- 送样通知单申请人字段（applicant）
ALTER TABLE `srm_uat`.`base_sample`
    ADD COLUMN `applicant` varchar(50) NULL COMMENT '申请人';

-- 送样通知单申请部门名称字段（apply_dept_name）
ALTER TABLE `srm_uat`.`base_sample`
    ADD COLUMN `apply_dept_name` varchar(50) NULL COMMENT '申请部门名称';

-- 送样通知单申请日期字段（apply_date）
ALTER TABLE `srm_uat`.`base_sample`
    ADD COLUMN `apply_date` Datetime NULL COMMENT '申请日期';
