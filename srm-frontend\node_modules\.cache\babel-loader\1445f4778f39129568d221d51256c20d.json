{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue", "mtime": 1754292527998}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _regeneratorRuntime2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/regeneratorRuntime.js\"));\nrequire(\"core-js/modules/es6.regexp.split\");\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es6.number.is-integer\");\nvar _vuePluginHiprint = require(\"vue-plugin-hiprint\");\nvar _elementUi = require(\"element-ui\");\nvar _printTemplate = require(\"@/api/base/printTemplate\");\nvar _goodsBarCode = require(\"@/api/base/goodsBarCode\");\nvar _sale = require(\"@/api/order/sale\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar hiprintTemplate;\n//业务导入\nvar _default = {\n  name: \"printDesign\",\n  props: {\n    title: {\n      type: String,\n      default: '打印条码'\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      btnLoading: false,\n      template: '',\n      templateId: 1,\n      templateList: [],\n      packagingNum: '',\n      numberOfBoxes: null,\n      countless: null,\n      devSumNum: null,\n      goodsCode: null,\n      goodsName: null,\n      dataForm: {\n        id: null,\n        tenantPId: 0,\n        templateName: '',\n        sceneId: null,\n        sceneCode: '',\n        sceneName: '',\n        remark: '',\n        printJson: '',\n        devSumNum: ''\n      },\n      deLineData: {},\n      printData: [],\n      curPaper: {\n        type: 'A4',\n        width: 210,\n        height: 296.6\n      },\n      isBatchPrint: true,\n      paperTypes: {\n        'A3': {\n          width: 420,\n          height: 296.6\n        },\n        'A4': {\n          width: 210,\n          height: 296.6\n        },\n        'A5': {\n          width: 210,\n          height: 147.6\n        },\n        'B3': {\n          width: 500,\n          height: 352.6\n        },\n        'B4': {\n          width: 250,\n          height: 352.6\n        },\n        'B5': {\n          width: 250,\n          height: 175.6\n        }\n      },\n      scaleMax: 5,\n      scaleMin: 0.5,\n      paperWidth: '210',\n      paperHeight: '296.6',\n      dataRule: {\n        goodsCode: [{\n          required: true,\n          message: '请选择物料',\n          trigger: 'blur'\n        }],\n        productDate: [{\n          required: true,\n          message: '请选择生产日期',\n          trigger: 'blur'\n        }],\n        bigPackStandardNum: [{\n          required: true,\n          message: '请输入条码大包数量',\n          trigger: 'blur'\n        }],\n        smallPackStandardNum: [{\n          required: true,\n          message: '请输入条码小包数量',\n          trigger: 'blur'\n        }],\n        devNum: [{\n          required: true,\n          message: '请输入送货数量',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: {\n    curPaperType: function curPaperType() {\n      var type = 'other';\n      var types = this.paperTypes;\n      for (var key in types) {\n        var item = types[key];\n        var _this$curPaper = this.curPaper,\n          width = _this$curPaper.width,\n          height = _this$curPaper.height;\n        if (item.width === width && item.height === height) {\n          type = key;\n        }\n      }\n      return type;\n    }\n  },\n  mounted: function mounted() {},\n  watch: {\n    'deLineData.bigPackStandardNum': {\n      handler: function handler(newVal) {\n        // 将 newVal 强制转为数值类型\n        var numericNewVal = parseInt(newVal, 10);\n\n        // 校验 devNum 是否为有效正整数\n        var devNum = this.deLineData.devNum;\n        if (typeof devNum !== 'number' || devNum <= 0 || !Number.isInteger(devNum)) {\n          this.deLineData.bigPackLabelNum = 0;\n          this.deLineData.bigPackMantissa = 0;\n          return;\n        }\n\n        // 校验 newVal 是否为有效除数（已转为数值）\n        if (isNaN(numericNewVal) || numericNewVal <= 0) {\n          this.deLineData.bigPackStandardNum = devNum;\n        }\n\n        // 计算大包条码张数和尾数\n        this.deLineData.bigPackLabelNum = Math.floor(devNum / numericNewVal);\n        this.deLineData.bigPackMantissa = devNum % numericNewVal;\n      }\n    },\n    'deLineData.smallPackStandardNum': {\n      deep: true,\n      handler: function handler(newVal) {\n        // 将 newVal 强制转为数值类型\n        var numericNewVal = parseInt(newVal, 10);\n\n        // 校验 devNum 是否为有效正整数\n        var devNum = this.deLineData.devNum;\n        if (typeof devNum !== 'number' || devNum <= 0 || !Number.isInteger(devNum)) {\n          this.deLineData.smallPackLabelNum = 0;\n          this.deLineData.smallPackMantissa = 0;\n          return;\n        }\n\n        // 校验 newVal 是否为有效除数（已转为数值）\n        if (isNaN(numericNewVal) || numericNewVal <= 0) {\n          this.deLineData.smallPackStandardNum = devNum;\n        }\n\n        // 计算大包条码张数和尾数\n        this.deLineData.smallPackLabelNum = Math.floor(devNum / numericNewVal);\n        this.deLineData.smallPackMantissa = devNum % numericNewVal;\n      }\n    }\n  },\n  methods: {\n    initData: function initData(params) {\n      this.deLineData = this.$options.data().deLineData;\n      // 使用 JSON 深拷贝避免引用污染\n      this.deLineData = JSON.parse(JSON.stringify(params));\n    },\n    init: function () {\n      var _init = (0, _asyncToGenerator2.default)( /*#__PURE__*/(0, _regeneratorRuntime2.default)().mark(function _callee(sceneCode) {\n        var _this = this;\n        return (0, _regeneratorRuntime2.default)().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // $('#hiprint-printTemplate').empty()\n              this.loading = true;\n              //查场景数据\n              (0, _printTemplate.getPrintTemplateList)({\n                sceneCode: sceneCode\n              }).then(function (res) {\n                _this.templateList = res.page.list;\n                //查模板数据\n                // getPrintTemplateInfo(id).then((res) => {\n                _this.dataForm = res.page.list[res.page.list.length - 1];\n                _this.templateId = _this.dataForm.id;\n                _this.template = JSON.parse(_this.dataForm.printJson.split(\"\").length > 0 ? _this.dataForm.printJson : '{}');\n                var newVar = _this;\n                newVar.hiprint = function () {\n                  _vuePluginHiprint.hiprint.init({\n                    providers: [new _vuePluginHiprint.defaultElementTypeProvider()]\n                  });\n                  hiprintTemplate = new _vuePluginHiprint.hiprint.PrintTemplate({\n                    template: this.template,\n                    // 模板json\n                    settingContainer: '#PrintElementOptionSetting',\n                    // 元素参数容器\n                    paginationContainer: '.hiprint-printPagination' // 多面板的容器， 实现多面板， 需要在添加一个 <div class=\"hiprint-printPagination\"/>\n                  });\n\n                  hiprintTemplate.design('#hiprint-printTemplate');\n                };\n                _this.hiprint();\n                _this.visible = true;\n              });\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function init(_x) {\n        return _init.apply(this, arguments);\n      }\n      return init;\n    }(),\n    getTemplate: function getTemplate(data) {\n      this.dataForm = data;\n      this.templateId = this.dataForm.id;\n      this.$forceUpdate();\n      this.template = JSON.parse(this.dataForm.printJson.split(\"\").length > 0 ? this.dataForm.printJson : '{}');\n      var newVar = this;\n      newVar.hiprint = function () {\n        _vuePluginHiprint.hiprint.init({\n          providers: [new _vuePluginHiprint.defaultElementTypeProvider()]\n        });\n        hiprintTemplate = new _vuePluginHiprint.hiprint.PrintTemplate({\n          template: this.template,\n          // 模板json\n          settingContainer: '#PrintElementOptionSetting',\n          // 元素参数容器\n          paginationContainer: '.hiprint-printPagination' // 多面板的容器， 实现多面板， 需要在添加一个 <div class=\"hiprint-printPagination\"/>\n        });\n        // var panel = hiprintTemplate.addPrintPanel(this.template);\n        hiprintTemplate.design('#hiprint-printTemplate');\n      };\n      this.hiprint();\n    },\n    print: function () {\n      var _print = (0, _asyncToGenerator2.default)( /*#__PURE__*/(0, _regeneratorRuntime2.default)().mark(function _callee2(type) {\n        var _this2 = this;\n        var html, i, dd, printerList;\n        return (0, _regeneratorRuntime2.default)().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              this.printData = [];\n              _context2.next = 3;\n              return (0, _goodsBarCode.queryGoodsBigBagAndSmallBarCodeListV2)({\n                sourceId: this.deLineData.sourceId,\n                goodsId: this.deLineData.goodsId,\n                sourceType: 1,\n                barType: type\n              }).then(function (res) {\n                _this2.printData = res.data;\n              }).catch(function (err) {\n                console.log(err);\n              });\n            case 3:\n              if (!(!Array.isArray(this.printData) || this.printData.length === 0)) {\n                _context2.next = 6;\n                break;\n              }\n              this.$message.error('请选择打印数据');\n              return _context2.abrupt(\"return\");\n            case 6:\n              console.log('数据', this.printData);\n              if (this.isBatchPrint) {\n                html = \"\";\n                for (i = 0; i < this.printData.length; i++) {\n                  html += hiprintTemplate.getHtml(this.printData[i])[0].innerHTML;\n                }\n                dd = hiprintTemplate.getHtml(this.printData[0]);\n                dd[0].innerHTML = html;\n                dd.hiwprint();\n              } else {\n                hiprintTemplate.print(this.printData);\n              }\n              if (window.hiwebSocket.opened) {\n                printerList = hiprintTemplate.getPrinterList();\n                console.log(printerList); // 打印机列表this.tasksPrint()return}this.$message.error('客户端未连接,无法直接打印')\n              }\n            case 9:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function print(_x2) {\n        return _print.apply(this, arguments);\n      }\n      return print;\n    }(),\n    // 生成条码\n    generateBarcode: function generateBarcode() {\n      var _this3 = this;\n      debugger;\n      this.$refs['deLineData'].validate(function (valid) {\n        if (valid) {\n          if (_this3.deLineData.bigPackStandardNum < _this3.deLineData.smallPackStandardNum) {\n            _this3.$message.error('大包条码数量不能小于小包条码数量! 请重新调整大包条码数量');\n            return;\n          }\n          if (_this3.deLineData.smallPackStandardNum > _this3.deLineData.bigPackStandardNum) {\n            _this3.$message.error('小包条码数量不能大于大包条码数量! 请重新调整小包条码数量');\n            return;\n          }\n          if (_this3.deLineData.bigPackStandardNum > _this3.deLineData.devNum) {\n            _this3.$message.error('大包条码数量不能大于订单物料数量! 请重新调整大包条码数量');\n            return;\n          }\n          if (_this3.deLineData.smallPackStandardNum > _this3.deLineData.devNum) {\n            _this3.$message.error('小包条码数量不能大于订单物料数量! 请重新调整小包条码数量');\n            return;\n          }\n          (0, _sale.generateBarcodeByPurOrder)(_this3.deLineData).then(function (res) {\n            _this3.$message.success('条码生成成功');\n          }).catch(function (err) {\n            _this3.$message.error('条码生成失败');\n          });\n        }\n      });\n    }\n  }\n};\nexports.default = _default;", null]}