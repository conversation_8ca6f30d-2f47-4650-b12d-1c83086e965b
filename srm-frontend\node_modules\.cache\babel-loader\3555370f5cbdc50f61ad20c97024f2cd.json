{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue", "mtime": 1754281247272}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _objectSpread2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _sample = require(\"@/api/base/sample\");\nvar _sampleDemand = require(\"@/api/base/sampleDemand\");\nvar _form = _interopRequireDefault(require(\"./form\"));\nvar _qualityForm = _interopRequireDefault(require(\"./qualityForm.vue\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  components: {\n    Form: _form.default,\n    QualityForm: _qualityForm.default\n  },\n  name: 'base-sample-tenant',\n  data: function data() {\n    return {\n      list: [],\n      total: 0,\n      showAll: false,\n      queryParam: {\n        //查询条件\n        sampleNo: '',\n        //需求申请单号\n        ifTenant: 'ifTenant',\n        //用于区分是否为供应商登录，为ifTenant则是采购方\n        sampleStat: '',\n        //主表单据状态\n        itemStat: '',\n        //明细表检验状态\n        vendor: '',\n        //供应商名称/编码\n        page: 1,\n        limit: 20,\n        goods: '',\n        //物料信息\n        dept: '',\n        //采购组织机构信息\n        sampleDate: '',\n        startDate: '',\n        endDate: '',\n        demandDate: '',\n        // 需求日期\n        demandDateStart: '',\n        // 需求开始日期\n        demandDateEnd: '',\n        // 需求结束日期\n        replyDeliveryDate: '',\n        // 回复日期\n        replyDateStart: '',\n        // 回复开始日期\n        replyDateEnd: '' // 回复结束日期\n      },\n\n      detailVisible: false,\n      qualityVisible: false,\n      listLoading: true,\n      btnLoading: false,\n      selectedDatas: [],\n      sampleDates: [],\n      demandDates: [],\n      replyDates: [],\n      // sampleItemStatOptions: store.getters.commonEnums['base.SampleItemEnums'], // 明细行检验状态\n      demandTypeOption: _store.default.getters.commonEnums['base.DemandClassTypeEnum'],\n      // 单据类型\n      sampleStatOptions: [{\n        key: 3,\n        value: '待送样'\n      }, {\n        key: 4,\n        value: '待收样'\n      }, {\n        key: 5,\n        value: '已收样'\n      }, {\n        key: 11,\n        value: '已拒绝'\n      }]\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  filters: {\n    date: function date(time) {\n      if (!time) {\n        return '';\n      }\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = date.getMonth() + 1;\n      var day = date.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    }\n  },\n  methods: {\n    // 页面初始化加载列表数据\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      var query = (0, _objectSpread2.default)({}, this.queryParam);\n      (0, _sample.getSampleList)(query).then(function (res) {\n        debugger;\n        _this.total = res.data.totalCount;\n        _this.list = res.data.list;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    //打开新增/修改弹窗页面\n    addOrUpdateHandle: function addOrUpdateHandle(id) {\n      var _this2 = this;\n      this.detailVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.detail.init(id);\n      });\n    },\n    //审核\n    handleCheck: function handleCheck(id) {},\n    search: function search() {\n      this.queryParam.page = 1;\n      // 处理送样日期\n      if (this.sampleDates.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.sampleDates[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.sampleDates[1], 'YYYY-MM-DD');\n        this.queryParam.sampleDate = startDate + \" 至 \" + endDate;\n        this.queryParam.startDate = startDate;\n        this.queryParam.endDate = endDate;\n      } else {\n        this.queryParam.sampleDate = '';\n        this.queryParam.startDate = '';\n        this.queryParam.endDate = '';\n      }\n\n      // 处理需求日期\n      if (this.demandDates.length !== 0) {\n        var _startDate = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');\n        var _endDate = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');\n        this.queryParam.demandDate = _startDate + \" 至 \" + _endDate;\n        this.queryParam.demandDateStart = _startDate;\n        this.queryParam.demandDateEnd = _endDate;\n      } else {\n        this.queryParam.demandDate = '';\n        this.queryParam.demandDateStart = '';\n        this.queryParam.demandDateEnd = '';\n      }\n\n      // 处理回复日期\n      if (this.replyDates.length !== 0) {\n        var _startDate2 = this.$dian.dateFormat(this.replyDates[0], 'YYYY-MM-DD');\n        var _endDate2 = this.$dian.dateFormat(this.replyDates[1], 'YYYY-MM-DD');\n        this.queryParam.replyDeliveryDate = _startDate2 + \" 至 \" + _endDate2;\n        this.queryParam.replyDateStart = _startDate2;\n        this.queryParam.replyDateEnd = _endDate2;\n      } else {\n        this.queryParam.replyDeliveryDate = '';\n        this.queryParam.replyDateStart = '';\n        this.queryParam.replyDateEnd = '';\n      }\n      this.initData();\n    },\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.sampleDates = [];\n      this.demandDates = [];\n      this.replyDates = [];\n      this.initData();\n    },\n    //删除\n    handleDel: function handleDel(id) {\n      var _this3 = this;\n      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {\n        type: 'warning'\n      }).then(function () {\n        (0, _sample.delSample)(id).then(function (res) {\n          _this3.$message({\n            type: 'success',\n            message: '删除成功',\n            duration: 1500,\n            onClose: function onClose() {\n              _this3.search();\n            }\n          });\n        });\n      }).catch(function () {});\n    },\n    callRefreshList: function callRefreshList() {\n      this.detailVisible = false;\n      this.qualityVisible = false;\n      this.search();\n    },\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n    },\n    quality: function quality(row) {\n      var _this4 = this;\n      this.qualityVisible = true;\n      var isAdd = false;\n      if (row.ifInspection > 0) {\n        isAdd = true;\n      }\n      this.$nextTick(function () {\n        _this4.$refs.quality.init(row.id, isAdd);\n      });\n    },\n    goToSampleDemand: function goToSampleDemand(sourceNo) {\n      var _this5 = this;\n      if (!sourceNo) {\n        this.$message.warning('PLM打样单号不能为空');\n        return;\n      }\n      // 根据sourceNo查询送样需求单\n      var params = {\n        demandNo: sourceNo,\n        demandClassType: 3,\n        // 包括内部打样和采购打样的所有单据\n        limit: 1\n      };\n      (0, _sampleDemand.getSampleDemandList)(params).then(function (res) {\n        if (res.data && res.data.list && res.data.list.length > 0) {\n          var demandId = res.data.list[0].id;\n          var timestamp = Date.now();\n          _this5.$router.push({\n            path: '/base/sampleDemand/tenant',\n            query: {\n              detailVisible: true,\n              id: demandId,\n              t: timestamp\n            }\n          });\n        } else {\n          _this5.$message.warning(\"\\u672A\\u627E\\u5230PLM\\u6253\\u6837\\u5355\\u53F7\\u4E3A\\\"\".concat(sourceNo, \"\\\"\\u7684\\u9001\\u6837\\u9700\\u6C42\\u5355\"));\n        }\n      }).catch(function (error) {\n        console.error('查询送样需求单失败:', error);\n        _this5.$message.error('查询送样需求单失败，请稍后重试');\n      });\n    }\n  }\n};\nexports.default = _default;", null]}