{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue", "mtime": 1754291919657}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _toConsumableArray2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.set\");\nrequire(\"core-js/modules/es6.array.sort\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nrequire(\"core-js/modules/web.dom.iterable\");\nvar _objectSpread2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _dianUiVue = require(\"@dian/dian-ui-vue\");\nvar _Form = _interopRequireDefault(require(\"./Form\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _sale = require(\"@/api/order/sale\");\nvar _auth = require(\"@/utils/auth\");\nvar _dian = _interopRequireDefault(require(\"@/utils/dian\"));\nvar _form = _interopRequireDefault(require(\"@/views/dm/deliveryBoard/vendor/form.vue\"));\nvar _codePrintTemplate = _interopRequireDefault(require(\"./codePrintTemplate\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  //加载底层公有组件\n  mixins: [_dianUiVue.dFlowMixin],\n  name: \"order-produce-vendor\",\n  components: {\n    Form: _Form.default,\n    deliveryForm: _form.default,\n    CodePrintTemplate: _codePrintTemplate.default\n  },\n  filters: {\n    dateFormat: function dateFormat(time) {\n      if (!time) {\n        return '';\n      }\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = date.getMonth() + 1;\n      var day = date.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    }\n  },\n  watch: {\n    $route: {\n      handler: function handler(to, from) {\n        if (to.path === '/order/produce/vendor' && null != to.query.id) {\n          this.addEditOrderHandle(to.query.id);\n        }\n      },\n      immediate: true\n    }\n  },\n  data: function data() {\n    return {\n      bsartTypeOptions: _store.default.getters.commonEnums['order.BsartTypeEnum'],\n      // 订单类型\n      // orderStatOptions: store.getters.commonEnums['order.PurStatEnum'],\n      orderStatOptions: [{\n        key: 1,\n        value: '待确认'\n      }, {\n        key: 4,\n        value: '已确认'\n      }],\n      queryParam: {\n        page: 1,\n        limit: 20,\n        keyword: '',\n        // 订单号/客户名称\n        orderDate: '',\n        // 订单日期\n        dept: '',\n        // 订单日期\n        bsart: '' // 采购类型\n      },\n\n      showAll: false,\n      formVisible: false,\n      deliveryFormVisible: false,\n      templateVisible: false,\n      // 是否显示模板弹窗\n      listLoading: false,\n      btnLoading: false,\n      subCompanyInfo: (0, _auth.getSubCompanyId)() || {},\n      //头部选择的采购组织信息\n      list: [],\n      total: 0,\n      selectedDatas: [],\n      selectedNum: 0\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      var queryParams = (0, _objectSpread2.default)({}, this.queryParam);\n      if (this.queryParam.orderDate && this.queryParam.orderDate.length === 2) {\n        try {\n          var startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\n          var endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\n          queryParams.orderDate = startDate + \" 至 \" + endDate;\n        } catch (error) {\n          console.error('日期格式化错误:', error);\n          queryParams.orderDate = '';\n        }\n      }\n      var subCompanyInfoData = _dian.default.storageGet('subCompanyInfo');\n      if (subCompanyInfoData) {\n        queryParams.deptId = subCompanyInfoData.id;\n      }\n      (0, _sale.getVendorSaleOrderList)(queryParams).then(function (res) {\n        _this.total = res.page.totalCount;\n        _this.list = res.page.list;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    // 新增|编辑 项目报备\n    addEditOrderHandle: function addEditOrderHandle(id) {\n      var _this2 = this;\n      this.formVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.form.init(id);\n      });\n    },\n    // 批量确认\n    oneClickBatchConfirm: function oneClickBatchConfirm() {\n      var _this3 = this;\n      if (this.selectedDatas.length === 0) {\n        this.$message({\n          message: '请至少选择一条记录',\n          type: 'warning',\n          duration: 1500\n        });\n        return;\n      }\n      // 检查是否有已确认的订单\n      var confirmedOrders = this.selectedDatas.filter(function (item) {\n        return item.stat === 5;\n      });\n      if (confirmedOrders.length > 0) {\n        var confirmedOrderNos = confirmedOrders.map(function (item) {\n          return item.purNo;\n        }).join('、');\n        this.$message({\n          message: \"\\u9009\\u4E2D\\u7684\\u8BA2\\u5355\\u4E2D\\u5B58\\u5728\\u5DF2\\u786E\\u8BA4\\u7684\\u8BA2\\u5355\\uFF1A\".concat(confirmedOrderNos, \"\\uFF0C\\u8BF7\\u91CD\\u65B0\\u9009\\u62E9\"),\n          type: 'warning',\n          duration: 3000\n        });\n        return;\n      }\n      this.$confirm('是否批量确认所选订单？确认后将无法撤销', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this3.btnLoading = true;\n        var ids = _this3.selectedDatas.map(function (item) {\n          return item.id;\n        });\n        (0, _sale.batchConfirmOrder)(ids).then(function (res) {\n          _this3.$message({\n            message: '批量确认成功',\n            type: 'success',\n            duration: 1500\n          });\n          _this3.btnLoading = false;\n          _this3.initData();\n        }).catch(function () {\n          _this3.btnLoading = false;\n        });\n      }).catch(function () {});\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    //打开创建送货单弹窗\n    openCreateFrom: function openCreateFrom() {\n      var _this4 = this;\n      //校验是否有选择数据\n      if (this.selectedNum === 0) {\n        this.deliveryFormVisible = true;\n        this.$nextTick(function () {\n          _this4.$refs.deliveryForm.init(null);\n        });\n      } else {\n        //为表格多选选中的数组\n        var list = this.selectedDatas;\n        //校验需要创建送货单的数据\n        this.judgeSelectCreateData(list);\n        var tenantInfo = this.getCreateTenantData(list);\n        var vendorInfo = this.getCreateVendorData(list);\n        var seq = 0;\n        list.forEach(function (item, index) {\n          seq = index + 1;\n          item.id = null;\n          item.seq = seq;\n          item.tempRowId = \"\".concat(new Date().getTime(), \"_\").concat(Math.random().toString(36).slice(2, 10));\n        });\n        list.sort(function (a, b) {\n          if (a['goodsErpCode'] !== b['goodsErpCode']) {\n            return a['goodsErpCode'].localeCompare(b['goodsErpCode']);\n          }\n        });\n        var createData = {\n          tenantId: tenantInfo.tenantId,\n          //采购方id\n          tenantName: tenantInfo.tenantName,\n          //采购方名称\n          vendorId: vendorInfo.vednorId,\n          //供应商id\n          vendorCode: vendorInfo.vendorCode,\n          //供应商编码\n          vendorName: vendorInfo.vendorName,\n          //供应商名称\n          deptId: vendorInfo.deptId,\n          //机构id\n          deptCode: vendorInfo.deptCode,\n          //机构编码\n          deptName: vendorInfo.deptName,\n          //机构名称\n          address: vendorInfo.address,\n          //送货地址\n          shippingAddress: vendorInfo.shippingAddress,\n          //送货地址\n          deliveryType: vendorInfo.deliveryType,\n          //送货类型\n          is: vendorInfo.is,\n          documentDate: new Date(),\n          //创建送货单日期（默认为当前时间）不可修改\n          deliveryDate: new Date(),\n          //实际送货时间（默认当前时间）可修改\n          isEle: 0,\n          //是否物流\n          logisticsName: '',\n          //物流公司\n          eleNo: '',\n          //物流单号\n          purchaserId: vendorInfo.purchaserId,\n          //采购员id\n          deliveryItemEntityList: list //送货单创建明细数据\n        };\n\n        this.deliveryFormVisible = true;\n        this.$nextTick(function () {\n          _this4.$refs.deliveryForm.init(createData);\n        });\n      }\n    },\n    //校验需要创建送货单的数据\n    judgeSelectCreateData: function judgeSelectCreateData(list) {\n      var _this5 = this;\n      //从已选择数据根据采购方（客户）获取每一个租户id\n      var tenantIds = list.map(function (value) {\n        return value.tenantId;\n      });\n      //去除重复的采购方id数据\n      var tenantIdsSet = new Set(tenantIds);\n      //去重后的采购方id数据\n      var tenantIdList = (0, _toConsumableArray2.default)(tenantIdsSet);\n      //若去重后的采购方id数据长度大于1时\n      if (tenantIdList.length > 1) {\n        this.$message.error('只能选择同一个采购方（客户）的单据创建送货单');\n        throw new Error();\n      }\n\n      //从已选择数据根据采购方（客户）获取每一个采购组织机构id\n      var deptIds = list.map(function (value) {\n        return value.deptId;\n      });\n      //去除重复的采购组织id数据\n      var deptIdsSet = new Set(deptIds);\n      //去重后的采购方id数据\n      var deptIdList = (0, _toConsumableArray2.default)(deptIdsSet);\n      //若去重后的采购方id数据长度大于1时\n      if (deptIdList.length > 1) {\n        this.$message.error('只能选择同一个采购组织（客户）的单据创建送货单');\n        throw new Error();\n      }\n\n      //从已选择数据根据地址\n      var address = list.map(function (value) {\n        return value.shippingAddress;\n      });\n      //去除重复的地址数据\n      var addressSet = new Set(address);\n      //去重后的地址数据\n      var addressList = (0, _toConsumableArray2.default)(addressSet);\n      // if(addressList.length > 1){\n      //   this.$message.error('存在不同交货地址的单据不允许创建在同一张送货单');\n      //   throw new Error()\n      // }\n\n      if (tenantIdList[0] === 24739) {\n        //从已选择数据根据送货单过滤获取每一条的订单类型\n        var orderType = list.map(function (value) {\n          return value.orderType;\n        });\n        //去除重复的订单类型数据\n        var orderTypeSet = new Set(orderType);\n        //去重后的订单类型数据\n        var orderTypeList = (0, _toConsumableArray2.default)(orderTypeSet);\n        if (orderTypeList.length > 1) {\n          this.$message.error('存在不同订单类型的单据不允许创建在同一张送货单');\n          throw new Error();\n        }\n\n        //从已选择数据根据送货单过滤获取每一条的订单业务类型\n        var orderBusType = list.map(function (value) {\n          return value.orderBusType;\n        });\n        //去除重复的订单业务类型数据\n        var orderBusTypeSet = new Set(orderBusType);\n        //去重后的订单业务类型数据\n        var orderBusTypeList = (0, _toConsumableArray2.default)(orderBusTypeSet);\n        if (orderBusTypeList.length > 1) {\n          this.$message.error('存在不同订单业务类型的单据不允许创建在同一张送货单');\n          throw new Error();\n        }\n      }\n\n      //从已选择数据根据送货单过滤获取每一条的送货类型\n      var deliveryType = list.map(function (value) {\n        return value.deliveryType;\n      });\n      //去除重复的送货类型数据\n      var deliveryTypeSet = new Set(deliveryType);\n      //去重后的送货类型数据\n      var deliveryTypeList = (0, _toConsumableArray2.default)(deliveryTypeSet);\n      if (deliveryTypeList.length > 1) {\n        this.$message.error('存在不同送货方式的单据不允许创建在同一张送货单');\n        throw new Error();\n      }\n\n      //从已选择数据根据送货单过滤获取每一条的仓库\n      var warehouseCode = list.map(function (value) {\n        return value.warehouseCode;\n      });\n      //去除重复的送货类型数据\n      var warehouseCodeSet = new Set(warehouseCode);\n      //去重后的送货类型数据\n      var warehouseCodeList = (0, _toConsumableArray2.default)(warehouseCodeSet);\n      if (warehouseCodeList.length > 1) {\n        this.$message.error('当前选择的数据中存在不同仓库地址，不允许创建在同一张送货单');\n        throw new Error();\n      }\n\n      //从已选择数据根据采购员过滤获取每一条的送货类型\n      var purId = list.map(function (value) {\n        return value.purId;\n      });\n      //去除重复的采购员数据\n      var purIdSet = new Set(purId);\n      //去重后的采购员数据\n      var purIdList = (0, _toConsumableArray2.default)(purIdSet);\n      // if(purIdList.length > 1){\n      //   this.$message.error('存在两个或两个以上不同采购员的单据不允许创建在同一张送货单');\n      //   throw new Error()\n      // }\n\n      var idx = 0;\n      list.forEach(function (item, index) {\n        idx = index + 1;\n        // 当前日期小于最早可提前送货日期时\n        var nowDate = new Date();\n        var inAdvanceDeliveryDate = new Date(item.inAdvanceDeliveryDate);\n        if (nowDate < inAdvanceDeliveryDate) {\n          _this5.$message.error('目前已选择的数据中存在当前日期小于最早可提前送货日期' + item.inAdvanceDeliveryDate + '，无法创建送货单');\n          throw new Error();\n        }\n        //已选择数据中的可制单数量小于0/等于0\n        if (parseFloat(item.canMakeNum) <= 0) {\n          _this5.$message.error('第' + idx + '行物料[' + item + ']的可制单数量小于等于0，无法创建送货单，请刷新页面');\n          throw new Error();\n        }\n      });\n    },\n    //获取采购方（客户）基本信息\n    getCreateTenantData: function getCreateTenantData(list) {\n      var tenantInfoData = {};\n      //从已选择数据根据采购方（客户）获取每一个租户id\n      var tenantIds = list.map(function (value) {\n        return value.tenantId;\n      });\n      //去除重复的采购方id数据\n      var tenantIdsSet = new Set(tenantIds);\n      //去重后的采购方id数据\n      var tenantIdList = (0, _toConsumableArray2.default)(tenantIdsSet);\n      tenantInfoData.tenantId = tenantIdList[0];\n\n      //从已选择数据根据采购方（客户）获取每一个租户名称\n      var tenantNames = list.map(function (value) {\n        return value.tenantName;\n      });\n      //去除重复的采购方名称数据\n      var tenantNamesSet = new Set(tenantNames);\n      //去重后的采购方名称数据\n      var tenantNameList = (0, _toConsumableArray2.default)(tenantNamesSet);\n      tenantInfoData.tenantName = tenantNameList[0];\n      return tenantInfoData;\n    },\n    //获取当前供应商基本信息\n    getCreateVendorData: function getCreateVendorData(list) {\n      var vendorData = {};\n      //从已选择数据根据供应商id获取每一个供应商id\n      var vednorIds = list.map(function (value) {\n        return value.vendorId;\n      });\n      //去除重复的供应商id数据\n      var vednorIdsSet = new Set(vednorIds);\n      //去重后的供应商id数据\n      var vednorIdList = (0, _toConsumableArray2.default)(vednorIdsSet);\n      vendorData.vednorId = vednorIdList[0];\n\n      //从已选择数据根据机构id获取每一个机构id\n      var deptIds = list.map(function (value) {\n        return value.deptId;\n      });\n      //去除重复的机构id数据\n      var deptIdsSet = new Set(deptIds);\n      //去重后的机构id数据\n      var deptIdList = (0, _toConsumableArray2.default)(deptIds);\n      vendorData.deptId = deptIdList[0];\n\n      //从已选择数据根据机构编码获取每一个机构编码\n      var deptCodes = list.map(function (value) {\n        return value.deptCode;\n      });\n      //去除重复的机构编码数据\n      var deptCodesSet = new Set(deptCodes);\n      //去重后的机构编码数据\n      var deptCodeList = (0, _toConsumableArray2.default)(deptCodesSet);\n      vendorData.deptCode = deptCodeList[0];\n\n      //从已选择数据根据机构名称获取每一个机构名称\n      var deptNames = list.map(function (value) {\n        return value.deptName;\n      });\n      //去除重复的机构名称数据\n      var deptNamesSet = new Set(deptNames);\n      //去重后的机构名称数据\n      var deptNameList = (0, _toConsumableArray2.default)(deptNamesSet);\n      vendorData.deptName = deptNameList[0];\n\n      //从已选择数据根据地址过滤获取每一个地址\n      var address = list.map(function (value) {\n        return value.vendorDeliveryAddress;\n      });\n      //去除重复的地址数据\n      var addressSet = new Set(address);\n      //去重后的地址数据\n      var addressList = (0, _toConsumableArray2.default)(addressSet);\n      vendorData.address = addressList[0];\n\n      //从已选择数据根据地址过滤获取每一个地址\n      var shippingAddress = list.map(function (value) {\n        return value.shippingAddress;\n      });\n      //去除重复的地址数据\n      var shippingAddressSet = new Set(shippingAddress);\n      //去重后的地址数据\n      var shippingAddressList = (0, _toConsumableArray2.default)(shippingAddressSet);\n      vendorData.shippingAddress = shippingAddressList[0];\n\n      //从已选择数据根据送货单过滤获取每一条的送货类型\n      var deliveryType = list.map(function (value) {\n        return value.deliveryType;\n      });\n      //去除重复的送货类型数据\n      var deliveryTypeSet = new Set(deliveryType);\n      //去重后的送货类型数据\n      var deliveryTypeList = (0, _toConsumableArray2.default)(deliveryTypeSet);\n      vendorData.deliveryType = deliveryTypeList[0];\n      if (vendorData.deliveryType) {\n        vendorData.deliveryType = deliveryTypeList[0];\n        if (vendorData.deliveryType == 2) {\n          vendorData.is = 1;\n        } else {\n          vendorData.is = 0;\n        }\n      } else {\n        vendorData.deliveryType = 1;\n        vendorData.is = 0;\n      }\n\n      //从已选择数据根据采购员过滤获取每一条的送货类型\n      var purId = list.map(function (value) {\n        return value.purId;\n      });\n      //去除重复的采购员数据\n      var purIdSet = new Set(purId);\n      //去重后的采购员数据\n      var purIdList = (0, _toConsumableArray2.default)(purIdSet);\n      vendorData.purchaserId = purIdList[0];\n      return vendorData;\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.queryParam.page = 1;\n      this.initData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.search();\n    },\n    // Form表单关闭时回调方法\n    closeForm: function closeForm() {\n      this.formVisible = false;\n      this.deliveryFormVisible = false;\n      this.initData();\n    },\n    // 打印条码\n    printBarCode: function printBarCode(data) {\n      var _this6 = this;\n      this.templateVisible = true;\n      var devNum = Number(data.orderNum) || 0;\n\n      // 从当前行数据中解构赋值获取所需信息\n      var tenantId = data.tenantId,\n        vendorId = data.vendorId,\n        vendorCode = data.vendorCode,\n        vendorName = data.vendorName,\n        sourceId = data.id,\n        sourceNo = data.purNo,\n        goodsId = data.goodsId,\n        goodsCode = data.goodsCode,\n        goodsErpCode = data.goodsErpCode,\n        goodsName = data.goodsName,\n        goodsModel = data.goodsModel,\n        bigPackStandardNum = data.bigPackStandardNum,\n        smallPackStandardNum = data.smallPackStandardNum,\n        bigPackLabelNum = data.bigPackLabelNum,\n        smallPackLabelNum = data.smallPackLabelNum,\n        bigPackMantissa = data.bigPackMantissa,\n        smallPackMantissa = data.smallPackMantissa;\n      var printData = {\n        tenantId: tenantId,\n        tenantPId: 0,\n        vendorId: vendorId,\n        vendorCode: vendorCode,\n        vendorName: vendorName,\n        sourceId: sourceId,\n        sourceNo: sourceNo,\n        goodsId: goodsId,\n        goodsCode: goodsCode,\n        goodsErpCode: goodsErpCode,\n        goodsName: goodsName,\n        goodsModel: goodsModel,\n        sourceType: 1,\n        devNum: devNum,\n        bigPackStandardNum: bigPackStandardNum,\n        smallPackStandardNum: smallPackStandardNum,\n        bigPackLabelNum: bigPackLabelNum,\n        smallPackLabelNum: smallPackLabelNum,\n        bigPackMantissa: bigPackMantissa,\n        smallPackMantissa: smallPackMantissa\n      };\n      this.$nextTick(function () {\n        console.log('$nextTick执行，PrintTemplate引用：', _this6.$refs.PrintTemplate);\n        if (_this6.$refs.PrintTemplate) {\n          // 先直接显示弹窗\n          _this6.$refs.PrintTemplate.visible = true;\n          console.log('手动设置弹窗显示状态为true');\n          _this6.$refs.PrintTemplate.isBatchPrint = false;\n          _this6.$refs.PrintTemplate.initData(printData);\n          _this6.$refs.PrintTemplate.init(\"goodsBarCodePrinting\");\n          console.log('条码打印组件初始化完成');\n        } else {\n          console.error('PrintTemplate组件引用不存在');\n        }\n      });\n    }\n  }\n};\nexports.default = _default;", null]}