{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue", "mtime": 1754042535665}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _toConsumableArray2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.set\");\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _form = _interopRequireDefault(require(\"./form\"));\nvar _delivery = require(\"@/api/dm/delivery\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  name: \"dm-delivery-vendor\",\n  components: {\n    Form: _form.default\n  },\n  data: function data() {\n    return {\n      SortModeOptions: _store.default.getters.commonEnums['dm.DeliveryBoardSortModeEnum'],\n      // 订单类型\n      deStatOptions: _store.default.getters.commonEnums['dm.DeStatEnum'],\n      // 单据状态\n      queryParam: {\n        page: 1,\n        limit: 20,\n        vendorIds: 'vendorIds',\n        //用于区分是否为供应商\n        whereType: 1,\n        //气泡查询条件 默认为1 - 全部\n        vendor: '',\n        // 供应商编码|名称\n        searchStr: '',\n        // 物料编码|名称|描述|图号\n        deliveryDate: '',\n        //创建送货日期\n        startDate: '',\n        // 开始日期\n        orderType: '',\n        // 订单类型\n        deNo: '',\n        // 送货单号\n        orderNo: '',\n        // 订单号\n        dept: '',\n        // 机构组织\n        goods: '',\n        // 物料信息\n        sortObjTwo: '1',\n        //排序方式\n        deStat: '',\n        //单据状态\n        deptId: ''\n      },\n      deliveryDate: [],\n      deliveryCount: {},\n      //气泡数\n      listLoading: false,\n      btnLoading: false,\n      formVisible: false,\n      list: [],\n      //列表数据\n      total: 0,\n      //条数\n      selectedDatas: [],\n      /*选择的数据*/\n      selectedNum: 0,\n      /*选择数据的条数*/\n      userInfo: _store.default.getters.userInfo,\n      /*获取当前用户*/\n      showAll: false,\n      /*收*/\n      // 气泡主题\n      buttonFrom: {\n        count1: 'primary',\n        //全部\n        count2: '',\n        //已送未收\n        count3: '',\n        //待发出\n        count4: '',\n        //暂收\n        count5: '' //暂退\n      }\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      if (this.deliveryDate.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.deliveryDate[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.deliveryDate[1], 'YYYY-MM-DD');\n        this.queryParam.deliveryDate = startDate + \" 至 \" + endDate;\n      }\n      (0, _delivery.getDeliveryList)(this.queryParam).then(function (res) {\n        _this.total = res.data.totalCount;\n        _this.list = res.data.list;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.initData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.deliveryDate = [];\n      this.search();\n    },\n    //打开送货单详情弹窗\n    openInfoForm: function openInfoForm(id) {\n      var _this2 = this;\n      this.formVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.form.init(id);\n      });\n    },\n    //关闭刷新列表数据\n    callDeliveryBoardList: function callDeliveryBoardList() {\n      this.formVisible = false;\n      this.search();\n    },\n    //点击气泡查询\n    changeCountsButton: function changeCountsButton(stat, name) {\n      // 动态变换\n      this.buttonFrom.count1 = ''; //全部\n      this.buttonFrom.count2 = ''; //已送未收\n      this.buttonFrom.count3 = ''; //待发出\n      this.buttonFrom.count4 = ''; //暂收\n      this.buttonFrom.count5 = ''; //暂退\n      this.buttonFrom[name] = 'primary';\n      this.queryParam.whereType = stat;\n      this.search();\n    },\n    delDelivery: function delDelivery() {\n      var _this3 = this;\n      if (this.selectedNum == 0) {\n        this.$message.error('请最少选择1条需要删除的送货单');\n        return;\n      }\n      if (this.selectedNum > 1) {\n        this.$message.error('只能选择删除单个相同的送货单号数据');\n        return;\n      }\n      var deptCodes = this.selectedDatas.map(function (value) {\n        return value.deptCode;\n      });\n      //去除重复的采购组织数据\n      var deptCodeSet = new Set(deptCodes);\n      //去重后的采购组织数据\n      var deptCodeList = (0, _toConsumableArray2.default)(deptCodeSet);\n      if (deptCodeList.length > 1) {\n        this.$message.error('选择删除的数据中存在不同的采购组织');\n        return;\n      }\n      //从已选择数据根据采购方（客户）获取每一个租户id\n      var delIds = this.selectedDatas.map(function (value) {\n        return value.id;\n      });\n      //去除重复的采购方id数据\n      var delIdsSet = new Set(delIds);\n      //去重后的采购方id数据\n      var delIdList = (0, _toConsumableArray2.default)(delIdsSet);\n      this.$confirm('是否确认删除选择的送货单？', '提示', {\n        type: 'warning'\n      }).then(function () {\n        _this3.btnLoading = true;\n        _this3.listLoading = true;\n        (0, _delivery.delDelivery)(delIdList).then(function (res) {\n          _this3.$message({\n            message: \"删除成功\",\n            type: 'success',\n            duration: 1500,\n            onClose: function onClose() {\n              _this3.search();\n            }\n          });\n          _this3.btnLoading = false;\n          _this3.listLoading = false;\n        }).catch(function () {\n          _this3.btnLoading = false;\n          _this3.listLoading = false;\n        });\n      });\n    }\n  }\n};\nexports.default = _default;", null]}