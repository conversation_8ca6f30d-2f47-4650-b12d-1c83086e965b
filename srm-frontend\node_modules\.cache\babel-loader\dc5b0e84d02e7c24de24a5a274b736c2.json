{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue", "mtime": 1754288813438}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/web.dom.iterable\");\nvar _sample = require(\"@/api/base/sample\");\nvar _classManage = require(\"@/api/base/classManage\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _goods = require(\"@/api/base/goods\");\nvar _goodsPopup = _interopRequireDefault(require(\"@/views/popup/base/goods/goodsPopup\"));\nvar _vendor = _interopRequireDefault(require(\"@/views/popup/base/vendor/vendor\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  components: {\n    GoodsPopup: _goodsPopup.default,\n    VendorProp: _vendor.default\n  },\n  data: function data() {\n    return {\n      isUpload: true,\n      isAdd: true,\n      dataForm: {\n        id: 0,\n        //送样通知单主表id\n        tenantId: '',\n        tenantName: '',\n        sampleNo: '',\n        //送样单号\n        vendorId: '',\n        //供应商id\n        vendorCode: '',\n        //供应商编码\n        vendorErpCode: '',\n        //供应商Erp编码\n        vendorName: '',\n        //供应商名称\n        deptId: '',\n        //机构id\n        deptCode: '',\n        //机构编码\n        deptName: '',\n        //机构名称\n        sampleDate: '',\n        //要求送样日期\n        sampleStat: 1,\n        //单据状态 1-待审核;2-待送样;3-待质检;4-质检退回;5-已审核;9-已作废\n        isNeedUpFile: null,\n        remark: '',\n        //备注\n        sampleItemEntityList: [] //送样单明细数据数组\n      },\n\n      selectedMaterialItems: [],\n      // 选中的物料明细行(多选)\n      index: '',\n      docActiveName: 'tenant',\n      // doc文档选项卡\n      btnLoading: false,\n      //控制按钮是否正在加载条件\n      loading: false,\n      //控制页面是否正在加载条件\n      activeName: 'form',\n      statOptions: _store.default.getters.commonEnums['base.SampleEnums'],\n      //送样单单据状态枚举类\n      whetherOpts: _store.default.getters.commonEnums['comm.ValidEnum'],\n      //送样单单据状态枚举类\n      demandTypeOpts: _store.default.getters.commonEnums['base.DemandClassTypeEnum'],\n      dataRule: {\n        isNeedUpFile: [{\n          required: true,\n          message: '是否需要上传资质文件不能为空',\n          trigger: 'input'\n        }],\n        vendorCode: [{\n          required: true,\n          message: '供应商编码不能为空',\n          trigger: 'input'\n        }],\n        vendorName: [{\n          required: true,\n          message: '供应商名称不能为空',\n          trigger: 'input'\n        }]\n      }\n    };\n  },\n  methods: {\n    //新增页面或查看详情\n    init: function init(id) {\n      var _this = this;\n      if (this.$refs.dataForm) ;\n      this.docActiveName = 'vendor';\n      this.$refs.dataForm.resetFields();\n      this.activeName = 'form';\n      this.dataForm = this.$options.data().dataForm;\n      this.isAdd = id ? true : false;\n      this.dataForm.id = this.isAdd ? id : new Date().getTime();\n      this.$nextTick(function () {\n        if (id) {\n          _this.loading = true;\n          (0, _sample.getSampleInfo)(id).then(function (res) {\n            _this.dataForm = res.data;\n            if (_this.dataForm.sampleItemEntityList && _this.dataForm.sampleItemEntityList.length > 0) {\n              _this.dataForm.sampleItemEntityList.forEach(function (item) {\n                if (item.replyQuantity && (!item.goodsNum || item.goodsNum === 0)) {\n                  item.goodsNum = item.replyQuantity;\n                }\n              });\n            }\n            _this.loading = false;\n          }).catch(function (res) {\n            _this.loading = false;\n          });\n        }\n      });\n    },\n    //保存\n    submit: function submit() {\n      var _this2 = this;\n      this.$refs['dataForm'].validate(function (valid) {\n        if (valid) {\n          _this2.btnLoading = true;\n          //明细行数据校验\n          var isLineValid = _this2.lineCheck();\n          if (!isLineValid) {\n            _this2.btnLoading = false;\n            return;\n          }\n          var formMethod = _this2.isAdd ? _sample.updateSample : _sample.createSample;\n          formMethod(_this2.dataForm).then(function (res) {\n            _this2.isAdd = true;\n            _this2.$message({\n              message: \"保存成功\",\n              type: 'success',\n              duration: 1500,\n              onClose: function onClose() {\n                _this2.btnLoading = false;\n                if (res.data) {\n                  _this2.loading = true;\n                  (0, _sample.getSampleInfo)(res.data).then(function (res) {\n                    _this2.dataForm = res.data;\n                    _this2.loading = false;\n                  });\n                }\n              }\n            });\n          }).catch(function (res) {\n            _this2.btnLoading = false;\n          });\n        }\n      });\n    },\n    //返回列表\n    goBack: function goBack() {\n      this.$emit('callRefreshList');\n    },\n    //打开供应商弹窗\n    choiceVendor: function choiceVendor() {\n      var _this3 = this;\n      this.$nextTick(function () {\n        _this3.$refs.vendor.init();\n      });\n    },\n    //获取已选择的供应商赋值\n    vendorSelect: function vendorSelect(data) {\n      this.dataForm.vendorId = data[0].id; //供应商id\n      this.dataForm.vendorCode = data[0].vendorCode; //供应商编码\n      this.dataForm.vendorErpCode = data[0].vendorErpCode; //供应商Erp编码\n      this.dataForm.vendorName = data[0].vendorName; //供应商名称\n    },\n    //打开物料选择弹窗\n    choiceGoods: function choiceGoods() {\n      var _this4 = this;\n      if (!this.dataForm.vendorId) {\n        this.$message.error('供应商信息为空，请先选择供应商');\n        return;\n      }\n      this.$nextTick(function () {\n        _this4.$refs.goods.init();\n      });\n    },\n    //获取已选择的物料\n    getSelectGoods: function getSelectGoods(list) {\n      //当送样明细数据数组长度大于0时，校验选择的数据是否有在需求申请明细数据中已存在，已存在的需要抛出错误信息\n      if (this.dataForm.sampleItemEntityList.length > 0) {\n        for (var i = 0; i < list.length; i++) {\n          var e = list[i];\n          for (var j = 0; j < this.dataForm.sampleItemEntityList.length; j++) {\n            if (this.dataForm.sampleItemEntityList[j].goodsId == e.id) {\n              this.$message.error('选择的产品编码' + e.goodsCode + '已存在，请勿重复添加');\n              return;\n            }\n          }\n        }\n      }\n      this.addSampleItem(list);\n    },\n    //新增明细\n    addSampleItem: function addSampleItem(list) {\n      var _this5 = this;\n      if (list.length > 0) {\n        list.forEach(function (item, index) {\n          var sampleItem = {\n            id: null,\n            tenantId: '',\n            sampleId: '',\n            //送样单主表id\n            goodsId: item.id,\n            //物料id\n            goodsErpCode: item.goodsErpCode,\n            //ERP物料编码\n            goodsCode: item.goodsCode,\n            //物料编码\n            goodsName: item.goodsName,\n            //物料名称\n            goodsModel: item.goodsModel,\n            //物料规格\n            goodsNum: item.goodsNum,\n            //送样数量\n            itemStat: 1,\n            //明细行状态 1-待审核;2-待送样;3-待质检\n            vendorRemark: '',\n            //供应商说明\n            remark: '' //采购方说明\n          };\n\n          _this5.dataForm.sampleItemEntityList.push(sampleItem);\n        });\n      }\n    },\n    //删除单条产品明细\n    handleDel: function handleDel(index, name, row) {\n      this.dataForm[name].splice(index, 1);\n    },\n    //批量明细删除\n    delLine: function delLine(lineName) {\n      var _this6 = this;\n      this.$confirm('是否批量删除所选明细', '提示', {\n        type: 'warning'\n      }).then(function () {\n        var rowThis = _this6;\n        //获取已选择数据\n        rowThis.$refs[lineName].$refs.DIANTable.selection.map(function (orw) {\n          //获取列表数据\n          rowThis.dataForm[lineName].forEach(function (dataRow, index) {\n            //判断是否为同一行\n            if (orw.__ob__.dep.id == dataRow.__ob__.dep.id) {\n              //删除当前行\n              rowThis.dataForm[lineName].splice(index, 1);\n            }\n          });\n        });\n        _this6.$message({\n          type: 'success',\n          message: '删除成功',\n          duration: 1500\n        });\n      }).catch(function () {});\n    },\n    //校验明细行\n    lineCheck: function lineCheck() {\n      if (this.dataForm.sampleItemEntityList.length == 0) {\n        this.btnLoading = false;\n        this.$message.error('请添加物料明细');\n        return false;\n      }\n      // let lineList = this.dataForm.sampleItemEntityList;\n      // for (let i = 0; i < lineList.length; i++) {\n      //   const item = lineList[i];\n      //   const idx = i + 1;\n      //   if(!item.goodsNum){\n      //      this.btnLoading = false;\n      //      this.$message({type: 'error', message: '物料信息第'+idx+'行的送样数量为空'});\n      //      throw new Error()\n      //    }\n      //   // 资质文件校验\n      //   if (this.dataForm.isNeedUpFile === 1 && !item.vendorFilePath) {\n      //     this.btnLoading = false;\n      //     this.$message.error(`物料信息第${idx}行的资质文件为空，请上传`);\n      //     return false; // 校验失败，返回false\n      //   }\n      // }\n      return true; // 校验通过，返回true\n    },\n    // 确认送样\n    confirmSample: function confirmSample() {\n      var _this7 = this;\n      // 检查是否选择了物料明细行\n      if (this.selectedMaterialItems.length === 0) {\n        this.$message.error(\"请选择要确认送样的物料明细行需求\");\n        return;\n      }\n\n      // 检查选中的物料明细行状态\n      var invalidItems = this.selectedMaterialItems.filter(function (item) {\n        return item.replyState != 2;\n      });\n      if (invalidItems.length > 0) {\n        this.$message.error('选中的物料明细中有未答交的需求，无法确认送样');\n        return;\n      }\n\n      // 检查送样数量是否大于0\n      var noQuantityItems = this.selectedMaterialItems.filter(function (item) {\n        return !item.goodsNum || item.goodsNum <= 0;\n      });\n      if (noQuantityItems.length > 0) {\n        this.$message.error('选中的物料明细中有送样数量为空或小于等于0的记录，请先填写送样数量');\n        return;\n      }\n\n      // 检查是否有已确认送样的明细行 (SampleEnums.STAT4 = 4 待收样)\n      var confirmedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.caseStat == 4;\n      });\n      if (confirmedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已确认送样的需求，请勿重复确认');\n        return;\n      }\n\n      // 检查是否有已拒绝的明细行 (SampleEnums.STAT11 = 11 已拒绝)\n      var rejectedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.caseStat == 11;\n      });\n      if (rejectedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已拒绝的需求，无法确认送样');\n        return;\n      }\n      this.$refs['dataForm'].validate(function (valid) {\n        if (valid) {\n          _this7.btnLoading = true;\n          var isLineValid = _this7.lineCheck();\n          if (!isLineValid) {\n            _this7.btnLoading = false;\n            return;\n          }\n          var formMethod = _this7.isAdd ? _sample.updateSample : _sample.createSample;\n          formMethod(_this7.dataForm).then(function (res) {\n            if (res.data) {\n              _this7.dataForm.id = res.data;\n\n              // 构建批量确认送样的参数\n              var ids = _this7.selectedMaterialItems.map(function (item) {\n                return item.id;\n              });\n              console.log(\"批量确认送样 =======> \", ids);\n              (0, _sample.confirmSample)(ids).then(function (res) {\n                _this7.$message({\n                  message: \"\\u6210\\u529F\\u786E\\u8BA4\".concat(ids.length, \"\\u4E2A\\u7269\\u6599\\u660E\\u7EC6\\u9001\\u6837\"),\n                  type: 'success',\n                  duration: 1500,\n                  onClose: function onClose() {\n                    _this7.btnLoading = false;\n                    _this7.selectedMaterialItems = []; // 重置选中项\n                    if (res.data) {\n                      _this7.loading = true;\n                      (0, _sample.getSampleInfo)(_this7.dataForm.id).then(function (res) {\n                        _this7.dataForm = res.data;\n                        _this7.loading = false;\n                      });\n                    }\n                  }\n                });\n              }).catch(function (err) {\n                _this7.btnLoading = false;\n              });\n            } else {\n              _this7.btnLoading = false;\n            }\n          }).catch(function (err) {\n            _this7.btnLoading = false;\n          });\n        }\n      });\n    },\n    //上传文件\n    uploadFlie: function uploadFlie(rowData, index) {\n      this.index = index;\n      this.$refs.fileUpload.init(\"base_sample_item\", index, 0);\n    },\n    //获取上传文件后返回的文件路径\n    upload: function upload(res) {\n      // this.dataForm.sampleItemEntityList[this.index].vendorFileName = res.data.name;\n      // this.dataForm.sampleItemEntityList[this.index].vendorFilePath = res.data.src;\n      this.dataForm.sampleItemEntityList[this.index].quaFileName = res.data.name;\n      this.dataForm.sampleItemEntityList[this.index].quaFilePath = res.data.src;\n    },\n    //查看文件\n    lookFile: function lookFile(url) {\n      window.open(url);\n    },\n    //跳转图纸网址\n    handleDrawing: function handleDrawing(val) {\n      var params = {\n        goodsErpCode: val\n      };\n      (0, _goods.handleGoodsDrawing)(params).then(function (res) {\n        window.open(res.data);\n      }).catch(function (res) {});\n    },\n    //确认需要送样\n    acceptSample: function acceptSample() {\n      var _this8 = this;\n      this.btnLoading = true;\n      (0, _sample.acceptSample)(this.dataForm).then(function (res) {\n        _this8.$message({\n          message: \"确认成功\",\n          type: 'success',\n          duration: 1500,\n          onClose: function onClose() {\n            _this8.btnLoading = false;\n            if (res.data) {\n              _this8.loading = true;\n              (0, _sample.getSampleInfo)(res.data).then(function (res) {\n                _this8.dataForm = res.data;\n                _this8.loading = false;\n              });\n            }\n          }\n        });\n      }).catch(function (res) {\n        _this8.btnLoading = false;\n      });\n    },\n    //拒绝该送样通知单\n    rejectSample: function rejectSample() {\n      var _this9 = this;\n      if (this.selectedMaterialItems.length === 0) {\n        this.$message.error(\"请选择要拒绝的物料明细行需求\");\n        return;\n      }\n\n      // 检查是否有已拒绝或已答交的明细行\n      var rejectedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.caseState == 11;\n      });\n      if (rejectedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已拒绝的需求，请勿重复拒绝');\n        return;\n      }\n      var repliedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.replyState == 2;\n      });\n      if (repliedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已答交的需求，不允许拒绝');\n        return;\n      }\n      this.$confirm('确认拒绝选中的物料明细行需求?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this9.btnLoading = true;\n\n        // 构建批量拒绝的参数\n        var ids = _this9.selectedMaterialItems.map(function (item) {\n          return item.id;\n        });\n        console.log(\"批量拒绝送样 =======> \", ids);\n        (0, _sample.rejectSample)(ids).then(function (res) {\n          _this9.$message({\n            message: \"\\u6210\\u529F\\u62D2\\u7EDD\".concat(ids.length, \"\\u4E2A\\u7269\\u6599\\u660E\\u7EC6\"),\n            type: 'success',\n            duration: 1500,\n            onClose: function onClose() {\n              _this9.btnLoading = false;\n              _this9.selectedMaterialItems = []; // 重置选中项\n              if (res.data) {\n                _this9.loading = true;\n                (0, _sample.getSampleInfo)(_this9.dataForm.id).then(function (res) {\n                  _this9.dataForm = res.data;\n                  _this9.loading = false;\n                });\n              }\n            }\n          });\n        }).catch(function (res) {\n          _this9.btnLoading = false;\n        });\n      }).catch(function () {\n        _this9.$message({\n          type: 'info',\n          message: '已取消拒绝'\n        });\n      });\n    },\n    // 答交送样\n    replySample: function replySample() {\n      var _this10 = this;\n      if (this.selectedMaterialItems.length === 0) {\n        this.$message.error(\"请选择要答交的物料明细行需求\");\n        return;\n      }\n\n      // 检查是否上传了资质文件\n      if (this.dataForm.isNeedUpFile === 1) {\n        var noFileItems = this.selectedMaterialItems.filter(function (item) {\n          return !item.quaFilePath;\n        });\n        if (noFileItems.length > 0) {\n          this.$message.error('选中的物料明细中有未上传资质文件的记录，请先上传资质文件');\n          return;\n        }\n      }\n\n      // 检查是否有已拒绝或已答交的明细行\n      var rejectedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.caseState == 11;\n      });\n      if (rejectedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已拒绝的需求，不允许答交');\n        return;\n      }\n\n      // 检查回复数量是否为空或小于等于0\n      var emptyReplyItems = this.selectedMaterialItems.filter(function (item) {\n        return !item.replyQuantity || item.replyQuantity <= 0;\n      });\n      if (emptyReplyItems.length > 0) {\n        this.$message.error('选中的物料明细中有回复数量为空或小于等于0的记录，请先填写回复数量');\n        return;\n      }\n\n      // 检查回复交期是否为空\n      var emptyReplyDateItems = this.selectedMaterialItems.filter(function (item) {\n        return !item.replyDeliveryDate;\n      });\n      if (emptyReplyDateItems.length > 0) {\n        this.$message.error('选中的物料明细中有回复交期为空的记录，请先填写回复交期');\n        return;\n      }\n      var repliedItems = this.selectedMaterialItems.filter(function (item) {\n        return item.replyState == 2;\n      });\n      if (repliedItems.length > 0) {\n        this.$message.error('选中的物料明细中有已答交的需求，请勿重复答交');\n        return;\n      }\n      this.$refs['dataForm'].validate(function (valid) {\n        if (valid) {\n          _this10.btnLoading = true;\n          var isLineValid = _this10.lineCheck();\n          if (!isLineValid) {\n            _this10.btnLoading = false;\n            return;\n          }\n          var formMethod = _this10.isAdd ? _sample.updateSample : _sample.createSample;\n          formMethod(_this10.dataForm).then(function (res) {\n            if (res.data) {\n              _this10.dataForm.id = res.data;\n\n              // 构建批量答交的参数\n              var ids = _this10.selectedMaterialItems.map(function (item) {\n                return item.id;\n              });\n              console.log(\"批量答交送样 =======> \", ids);\n              (0, _sample.replySample)(ids).then(function (res) {\n                _this10.$message({\n                  message: \"\\u6210\\u529F\\u7B54\\u4EA4\".concat(ids.length, \"\\u4E2A\\u7269\\u6599\\u660E\\u7EC6\"),\n                  type: 'success',\n                  duration: 1500,\n                  onClose: function onClose() {\n                    _this10.btnLoading = false;\n                    _this10.selectedMaterialItems = []; // 重置选中项\n                    if (res.data) {\n                      _this10.loading = true;\n                      (0, _sample.getSampleInfo)(_this10.dataForm.id).then(function (res) {\n                        _this10.dataForm = res.data;\n                        _this10.loading = false;\n                      });\n                    }\n                  }\n                });\n              }).catch(function (res) {\n                _this10.btnLoading = false;\n              });\n            } else {\n              _this10.btnLoading = false;\n            }\n          }).catch(function (err) {\n            _this10.btnLoading = false;\n          });\n        }\n      });\n    },\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedMaterialItems = selection;\n    },\n    handleReplyQuantityChange: function handleReplyQuantityChange(val, row) {\n      if (val !== null && val !== undefined) {\n        row.goodsNum = val;\n      }\n    }\n  }\n};\nexports.default = _default;", null]}