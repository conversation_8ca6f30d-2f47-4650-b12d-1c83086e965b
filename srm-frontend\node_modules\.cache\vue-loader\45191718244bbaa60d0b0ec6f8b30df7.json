{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue", "mtime": 1754042535665}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport store from \"@/store\";\r\nimport Form from './form'\r\nimport { getDeliveryList, delDelivery, specifyCloseDelivery } from \"@/api/dm/delivery\";\r\n\r\nexport default {\r\n  name: \"dm-delivery-vendor\",\r\n  components: {\r\n    Form\r\n  },\r\n  data() {\r\n    return {\r\n      SortModeOptions: store.getters.commonEnums['dm.DeliveryBoardSortModeEnum'], // 订单类型\r\n      deStatOptions: store.getters.commonEnums['dm.DeStatEnum'], // 单据状态\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        vendorIds:'vendorIds',//用于区分是否为供应商\r\n        whereType:1,//气泡查询条件 默认为1 - 全部\r\n        vendor : '', // 供应商编码|名称\r\n        searchStr : '',  // 物料编码|名称|描述|图号\r\n        deliveryDate:'',//创建送货日期\r\n        startDate : '',  // 开始日期\r\n        orderType : '', // 订单类型\r\n        deNo : '', // 送货单号\r\n        orderNo : '', // 订单号\r\n        dept : '', // 机构组织\r\n        goods : '', // 物料信息\r\n        sortObjTwo:'1',//排序方式\r\n        deStat:'',//单据状态\r\n        deptId:'',\r\n      },\r\n      deliveryDate: [],\r\n      deliveryCount:{},//气泡数\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      formVisible:false,\r\n      list: [],//列表数据\r\n      total: 0,//条数\r\n      selectedDatas: [],/*选择的数据*/\r\n      selectedNum: 0,/*选择数据的条数*/\r\n      userInfo:store.getters.userInfo,/*获取当前用户*/\r\n      showAll: false,/*收*/\r\n      // 气泡主题\r\n      buttonFrom: {\r\n        count1: 'primary',//全部\r\n        count2: '',//已送未收\r\n        count3: '',//待发出\r\n        count4: '',//暂收\r\n        count5: '',//暂退\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.listLoading = true;\r\n      if (this.deliveryDate.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.deliveryDate[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.deliveryDate[1], 'YYYY-MM-DD');\r\n        this.queryParam.deliveryDate = startDate +\" 至 \"+endDate;\r\n      }\r\n      getDeliveryList(this.queryParam).then(res => {\r\n        this.total = res.data.totalCount;\r\n        this.list = res.data.list;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.initData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.deliveryDate = []\r\n      this.search();\r\n    },\r\n    //打开送货单详情弹窗\r\n    openInfoForm(id){\r\n      this.formVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.init(id);\r\n      })\r\n    },\r\n    //关闭刷新列表数据\r\n    callDeliveryBoardList(){\r\n      this.formVisible = false;\r\n      this.search();\r\n    },\r\n    //点击气泡查询\r\n    changeCountsButton(stat, name){\r\n      // 动态变换\r\n      this.buttonFrom.count1 = '';//全部\r\n      this.buttonFrom.count2 = '';//已送未收\r\n      this.buttonFrom.count3 = '';//待发出\r\n      this.buttonFrom.count4 = '';//暂收\r\n      this.buttonFrom.count5 = '';//暂退\r\n      this.buttonFrom[name] = 'primary';\r\n\r\n      this.queryParam.whereType = stat;\r\n      this.search();\r\n    },\r\n    delDelivery(){\r\n      if(this.selectedNum == 0){\r\n        this.$message.error('请最少选择1条需要删除的送货单');\r\n        return;\r\n      }\r\n      if(this.selectedNum > 1){\r\n        this.$message.error('只能选择删除单个相同的送货单号数据');\r\n        return;\r\n      }\r\n      const deptCodes = this.selectedDatas.map(value => value.deptCode)\r\n      //去除重复的采购组织数据\r\n      const deptCodeSet = new Set(deptCodes);\r\n      //去重后的采购组织数据\r\n      let deptCodeList = [...deptCodeSet];\r\n      if (deptCodeList.length > 1){\r\n        this.$message.error('选择删除的数据中存在不同的采购组织');\r\n        return;\r\n      }\r\n      //从已选择数据根据采购方（客户）获取每一个租户id\r\n      const delIds = this.selectedDatas.map(value => value.id);\r\n      //去除重复的采购方id数据\r\n      const delIdsSet = new Set(delIds);\r\n      //去重后的采购方id数据\r\n      let delIdList = [...delIdsSet];\r\n      this.$confirm('是否确认删除选择的送货单？', '提示', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true;\r\n        this.listLoading = true;\r\n        delDelivery(delIdList).then(res => {\r\n          this.$message({\r\n            message: \"删除成功\",\r\n            type: 'success',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.search();\r\n            }\r\n          })\r\n          this.btnLoading = false;\r\n          this.listLoading = false;\r\n        }).catch(() => {\r\n          this.btnLoading = false;\r\n          this.listLoading = false;\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}