{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue", "mtime": 1754289857268}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n\r\nimport {dFlowMixin} from \"@dian/dian-ui-vue\";\r\nimport Form from './Form';\r\nimport store from \"@/store\";\r\nimport {getVendorSaleOrderList,batchConfirmOrder} from \"@/api/order/sale\";\r\nimport {getSubCompanyId} from \"@/utils/auth\";\r\nimport dian from \"@/utils/dian\";\r\nimport deliveryForm from '@/views/dm/deliveryBoard/vendor/form.vue';\r\nimport CodePrintTemplate from './codePrintTemplate'\r\n\r\nexport default {\r\n  //加载底层公有组件\r\n  mixins: [dFlowMixin],\r\n  name: \"order-produce-vendor\",\r\n  components: {\r\n    Form,\r\n    deliveryForm,\r\n    CodePrintTemplate,\r\n  },\r\n  filters: {\r\n    dateFormat(time) {\r\n      if (!time) {\r\n        return ''\r\n      }\r\n      let date = new Date(time)\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth() + 1;\r\n      let day = date.getDate();\r\n      return year + \"-\" + month + \"-\" + day;\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler (to, from) {\r\n        if (to.path === '/order/produce/vendor' && null != to.query.id) {\r\n          this.addEditOrderHandle(to.query.id);\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型\r\n      // orderStatOptions: store.getters.commonEnums['order.PurStatEnum'],\r\n      orderStatOptions:[\r\n        {key:1,value:'待确认'},\r\n        {key:4,value:'已确认'},\r\n      ],\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        keyword : '', // 订单号/客户名称\r\n        orderDate : '',  // 订单日期\r\n        dept : '',  // 订单日期\r\n        bsart : '', // 采购类型\r\n      },\r\n      showAll: false,\r\n      formVisible: false,\r\n      deliveryFormVisible: false,\r\n      templateVisible: false, // 是否显示模板弹窗\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      subCompanyInfo:getSubCompanyId()||{},//头部选择的采购组织信息\r\n      list: [],\r\n      total: 0,\r\n      selectedDatas: [],\r\n      selectedNum: 0\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.listLoading = true;\r\n      let queryParams = { ...this.queryParam };\r\n      if (this.queryParam.orderDate && this.queryParam.orderDate.length === 2) {\r\n        try {\r\n          const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');\r\n          const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');\r\n          queryParams.orderDate = startDate + \" 至 \" + endDate;\r\n        } catch (error) {\r\n          console.error('日期格式化错误:', error);\r\n          queryParams.orderDate = '';\r\n        }\r\n      }\r\n      let subCompanyInfoData = dian.storageGet('subCompanyInfo');\r\n      if (subCompanyInfoData){\r\n        queryParams.deptId = subCompanyInfoData.id;\r\n      }\r\n      getVendorSaleOrderList(queryParams).then(res => {\r\n        this.total = res.page.totalCount;\r\n        this.list = res.page.list;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    // 新增|编辑 项目报备\r\n    addEditOrderHandle(id) {\r\n      this.formVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.init(id);\r\n      })\r\n    },\r\n    // 批量确认\r\n    oneClickBatchConfirm(){\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message({\r\n          message: '请至少选择一条记录',\r\n          type: 'warning',\r\n          duration: 1500\r\n        });\r\n        return;\r\n      }\r\n      // 检查是否有已确认的订单\r\n      const confirmedOrders = this.selectedDatas.filter(item => item.stat === 5);\r\n      if (confirmedOrders.length > 0) {\r\n        const confirmedOrderNos = confirmedOrders.map(item => item.purNo).join('、');\r\n        this.$message({\r\n          message: `选中的订单中存在已确认的订单：${confirmedOrderNos}，请重新选择`,\r\n          type: 'warning',\r\n          duration: 3000\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm('是否批量确认所选订单？确认后将无法撤销', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true;\r\n        const ids = this.selectedDatas.map(item => item.id);\r\n        batchConfirmOrder(ids).then(res => {\r\n          this.$message({\r\n            message: '批量确认成功',\r\n            type: 'success',\r\n            duration: 1500\r\n          });\r\n          this.btnLoading = false;\r\n          this.initData();\r\n        }).catch(() => {\r\n          this.btnLoading = false;\r\n        })\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    //打开创建送货单弹窗\r\n    openCreateFrom(){\r\n      //校验是否有选择数据\r\n      if (this.selectedNum === 0) {\r\n        this.deliveryFormVisible = true;\r\n        this.$nextTick(() => {\r\n          this.$refs.deliveryForm.init(null);\r\n        })\r\n      } else {\r\n        //为表格多选选中的数组\r\n        let list = this.selectedDatas;\r\n        //校验需要创建送货单的数据\r\n        this.judgeSelectCreateData(list);\r\n        let tenantInfo = this.getCreateTenantData(list)\r\n        let vendorInfo = this.getCreateVendorData(list);\r\n        let seq = 0;\r\n        list.forEach((item,index) => {\r\n          seq = index+1;\r\n          item.id = null;\r\n          item.seq = seq;\r\n          item.tempRowId = `${new Date().getTime()}_${Math.random().toString(36).slice(2, 10)}`;\r\n        })\r\n        list.sort((a,b)=>{\r\n          if(a['goodsErpCode']!==b['goodsErpCode']){\r\n            return a['goodsErpCode'].localeCompare(b['goodsErpCode']);\r\n          }\r\n        })\r\n        let createData = {\r\n          tenantId:tenantInfo.tenantId,//采购方id\r\n          tenantName:tenantInfo.tenantName,//采购方名称\r\n          vendorId:vendorInfo.vednorId,//供应商id\r\n          vendorCode:vendorInfo.vendorCode,//供应商编码\r\n          vendorName:vendorInfo.vendorName,//供应商名称\r\n          deptId:vendorInfo.deptId,//机构id\r\n          deptCode:vendorInfo.deptCode,//机构编码\r\n          deptName:vendorInfo.deptName,//机构名称\r\n          address:vendorInfo.address,//送货地址\r\n          shippingAddress:vendorInfo.shippingAddress,//送货地址\r\n          deliveryType:vendorInfo.deliveryType,//送货类型\r\n          is:vendorInfo.is,\r\n          documentDate:new Date(),//创建送货单日期（默认为当前时间）不可修改\r\n          deliveryDate:new Date(),//实际送货时间（默认当前时间）可修改\r\n          isEle:0,//是否物流\r\n          logisticsName:'',//物流公司\r\n          eleNo:'',//物流单号\r\n          purchaserId:vendorInfo.purchaserId,//采购员id\r\n          deliveryItemEntityList:list,//送货单创建明细数据\r\n        }\r\n        this.deliveryFormVisible = true;\r\n        this.$nextTick(() => {\r\n          this.$refs.deliveryForm.init(createData);\r\n        })\r\n      }\r\n    },\r\n    //校验需要创建送货单的数据\r\n    judgeSelectCreateData(list){\r\n      //从已选择数据根据采购方（客户）获取每一个租户id\r\n      const tenantIds = list.map(value => value.tenantId);\r\n      //去除重复的采购方id数据\r\n      const tenantIdsSet = new Set(tenantIds);\r\n      //去重后的采购方id数据\r\n      let tenantIdList = [...tenantIdsSet];\r\n      //若去重后的采购方id数据长度大于1时\r\n      if(tenantIdList.length > 1){\r\n        this.$message.error('只能选择同一个采购方（客户）的单据创建送货单');\r\n        throw new Error()\r\n      }\r\n\r\n      //从已选择数据根据采购方（客户）获取每一个采购组织机构id\r\n      const deptIds = list.map(value => value.deptId);\r\n      //去除重复的采购组织id数据\r\n      const deptIdsSet = new Set(deptIds);\r\n      //去重后的采购方id数据\r\n      let deptIdList = [...deptIdsSet];\r\n      //若去重后的采购方id数据长度大于1时\r\n      if(deptIdList.length > 1){\r\n        this.$message.error('只能选择同一个采购组织（客户）的单据创建送货单');\r\n        throw new Error()\r\n      }\r\n\r\n      //从已选择数据根据地址\r\n      const address = list.map(value => value.shippingAddress);\r\n      //去除重复的地址数据\r\n      const addressSet = new Set(address);\r\n      //去重后的地址数据\r\n      let addressList = [...addressSet];\r\n      // if(addressList.length > 1){\r\n      //   this.$message.error('存在不同交货地址的单据不允许创建在同一张送货单');\r\n      //   throw new Error()\r\n      // }\r\n\r\n      if (tenantIdList[0] === 24739){\r\n        //从已选择数据根据送货单过滤获取每一条的订单类型\r\n        const orderType = list.map(value => value.orderType);\r\n        //去除重复的订单类型数据\r\n        const orderTypeSet = new Set(orderType);\r\n        //去重后的订单类型数据\r\n        let orderTypeList = [...orderTypeSet];\r\n        if(orderTypeList.length > 1){\r\n          this.$message.error('存在不同订单类型的单据不允许创建在同一张送货单');\r\n          throw new Error()\r\n        }\r\n\r\n        //从已选择数据根据送货单过滤获取每一条的订单业务类型\r\n        const orderBusType = list.map(value => value.orderBusType);\r\n        //去除重复的订单业务类型数据\r\n        const orderBusTypeSet = new Set(orderBusType);\r\n        //去重后的订单业务类型数据\r\n        let orderBusTypeList = [...orderBusTypeSet];\r\n        if(orderBusTypeList.length > 1){\r\n          this.$message.error('存在不同订单业务类型的单据不允许创建在同一张送货单');\r\n          throw new Error()\r\n        }\r\n      }\r\n\r\n      //从已选择数据根据送货单过滤获取每一条的送货类型\r\n      const deliveryType = list.map(value => value.deliveryType);\r\n      //去除重复的送货类型数据\r\n      const deliveryTypeSet = new Set(deliveryType);\r\n      //去重后的送货类型数据\r\n      let deliveryTypeList = [...deliveryTypeSet];\r\n      if(deliveryTypeList.length > 1){\r\n        this.$message.error('存在不同送货方式的单据不允许创建在同一张送货单');\r\n        throw new Error()\r\n      }\r\n\r\n      //从已选择数据根据送货单过滤获取每一条的仓库\r\n      const warehouseCode = list.map(value => value.warehouseCode);\r\n      //去除重复的送货类型数据\r\n      const warehouseCodeSet = new Set(warehouseCode);\r\n      //去重后的送货类型数据\r\n      let warehouseCodeList = [...warehouseCodeSet];\r\n      if(warehouseCodeList.length > 1){\r\n        this.$message.error('当前选择的数据中存在不同仓库地址，不允许创建在同一张送货单');\r\n        throw new Error()\r\n      }\r\n\r\n      //从已选择数据根据采购员过滤获取每一条的送货类型\r\n      const purId = list.map(value => value.purId);\r\n      //去除重复的采购员数据\r\n      const purIdSet = new Set(purId);\r\n      //去重后的采购员数据\r\n      let purIdList = [...purIdSet];\r\n      // if(purIdList.length > 1){\r\n      //   this.$message.error('存在两个或两个以上不同采购员的单据不允许创建在同一张送货单');\r\n      //   throw new Error()\r\n      // }\r\n\r\n      let idx = 0;\r\n      list.forEach((item,index) => {\r\n        idx=index+1;\r\n        // 当前日期小于最早可提前送货日期时\r\n        let nowDate = new Date();\r\n        let inAdvanceDeliveryDate = new Date(item.inAdvanceDeliveryDate);\r\n        if(nowDate < inAdvanceDeliveryDate){\r\n          this.$message.error('目前已选择的数据中存在当前日期小于最早可提前送货日期'+item.inAdvanceDeliveryDate+'，无法创建送货单');\r\n          throw new Error()\r\n        }\r\n        //已选择数据中的可制单数量小于0/等于0\r\n        if(parseFloat(item.canMakeNum) <= 0){\r\n          this.$message.error('第'+idx+'行物料['+item+']的可制单数量小于等于0，无法创建送货单，请刷新页面');\r\n          throw new Error()\r\n        }\r\n      })\r\n    },\r\n    //获取采购方（客户）基本信息\r\n    getCreateTenantData(list){\r\n      let tenantInfoData = {}\r\n      //从已选择数据根据采购方（客户）获取每一个租户id\r\n      const tenantIds = list.map(value => value.tenantId);\r\n      //去除重复的采购方id数据\r\n      const tenantIdsSet = new Set(tenantIds);\r\n      //去重后的采购方id数据\r\n      let tenantIdList = [...tenantIdsSet];\r\n      tenantInfoData.tenantId = tenantIdList[0];\r\n\r\n      //从已选择数据根据采购方（客户）获取每一个租户名称\r\n      const tenantNames = list.map(value => value.tenantName);\r\n      //去除重复的采购方名称数据\r\n      const tenantNamesSet = new Set(tenantNames);\r\n      //去重后的采购方名称数据\r\n      let tenantNameList = [...tenantNamesSet];\r\n      tenantInfoData.tenantName = tenantNameList[0];\r\n      return tenantInfoData;\r\n    },\r\n    //获取当前供应商基本信息\r\n    getCreateVendorData(list){\r\n      let vendorData = {};\r\n      //从已选择数据根据供应商id获取每一个供应商id\r\n      const vednorIds = list.map(value => value.vendorId);\r\n      //去除重复的供应商id数据\r\n      const vednorIdsSet = new Set(vednorIds);\r\n      //去重后的供应商id数据\r\n      let vednorIdList = [...vednorIdsSet];\r\n      vendorData.vednorId = vednorIdList[0];\r\n\r\n      //从已选择数据根据机构id获取每一个机构id\r\n      const deptIds = list.map(value => value.deptId);\r\n      //去除重复的机构id数据\r\n      const deptIdsSet = new Set(deptIds);\r\n      //去重后的机构id数据\r\n      let deptIdList = [...deptIds];\r\n      vendorData.deptId = deptIdList[0];\r\n\r\n      //从已选择数据根据机构编码获取每一个机构编码\r\n      const deptCodes = list.map(value => value.deptCode);\r\n      //去除重复的机构编码数据\r\n      const deptCodesSet = new Set(deptCodes);\r\n      //去重后的机构编码数据\r\n      let deptCodeList = [...deptCodesSet];\r\n      vendorData.deptCode = deptCodeList[0];\r\n\r\n      //从已选择数据根据机构名称获取每一个机构名称\r\n      const deptNames = list.map(value => value.deptName);\r\n      //去除重复的机构名称数据\r\n      const deptNamesSet = new Set(deptNames);\r\n      //去重后的机构名称数据\r\n      let deptNameList = [...deptNamesSet];\r\n      vendorData.deptName = deptNameList[0];\r\n\r\n      //从已选择数据根据地址过滤获取每一个地址\r\n      const address = list.map(value => value.vendorDeliveryAddress);\r\n      //去除重复的地址数据\r\n      const addressSet = new Set(address);\r\n      //去重后的地址数据\r\n      let addressList = [...addressSet];\r\n      vendorData.address = addressList[0];\r\n\r\n      //从已选择数据根据地址过滤获取每一个地址\r\n      const shippingAddress = list.map(value => value.shippingAddress);\r\n      //去除重复的地址数据\r\n      const shippingAddressSet = new Set(shippingAddress);\r\n      //去重后的地址数据\r\n      let shippingAddressList = [...shippingAddressSet];\r\n      vendorData.shippingAddress = shippingAddressList[0];\r\n\r\n      //从已选择数据根据送货单过滤获取每一条的送货类型\r\n      const deliveryType = list.map(value => value.deliveryType);\r\n      //去除重复的送货类型数据\r\n      const deliveryTypeSet = new Set(deliveryType);\r\n      //去重后的送货类型数据\r\n      let deliveryTypeList = [...deliveryTypeSet];\r\n      vendorData.deliveryType = deliveryTypeList[0];\r\n      if(vendorData.deliveryType){\r\n        vendorData.deliveryType = deliveryTypeList[0]\r\n        if(vendorData.deliveryType == 2){\r\n          vendorData.is = 1\r\n        } else {\r\n          vendorData.is = 0\r\n        }\r\n      } else {\r\n        vendorData.deliveryType = 1\r\n        vendorData.is = 0\r\n      }\r\n\r\n      //从已选择数据根据采购员过滤获取每一条的送货类型\r\n      const purId = list.map(value => value.purId);\r\n      //去除重复的采购员数据\r\n      const purIdSet = new Set(purId);\r\n      //去重后的采购员数据\r\n      let purIdList = [...purIdSet];\r\n      vendorData.purchaserId = purIdList[0];\r\n\r\n      return vendorData;\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.queryParam.page = 1;\r\n      this.initData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.search();\r\n    },\r\n    // Form表单关闭时回调方法\r\n    closeForm() {\r\n      this.formVisible = false;\r\n      this.deliveryFormVisible = false;\r\n      this.initData();\r\n    },\r\n    // 打印条码\r\n    printBarCode(data) {\r\n      this.templateVisible = true\r\n      const list = this.dataForm.purItemEntityList.filter(\r\n        el => el.goodsErpCode === data.goodsErpCode && el.isClose !== 1\r\n      )\r\n\r\n      let devNum = 0\r\n      list.forEach(el => {\r\n        devNum += Number(el.orderNum) || 0 // 非法值转为0\r\n      })\r\n\r\n      // 解构赋值简化代码\r\n      const {\r\n        tenantId,\r\n        vendorId,\r\n        vendorCode,\r\n        vendorName,\r\n        id: sourceId,\r\n        orderNo: sourceNo\r\n      } = this.dataForm\r\n\r\n      const {\r\n        goodsId,\r\n        goodsCode,\r\n        goodsErpCode,\r\n        goodsName,\r\n        goodsModel,\r\n        bigPackStandardNum,\r\n        smallPackStandardNum,\r\n        bigPackLabelNum,\r\n        smallPackLabelNum,\r\n        bigPackMantissa,\r\n        smallPackMantissa\r\n      } = data\r\n\r\n      const printData = {\r\n        tenantId,\r\n        tenantPId: 0,\r\n        vendorId,\r\n        vendorCode,\r\n        vendorName,\r\n        sourceId,\r\n        sourceNo,\r\n        goodsId,\r\n        goodsCode,\r\n        goodsErpCode,\r\n        goodsName,\r\n        goodsModel,\r\n        sourceType: 1,\r\n        devNum,\r\n        bigPackStandardNum,\r\n        smallPackStandardNum,\r\n        bigPackLabelNum,\r\n        smallPackLabelNum,\r\n        bigPackMantissa,\r\n        smallPackMantissa\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.PrintTemplate.isBatchPrint = false;\r\n        this.$refs.PrintTemplate.initData(printData);\r\n        this.$refs.PrintTemplate.init(\"goodsBarCodePrinting\")\r\n      })\r\n    },\r\n  }\r\n}\r\n", null]}