{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue?vue&type=template&id=6e8dc346&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue", "mtime": 1754292527998}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n<el-dialog  :title=\"title\"\n            :close-on-click-modal=\"false\" :visible.sync=\"visible\" class=\"DIAN-dialog DIAN-dialog_center\"\n            lock-scroll  width=\"55%\" top=\"2vh\" :modal-append-to-body=\"false\">\n  <div class=\"button-group-container\">\n    <el-button-group>\n      <el-button type=\"primary\" @click=\"generateBarcode\">生成条码</el-button>\n      <el-button type=\"warning\" @click=\"print(1)\">打印大包条码</el-button>\n      <el-button type=\"warning\" @click=\"print(2)\">打印小包条码</el-button>\n    </el-button-group>\n  </div>\n  <el-form class=\"el-form\" label-width=\"110px\" ref=\"deLineData\" :rules=\"dataRule\" :model=\"deLineData\">\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item label=\"物料编码:\" prop=\"goodsCode\">\n          <el-input v-model=\"deLineData.goodsCode\" readonly/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"物料名称:\" prop=\"goodsName\">\n          <el-input v-model=\"deLineData.goodsName\" readonly/>\n        </el-form-item>\n      </el-col>\n    </el-row>\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item label=\"生产日期:\" prop=\"productDate\">\n          <el-date-picker\n            v-model=\"deLineData.productDate\"\n            type=\"date\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n          >\n          </el-date-picker>\n        </el-form-item>\n      </el-col>\n    </el-row>\n    <el-row>\n      <el-col :span=\"24\">\n        <el-form-item label=\"条码模板:\" prop=\"templateId\">\n          <el-radio @change=\"getTemplate(item)\" v-model=\"templateId\"  v-for=\"(item, index) in templateList\" :key=\"index\" :label=\"item.id\" border>\n            {{item.templateName}}\n          </el-radio>\n        </el-form-item>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"24\">\n      <el-col :span=\"6\">\n        <el-form-item label=\"条码大包数量:\" prop=\"bigPackStandardNum\">\n          <el-input v-model=\"deLineData.bigPackStandardNum\"/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"条码小包数量:\" prop=\"smallPackStandardNum\">\n          <el-input v-model.number=\"deLineData.smallPackStandardNum\"/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"大包尾数:\" prop=\"bigPackMantissa\">\n          <el-input v-model.number=\"deLineData.bigPackMantissa\" disabled readonly/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"序号:\" prop=\"seq\">\n          <el-input v-model=\"deLineData.seq\" disabled/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"大包条码张数:\" prop=\"bigPackLabelNum\">\n          <el-input v-model.number=\"deLineData.bigPackLabelNum\" disabled readonly/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"小包条码张数:\" prop=\"smallPackLabelNum\">\n          <el-input v-model.number=\"deLineData.smallPackLabelNum\" disabled readonly/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"小包尾数:\" prop=\"smallPackMantissa\">\n          <el-input v-model.number=\"deLineData.smallPackMantissa\" disabled readonly/>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-form-item label=\"送货数量:\" prop=\"devNum\">\n          <el-input v-model.number=\"deLineData.devNum\"/>\n        </el-form-item>\n      </el-col>\n    </el-row>\n  </el-form>\n  <!--    <span slot=\"footer\" class=\"dialog-footer\">-->\n  <!--      <el-button @click=\"visible = false\">取消</el-button>-->\n  <!--      <el-button type=\"primary\" :loading=\"btnLoading\" @click.stop=\"print\">-->\n  <!--        打印</el-button>-->\n  <!--    </span>-->\n</el-dialog>\n", null]}