{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\index.vue?vue&type=template&id=27aa0e6c&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\index.vue", "mtime": 1754281238291}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n<div class=\"DIAN-common-layout DIAN-flex-main\">\n  <div class=\"DIAN-common-layout-center\">\n    <el-row class=\"DIAN-common-search-box\" :gutter=\"16\">\n      <el-form @submit.native.prevent>\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.sourceNo\" placeholder=\"请输入PLM打样单号\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <!-- v-model.trim 可以去除前后空格 -->\n          <el-input v-model.trim=\"queryParam.sampleNo\" placeholder=\"请输入送样单号\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.tenantName\" placeholder=\"请输入客户名称\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.goods\" placeholder=\"请输入物料编码/名称/型号\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.pur\" placeholder=\"请输入采购员名称\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-form-item>\n            <el-select v-model=\"queryParam.sampleStat\" placeholder=\"单据状态\" clearable>\n              <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                         v-for=\"item in sampleStatOptions\"/>\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <!-- <el-col :span=\"3\">\n          <el-form-item>\n            <el-select v-model=\"queryParam.itemStat\" placeholder=\"检验状态\" clearable>\n              <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                         v-for=\"item in sampleItemStatOptions\"/>\n            </el-select>\n          </el-form-item>\n        </el-col> -->\n        <template v-if=\"showAll\">\n          <el-col :span=\"3\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.dept\" placeholder=\"采购组织编码/名称\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"3\">\n            <el-form-item>\n              <el-select v-model=\"queryParam.demandClassType\" placeholder=\"请选择单据类型\" clearable>\n                <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                          v-for=\"item in demandTypeOption\"/>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-date-picker\n                v-model=\"sampleDates\"\n                type=\"daterange\"\n                placeholder=\"请选择要求送样日期\"\n                range-separator=\"至\"\n                start-placeholder=\"（送样）开始日期\"\n                end-placeholder=\"（送样）结束日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-date-picker\n                v-model=\"demandDates\"\n                type=\"daterange\"\n                placeholder=\"请选择需求日期\"\n                range-separator=\"至\"\n                start-placeholder=\"（需求）开始日期\"\n                end-placeholder=\"（需求）结束日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-date-picker\n                v-model=\"replyDates\"\n                type=\"daterange\"\n                placeholder=\"请选择回复日期\"\n                range-separator=\"至\"\n                start-placeholder=\"（回复）开始日期\"\n                end-placeholder=\"（回复）结束日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </template>\n        <el-col :span=\"6\">\n          <el-form-item>\n            <!-- 查询按钮 -->\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">\n              {{ $t('common.search') }}\n            </el-button>\n            <!-- 重置按钮 -->\n            <el-button icon=\"el-icon-refresh-right\" @click=\"reset()\">\n              {{ $t('common.reset') }}\n            </el-button>\n            <el-button type=\"text\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\"\n                       v-if=\"!showAll\">展开\n            </el-button>\n            <el-button type=\"text\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>\n              收起\n            </el-button>\n          </el-form-item>\n        </el-col>\n      </el-form>\n    </el-row>\n    <div class=\"DIAN-common-layout-main DIAN-flex-main\">\n      <div class=\"DIAN-common-head\">\n        <div>\n\n        </div>\n        <div class=\"DIAN-common-head-right\">\n          <el-tooltip effect=\"dark\" :content=\"$t('common.refresh')\" placement=\"top\">\n            <el-link icon=\"icon-ym icon-ym-Refresh DIAN-common-head-icon\" :underline=\"false\"\n                     @click=\"search()\"/>\n          </el-tooltip>\n          <d-screen-full/>\n        </div>\n      </div>\n      <d-table v-loading=\"listLoading\" :data=\"list\" :hasNO=\"false\" :hasC=\"true\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"index\" width=\"50\" label=\"序号\" align=\"center\" />\n        <el-table-column prop=\"deptName\" label=\"客户名称\" align=\"center\" show-overflow-tooltip width=\"115\" sortable/>\n        <el-table-column prop=\"applicant\" label=\"申请人\" align=\"center\" show-overflow-tooltip width=\"115\" sortable/>\n        <el-table-column prop=\"applyDeptName\" label=\"申请部门\" align=\"center\" show-overflow-tooltip width=\"115\" sortable/>\n        <el-table-column prop=\"applyDate\" label=\"申请日期\" align=\"center\" show-overflow-tooltip width=\"125\" sortable/>\n        <el-table-column prop=\"sourceNo\" label=\"PLM打样单号\" align=\"center\" show-overflow-tooltip width=\"150\" sortable/>\n        <el-table-column prop=\"sampleNo\" label=\"送样单号\" show-overflow-tooltip width=\"125\" sortable/>\n        <!-- <el-table-column prop=\"sampleDate\" label=\"送样日期\" show-overflow-tooltip width=\"125\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.sampleDate | date}}</span>\n          </template>\n        </el-table-column> -->\n        <el-table-column prop=\"caseDate\" label=\"送样日期\" show-overflow-tooltip width=\"125\" align=\"center\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.caseDate | date}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"vendorCode\" label=\"供应商编码\" show-overflow-tooltip width=\"120\" sortable/>\n        <el-table-column prop=\"vendorName\" label=\"供应商名称\" show-overflow-tooltip width=\"120\" sortable/>\n        <el-table-column prop=\"caseStat\" label=\"单据状态\" show-overflow-tooltip width=\"115\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.caseStat | commonEnumsTurn('base.SampleEnums')}}</span>\n          </template>\n        </el-table-column>\n        <!-- <el-table-column prop=\"itemStat\" label=\"检验状态\" show-overflow-tooltip width=\"115\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.itemStat | commonEnumsTurn('base.SampleItemEnums')}}</span>\n          </template>\n        </el-table-column> -->\n        <el-table-column prop=\"goodsErpCode\" label=\"物料编码\" show-overflow-tooltip width=\"120\" sortable/>\n        <el-table-column prop=\"goodsName\" label=\"物料名称\" show-overflow-tooltip width=\"120\" sortable/>\n        <el-table-column prop=\"goodsModel\" label=\"物料型号\" show-overflow-tooltip width=\"120\" sortable/>\n        <el-table-column prop=\"demandQty\" label=\"需求数量\" show-overflow-tooltip width=\"100\" sortable/>\n        <el-table-column prop=\"demandDate\" label=\"需求日期\" show-overflow-tooltip width=\"100\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.demandDate | date}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"replyQuantity\" label=\"回复数量\" align=\"center\" show-overflow-tooltip width=\"100\" sortable/>\n        <el-table-column prop=\"replyDeliveryDate\" label=\"回复交期\" align=\"center\" show-overflow-tooltip width=\"100\" sortable>\n          <template slot-scope=\"scope\">\n              <span>{{scope.row.replyDeliveryDate | date}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"replyState\" label=\"答交状态\" align=\"center\" width=\"150\">\n                <template slot-scope=\"scope\">\n                  <span>{{scope.row.replyState | commonEnumsTurn('base.ReplyStateEnum')}}</span>\n                </template>\n              </el-table-column>\n        <el-table-column prop=\"purName\" label=\"采购员\" align=\"center\" show-overflow-tooltip width=\"100\" sortable/>\n        <el-table-column prop=\"goodsNum\" label=\"送样数量\" show-overflow-tooltip width=\"100\" sortable/>\n        <el-table-column prop=\"vendorRemark\" label=\"供应商说明\" show-overflow-tooltip width=\"200\" sortable/>\n        <el-table-column prop=\"creater\" label=\"创建人\" show-overflow-tooltip width=\"150\" sortable/>\n        <el-table-column prop=\"createDate\" label=\"创建时间\" width=\"150\" sortable/>\n        <el-table-column prop=\"modifier\" label=\"更新人\" show-overflow-tooltip width=\"150\" sortable/>\n        <el-table-column prop=\"modifyDate\" label=\"更新时间\" width=\"150\" sortable/>\n        <el-table-column label=\"操作\" fixed=\"right\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"addOrUpdateHandle(scope.row.id)\" v-has-per=\"'base:sample:info'\">\n              <span v-if=\"scope.row.sampleStat == 2\">\n                {{ $t('common.editBtn')}}\n              </span>\n              <span v-if=\"scope.row.sampleStat != 2\">\n                {{ $t('common.lookBtn')}}\n              </span>\n            </el-button>\n          </template>\n        </el-table-column>\n      </d-table>\n      <d-pagination :total=\"total\" :page.sync=\"queryParam.page\"\n                  :limit.sync=\"queryParam.limit\" @pagination=\"initData\"/>\n    </div>\n    <!-- FORM表单 -->\n    <Form ref=\"detail\" v-show=\"detailVisible\" @callRefreshList=\"callRefreshList\"></Form>\n  </div>\n</div>\n", null]}