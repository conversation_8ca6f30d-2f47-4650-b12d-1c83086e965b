{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue", "mtime": 1754288813438}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { confirmSample,getSampleInfo,updateSample,acceptSample,rejectSample,replySample,createSample } from '@/api/base/sample'\r\nimport {getClassInfoByCode} from '@/api/base/classManage'\r\nimport store from '@/store'\r\nimport {handleGoodsDrawing} from '@/api/base/goods'\r\nimport GoodsPopup from \"@/views/popup/base/goods/goodsPopup\";\r\nimport VendorProp from '@/views/popup/base/vendor/vendor'\r\nexport default {\r\n  components: {\r\n    GoodsPopup,\r\n    VendorProp,\r\n  },\r\n  data() {\r\n    return {\r\n      isUpload:true,\r\n      isAdd:true,\r\n      dataForm: {\r\n        id: 0,//送样通知单主表id\r\n        tenantId:'',\r\n        tenantName:'',\r\n        sampleNo: '',//送样单号\r\n        vendorId:'',//供应商id\r\n        vendorCode:'',//供应商编码\r\n        vendorErpCode:'',//供应商Erp编码\r\n        vendorName:'',//供应商名称\r\n        deptId:'',//机构id\r\n        deptCode:'',//机构编码\r\n        deptName:'',//机构名称\r\n        sampleDate:'',//要求送样日期\r\n        sampleStat: 1,//单据状态 1-待审核;2-待送样;3-待质检;4-质检退回;5-已审核;9-已作废\r\n        isNeedUpFile: null,\r\n        remark: '',//备注\r\n        sampleItemEntityList: [],//送样单明细数据数组\r\n      },\r\n      selectedMaterialItems: [], // 选中的物料明细行(多选)\r\n      index:'',\r\n      docActiveName: 'tenant', // doc文档选项卡\r\n      btnLoading: false,//控制按钮是否正在加载条件\r\n      loading: false,//控制页面是否正在加载条件\r\n      activeName: 'form',\r\n      statOptions: store.getters.commonEnums['base.SampleEnums'],//送样单单据状态枚举类\r\n      whetherOpts: store.getters.commonEnums['comm.ValidEnum'],//送样单单据状态枚举类\r\n      demandTypeOpts: store.getters.commonEnums['base.DemandClassTypeEnum'],\r\n      dataRule: {\r\n        isNeedUpFile:[\r\n          {required: true, message: '是否需要上传资质文件不能为空', trigger: 'input'},\r\n        ],\r\n        vendorCode:[\r\n          {required: true, message: '供应商编码不能为空', trigger: 'input'},\r\n        ],\r\n        vendorName:[\r\n          {required: true, message: '供应商名称不能为空', trigger: 'input'},\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    //新增页面或查看详情\r\n    init(id) {\r\n      if (this.$refs.dataForm);\r\n      this.docActiveName = 'vendor';\r\n      this.$refs.dataForm.resetFields();\r\n      this.activeName='form';\r\n      this.dataForm = this.$options.data().dataForm;\r\n      this.isAdd=id?true:false;\r\n      this.dataForm.id = this.isAdd ? id : new Date().getTime();\r\n      this.$nextTick(() => {\r\n        if (id) {\r\n          this.loading = true;\r\n          getSampleInfo(id).then(res => {\r\n            this.dataForm = res.data;\r\n            if (this.dataForm.sampleItemEntityList && this.dataForm.sampleItemEntityList.length > 0) {\r\n              this.dataForm.sampleItemEntityList.forEach(item => {\r\n                if (item.replyQuantity && (!item.goodsNum || item.goodsNum === 0)) {\r\n                  item.goodsNum = item.replyQuantity;\r\n                }\r\n              });\r\n            }\r\n            this.loading = false;\r\n          }).catch(res => {\r\n            this.loading = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //保存\r\n    submit() {\r\n      this.$refs['dataForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          //明细行数据校验\r\n          const isLineValid = this.lineCheck();\r\n          if(!isLineValid){\r\n            this.btnLoading = false;\r\n            return;\r\n          }\r\n          const formMethod = this.isAdd ? updateSample : createSample;\r\n          formMethod(this.dataForm).then((res) => {\r\n            this.isAdd = true;\r\n            this.$message({\r\n              message: \"保存成功\",\r\n              type: 'success',\r\n              duration: 1500,\r\n              onClose: () => {\r\n                this.btnLoading = false\r\n                if(res.data){\r\n                  this.loading = true;\r\n                  getSampleInfo(res.data).then(res => {\r\n                    this.dataForm = res.data;\r\n                    this.loading = false;\r\n                  })\r\n                }\r\n              }\r\n            })\r\n          }).catch((res) => {\r\n            this.btnLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //返回列表\r\n    goBack() {\r\n      this.$emit('callRefreshList');\r\n    },\r\n    //打开供应商弹窗\r\n    choiceVendor() {\r\n      this.$nextTick(() => {\r\n        this.$refs.vendor.init();\r\n      })\r\n    },\r\n    //获取已选择的供应商赋值\r\n    vendorSelect(data) {\r\n      this.dataForm.vendorId = data[0].id;//供应商id\r\n      this.dataForm.vendorCode = data[0].vendorCode;//供应商编码\r\n      this.dataForm.vendorErpCode = data[0].vendorErpCode;//供应商Erp编码\r\n      this.dataForm.vendorName = data[0].vendorName;//供应商名称\r\n    },\r\n    //打开物料选择弹窗\r\n    choiceGoods(){\r\n      if(!this.dataForm.vendorId){\r\n        this.$message.error('供应商信息为空，请先选择供应商');\r\n        return\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.goods.init();\r\n      })\r\n    },\r\n    //获取已选择的物料\r\n    getSelectGoods(list) {\r\n      //当送样明细数据数组长度大于0时，校验选择的数据是否有在需求申请明细数据中已存在，已存在的需要抛出错误信息\r\n      if (this.dataForm.sampleItemEntityList.length > 0) {\r\n        for (let i = 0; i < list.length; i++) {\r\n          const e = list[i];\r\n          for (let j = 0; j < this.dataForm.sampleItemEntityList.length; j++) {\r\n            if (this.dataForm.sampleItemEntityList[j].goodsId == e.id) {\r\n              this.$message.error('选择的产品编码' + e.goodsCode + '已存在，请勿重复添加');\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.addSampleItem(list);\r\n    },\r\n    //新增明细\r\n    addSampleItem(list){\r\n      if(list.length > 0){\r\n        list.forEach((item,index) => {\r\n          let sampleItem = {\r\n            id:null,\r\n            tenantId:'',\r\n            sampleId:'',//送样单主表id\r\n            goodsId:item.id,//物料id\r\n            goodsErpCode:item.goodsErpCode,//ERP物料编码\r\n            goodsCode:item.goodsCode,//物料编码\r\n            goodsName:item.goodsName,//物料名称\r\n            goodsModel:item.goodsModel,//物料规格\r\n            goodsNum:item.goodsNum,//送样数量\r\n            itemStat:1,//明细行状态 1-待审核;2-待送样;3-待质检\r\n            vendorRemark:'',//供应商说明\r\n            remark:'',//采购方说明\r\n          }\r\n          this.dataForm.sampleItemEntityList.push(sampleItem);\r\n        })\r\n      }\r\n    },\r\n    //删除单条产品明细\r\n    handleDel(index, name, row) {\r\n      this.dataForm[name].splice(index, 1);\r\n    },\r\n    //批量明细删除\r\n    delLine(lineName) {\r\n      this.$confirm('是否批量删除所选明细', '提示', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        let rowThis = this;\r\n        //获取已选择数据\r\n        rowThis.$refs[lineName].$refs.DIANTable.selection.map((orw) => {\r\n          //获取列表数据\r\n          rowThis.dataForm[lineName].forEach((dataRow, index) => {\r\n            //判断是否为同一行\r\n            if (orw.__ob__.dep.id == dataRow.__ob__.dep.id) {\r\n              //删除当前行\r\n              rowThis.dataForm[lineName].splice(index, 1);\r\n            }\r\n          })\r\n        })\r\n        this.$message({\r\n          type: 'success',\r\n          message: '删除成功',\r\n          duration: 1500,\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    //校验明细行\r\n    lineCheck(){\r\n      if (this.dataForm.sampleItemEntityList.length == 0){\r\n        this.btnLoading = false;\r\n        this.$message.error('请添加物料明细');\r\n        return false;\r\n      }\r\n      // let lineList = this.dataForm.sampleItemEntityList;\r\n      // for (let i = 0; i < lineList.length; i++) {\r\n      //   const item = lineList[i];\r\n      //   const idx = i + 1;\r\n      //   if(!item.goodsNum){\r\n      //      this.btnLoading = false;\r\n      //      this.$message({type: 'error', message: '物料信息第'+idx+'行的送样数量为空'});\r\n      //      throw new Error()\r\n      //    }\r\n      //   // 资质文件校验\r\n      //   if (this.dataForm.isNeedUpFile === 1 && !item.vendorFilePath) {\r\n      //     this.btnLoading = false;\r\n      //     this.$message.error(`物料信息第${idx}行的资质文件为空，请上传`);\r\n      //     return false; // 校验失败，返回false\r\n      //   }\r\n      // }\r\n      return true; // 校验通过，返回true\r\n    },\r\n    // 确认送样\r\n    confirmSample(){\r\n      // 检查是否选择了物料明细行\r\n      if(this.selectedMaterialItems.length === 0) {\r\n        this.$message.error(\"请选择要确认送样的物料明细行需求\");\r\n        return;\r\n      }\r\n\r\n      // 检查选中的物料明细行状态\r\n      const invalidItems = this.selectedMaterialItems.filter(item => item.replyState != 2);\r\n      if(invalidItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有未答交的需求，无法确认送样');\r\n        return;\r\n      }\r\n\r\n      // 检查送样数量是否大于0\r\n      const noQuantityItems = this.selectedMaterialItems.filter(item => !item.goodsNum || item.goodsNum <= 0);\r\n      if(noQuantityItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有送样数量为空或小于等于0的记录，请先填写送样数量');\r\n        return;\r\n      }\r\n\r\n      // 检查是否有已确认送样的明细行 (SampleEnums.STAT4 = 4 待收样)\r\n      const confirmedItems = this.selectedMaterialItems.filter(item => item.caseStat == 4);\r\n      if(confirmedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已确认送样的需求，请勿重复确认');\r\n        return;\r\n      }\r\n\r\n      // 检查是否有已拒绝的明细行 (SampleEnums.STAT11 = 11 已拒绝)\r\n      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseStat == 11);\r\n      if(rejectedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已拒绝的需求，无法确认送样');\r\n        return;\r\n      }\r\n\r\n      this.$refs['dataForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          const isLineValid = this.lineCheck();\r\n          if(!isLineValid){\r\n            this.btnLoading = false;\r\n            return;\r\n          }\r\n          const formMethod = this.isAdd ? updateSample : createSample;\r\n          formMethod(this.dataForm).then((res) => {\r\n            if(res.data){\r\n              this.dataForm.id = res.data;\r\n\r\n              // 构建批量确认送样的参数\r\n              const ids = this.selectedMaterialItems.map(item => item.id);\r\n              console.log(\"批量确认送样 =======> \", ids);\r\n\r\n              confirmSample(ids).then(res => {\r\n                this.$message({\r\n                  message: `成功确认${ids.length}个物料明细送样`,\r\n                  type: 'success',\r\n                  duration: 1500,\r\n                  onClose: () => {\r\n                    this.btnLoading = false;\r\n                    this.selectedMaterialItems = []; // 重置选中项\r\n                    if(res.data){\r\n                      this.loading = true;\r\n                      getSampleInfo(this.dataForm.id).then(res => {\r\n                        this.dataForm = res.data;\r\n                        this.loading = false;\r\n                      })\r\n                    }\r\n                  }\r\n                })\r\n              }).catch(err => {\r\n                this.btnLoading = false\r\n              })\r\n            } else {\r\n              this.btnLoading = false;\r\n            }\r\n          }).catch((err) => {\r\n            this.btnLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //上传文件\r\n    uploadFlie(rowData,index){\r\n      this.index = index;\r\n      this.$refs.fileUpload.init(\"base_sample_item\",index,0);\r\n    },\r\n    //获取上传文件后返回的文件路径\r\n    upload(res){\r\n      // this.dataForm.sampleItemEntityList[this.index].vendorFileName = res.data.name;\r\n      // this.dataForm.sampleItemEntityList[this.index].vendorFilePath = res.data.src;\r\n      this.dataForm.sampleItemEntityList[this.index].quaFileName = res.data.name;\r\n      this.dataForm.sampleItemEntityList[this.index].quaFilePath = res.data.src;\r\n    },\r\n    //查看文件\r\n    lookFile(url){\r\n      window.open(url);\r\n    },\r\n    //跳转图纸网址\r\n    handleDrawing(val){\r\n      const params={\r\n        goodsErpCode:val\r\n      }\r\n      handleGoodsDrawing(params).then(res =>{\r\n        window.open(res.data);\r\n      }).catch((res) => {})\r\n    },\r\n    //确认需要送样\r\n    acceptSample(){\r\n      this.btnLoading = true\r\n      acceptSample(this.dataForm).then(res => {\r\n        this.$message({\r\n          message: \"确认成功\",\r\n          type: 'success',\r\n          duration: 1500,\r\n          onClose: () => {\r\n            this.btnLoading = false\r\n            if(res.data){\r\n              this.loading = true;\r\n              getSampleInfo(res.data).then(res => {\r\n                this.dataForm = res.data;\r\n                this.loading = false;\r\n              })\r\n            }\r\n          }\r\n        })\r\n      }).catch(res => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    //拒绝该送样通知单\r\n    rejectSample(){\r\n      if(this.selectedMaterialItems.length === 0) {\r\n        this.$message.error(\"请选择要拒绝的物料明细行需求\");\r\n        return;\r\n      }\r\n      \r\n      // 检查是否有已拒绝或已答交的明细行\r\n      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseState == 11);\r\n      if(rejectedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已拒绝的需求，请勿重复拒绝');\r\n        return;\r\n      }\r\n      \r\n      const repliedItems = this.selectedMaterialItems.filter(item => item.replyState == 2);\r\n      if(repliedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已答交的需求，不允许拒绝');\r\n        return;\r\n      }\r\n      \r\n      this.$confirm('确认拒绝选中的物料明细行需求?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true;\r\n        \r\n        // 构建批量拒绝的参数\r\n        const ids = this.selectedMaterialItems.map(item => item.id);\r\n        console.log(\"批量拒绝送样 =======> \", ids);\r\n        \r\n        rejectSample(ids).then(res => {\r\n          this.$message({\r\n            message: `成功拒绝${ids.length}个物料明细`,\r\n            type: 'success',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.btnLoading = false;\r\n              this.selectedMaterialItems = []; // 重置选中项\r\n              if(res.data){\r\n                this.loading = true;\r\n                getSampleInfo(this.dataForm.id).then(res => {\r\n                  this.dataForm = res.data;\r\n                  this.loading = false;\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }).catch(res => {\r\n          this.btnLoading = false;\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消拒绝'\r\n        });\r\n      });\r\n    },\r\n    // 答交送样\r\n    replySample(){\r\n      if(this.selectedMaterialItems.length === 0) {\r\n        this.$message.error(\"请选择要答交的物料明细行需求\");\r\n        return;\r\n      }\r\n\r\n      // 检查是否上传了资质文件\r\n      if(this.dataForm.isNeedUpFile === 1) {\r\n        const noFileItems = this.selectedMaterialItems.filter(item => !item.quaFilePath);\r\n        if(noFileItems.length > 0) {\r\n          this.$message.error('选中的物料明细中有未上传资质文件的记录，请先上传资质文件');\r\n          return;\r\n        }\r\n      }\r\n\r\n      // 检查是否有已拒绝或已答交的明细行\r\n      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseState == 11);\r\n      if(rejectedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已拒绝的需求，不允许答交');\r\n        return;\r\n      }\r\n\r\n      // 检查回复数量是否为空或小于等于0\r\n      const emptyReplyItems = this.selectedMaterialItems.filter(item => !item.replyQuantity || item.replyQuantity <= 0);\r\n      if(emptyReplyItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有回复数量为空或小于等于0的记录，请先填写回复数量');\r\n        return;\r\n      }\r\n\r\n      // 检查回复交期是否为空\r\n      const emptyReplyDateItems = this.selectedMaterialItems.filter(item => !item.replyDeliveryDate);\r\n      if(emptyReplyDateItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有回复交期为空的记录，请先填写回复交期');\r\n        return;\r\n      }\r\n      \r\n      const repliedItems = this.selectedMaterialItems.filter(item => item.replyState == 2);\r\n      if(repliedItems.length > 0) {\r\n        this.$message.error('选中的物料明细中有已答交的需求，请勿重复答交');\r\n        return;\r\n      }\r\n      \r\n      this.$refs['dataForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          const isLineValid = this.lineCheck();\r\n          if(!isLineValid){\r\n            this.btnLoading = false;\r\n            return;\r\n          }\r\n          const formMethod = this.isAdd ? updateSample : createSample;\r\n          formMethod(this.dataForm).then((res) => {\r\n            if(res.data){\r\n              this.dataForm.id = res.data;\r\n              \r\n              // 构建批量答交的参数\r\n              const ids = this.selectedMaterialItems.map(item => item.id);    \r\n              console.log(\"批量答交送样 =======> \", ids);           \r\n              replySample(ids).then(res => {\r\n                this.$message({\r\n                  message: `成功答交${ids.length}个物料明细`,\r\n                  type: 'success',\r\n                  duration: 1500,\r\n                  onClose: () => {\r\n                    this.btnLoading = false;\r\n                    this.selectedMaterialItems = []; // 重置选中项\r\n                    if(res.data){\r\n                      this.loading = true;\r\n                      getSampleInfo(this.dataForm.id).then(res => {\r\n                        this.dataForm = res.data;\r\n                        this.loading = false;\r\n                      })\r\n                    }\r\n                  }\r\n                })\r\n              }).catch(res => {\r\n                this.btnLoading = false;\r\n              });\r\n            } else {\r\n              this.btnLoading = false;\r\n            }\r\n          }).catch((err) => {\r\n            this.btnLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedMaterialItems = selection;\r\n    },\r\n    handleReplyQuantityChange(val, row) {\r\n      if (val !== null && val !== undefined) {\r\n        row.goodsNum = val;\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}