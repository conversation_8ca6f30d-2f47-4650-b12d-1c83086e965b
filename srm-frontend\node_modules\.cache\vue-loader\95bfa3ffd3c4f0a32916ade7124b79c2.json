{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue?vue&type=style&index=0&id=6e8dc346&lang=less&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue", "mtime": 1754292527998}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\css-loader\\index.js", "mtime": 1683164317321}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1683164318717}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1683164317478}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1683164319296}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n// 拖拽\r\n.drag_item_box {\r\n  height: 100%;\r\n  padding: 6px;\r\n}\r\n\r\n.drag_item_box > div {\r\n  height: 100%;\r\n  width: 100%;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.drag_item_box > div > a {\r\n  text-align: center;\r\n  text-decoration-line: none;\r\n}\r\n\r\n.drag_item_box > div > a > span {\r\n  font-size: 28px;\r\n}\r\n\r\n.drag_item_box > div > a > p {\r\n  margin: 0;\r\n}\r\n\r\n.drag_item_title {\r\n  font-size: 16px;\r\n  padding: 12px 6px 0 6px;\r\n  font-weight: bold;\r\n}\r\n\r\n// 默认图片\r\n/deep/ .hiprint-printElement-image-content {\r\n  img {\r\n    content: url(\"~@/assets/logo.png\");\r\n  }\r\n}\r\n\r\n// 设计容器\r\n.card-design {\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n  overflow-y: auto;\r\n}\r\n\r\n.button-group-container {\r\n  margin-bottom: 10px;\r\n  margin-left: 30px;\r\n  text-align: left;\r\n\r\n  .el-button-group {\r\n    .el-button {\r\n      // 调整按钮间距\r\n      margin: 0 5px;\r\n\r\n      // 调整按钮圆角\r\n      border-radius: 4px;\r\n\r\n      // 自定义按钮颜色和背景色\r\n      &.el-button--primary {\r\n        background-color: #409EFF;\r\n        border-color: #409EFF;\r\n      }\r\n\r\n      &.el-button--success {\r\n        background-color: #ee9245;\r\n        border-color: #ee9245;\r\n      }\r\n\r\n      &.el-button--warning {\r\n        background-color: #ee9245;\r\n        border-color: #ee9245;\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}