{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue?vue&type=template&id=ca416dcc&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\vendor\\form.vue", "mtime": 1754288813438}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"transition\", { attrs: { name: \"el-zoom-in-center\" } }, [\n    _c(\"div\", { staticClass: \"DIAN-preview-main nohead\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"DIAN-common-page-header\" },\n        [\n          _c(\"el-page-header\", {\n            attrs: { content: _vm.isAdd ? \"编辑送样单\" : \"新建送样单\" },\n            on: { back: _vm.goBack },\n          }),\n          _c(\n            \"div\",\n            { staticClass: \"options\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"has-per\",\n                      rawName: \"v-has-per\",\n                      value: \"base:sample:update\",\n                      expression: \"'base:sample:update'\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", loading: _vm.btnLoading },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submit()\n                    },\n                  },\n                },\n                [_vm._v(\"保存\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"has-per\",\n                      rawName: \"v-has-per\",\n                      value: \"base:sample:replySample\",\n                      expression: \"'base:sample:replySample'\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", loading: _vm.btnLoading },\n                  on: {\n                    click: function ($event) {\n                      return _vm.replySample()\n                    },\n                  },\n                },\n                [_vm._v(\"答交\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"has-per\",\n                      rawName: \"v-has-per\",\n                      value: \"base:sample:rejectSample\",\n                      expression: \"'base:sample:rejectSample'\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", loading: _vm.btnLoading },\n                  on: {\n                    click: function ($event) {\n                      return _vm.rejectSample()\n                    },\n                  },\n                },\n                [_vm._v(\"拒绝\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"has-per\",\n                      rawName: \"v-has-per\",\n                      value: \"base:sample:confirmSample\",\n                      expression: \"'base:sample:confirmSample'\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", loading: _vm.btnLoading },\n                  on: {\n                    click: function ($event) {\n                      return _vm.confirmSample()\n                    },\n                  },\n                },\n                [_vm._v(\"确认送样\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.goBack()\n                    },\n                  },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"common.cancelButton\")))]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"app-container Document-container nohead\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"dataForm\",\n              attrs: {\n                model: _vm.dataForm,\n                rules: _vm.dataRule,\n                \"label-width\": \"125px\",\n                id: \"dataForm\",\n              },\n            },\n            [\n              _c(\n                \"d-card\",\n                {\n                  ref: \"dCard\",\n                  attrs: { flow: false },\n                  model: {\n                    value: _vm.activeName,\n                    callback: function ($$v) {\n                      _vm.activeName = $$v\n                    },\n                    expression: \"activeName\",\n                  },\n                },\n                [\n                  _c(\n                    \"d-card-item\",\n                    { attrs: { label: \"基础信息\", name: \"form\" } },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.loading,\n                              expression: \"loading\",\n                            },\n                          ],\n                          staticClass: \"DIAN-flex-main\",\n                        },\n                        [\n                          _c(\n                            \"el-row\",\n                            [\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    { attrs: { label: \"PLM打样单号\" } },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"<系统自动生成>\",\n                                          readonly: \"\",\n                                          disabled: true,\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.sourceNo,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"sourceNo\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.sourceNo\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    { attrs: { label: \"送样单号\" } },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"<系统自动生成>\",\n                                          readonly: \"\",\n                                          disabled: true,\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.sampleNo,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"sampleNo\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.sampleNo\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"客户名称\",\n                                        prop: \"deptName\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          readonly: \"\",\n                                          placeholder: \"客户名称\",\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.deptName,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"deptName\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.deptName\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"供应商编码\",\n                                        prop: \"vendorCode\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          readonly: \"\",\n                                          placeholder: \"供应商编码\",\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.vendorCode,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"vendorCode\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.vendorCode\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"供应商名称\",\n                                        prop: \"vendorName\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"供应商名称\",\n                                          readonly: \"\",\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.vendorName,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"vendorName\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.vendorName\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"是否上传资质文件\",\n                                        prop: \"sampleStat\",\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"el-select\",\n                                        {\n                                          attrs: { disabled: \"\" },\n                                          model: {\n                                            value: _vm.dataForm.isNeedUpFile,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                _vm.dataForm,\n                                                \"isNeedUpFile\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression: \"dataForm.isNeedUpFile\",\n                                          },\n                                        },\n                                        _vm._l(\n                                          _vm.whetherOpts,\n                                          function (item) {\n                                            return _c(\"el-option\", {\n                                              key: item.key,\n                                              attrs: {\n                                                label: item.value,\n                                                value: parseInt(item.key),\n                                              },\n                                            })\n                                          }\n                                        ),\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"创建人\",\n                                        prop: \"creater\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"<系统自动生成>\",\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.creater,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"creater\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.creater\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"创建时间\",\n                                        prop: \"createDate\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-date-picker\", {\n                                        attrs: {\n                                          type: \"datetime\",\n                                          placeholder: \"<系统自动生成>\",\n                                          disabled: \"\",\n                                          \"value-format\": \"yyyy-MM-dd HH:mm:ss\",\n                                          format: \"yyyy-MM-dd HH:mm:ss\",\n                                          editable: false,\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.createDate,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"createDate\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.createDate\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"修改人\",\n                                        prop: \"modifier\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"<系统自动生成>\",\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.modifier,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"modifier\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.modifier\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"修改时间\",\n                                        prop: \"modifyDate\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-date-picker\", {\n                                        attrs: {\n                                          type: \"datetime\",\n                                          placeholder: \"<系统自动生成>\",\n                                          disabled: \"\",\n                                          \"value-format\": \"yyyy-MM-dd HH:mm:ss\",\n                                          format: \"yyyy-MM-dd HH:mm:ss\",\n                                          editable: false,\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.modifyDate,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"modifyDate\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.modifyDate\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 6 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: {\n                                        label: \"单据类型\",\n                                        prop: \"demandClassType\",\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"el-select\",\n                                        {\n                                          attrs: { disabled: \"\" },\n                                          model: {\n                                            value: _vm.dataForm.demandClassType,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                _vm.dataForm,\n                                                \"demandClassType\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"dataForm.demandClassType\",\n                                          },\n                                        },\n                                        _vm._l(\n                                          _vm.demandTypeOpts,\n                                          function (item) {\n                                            return _c(\"el-option\", {\n                                              key: item.key,\n                                              attrs: {\n                                                label: item.value,\n                                                value: parseInt(item.key),\n                                              },\n                                            })\n                                          }\n                                        ),\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-row\",\n                            [\n                              _c(\n                                \"el-col\",\n                                { attrs: { span: 24 } },\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    {\n                                      attrs: { label: \"备注\", prop: \"remark\" },\n                                    },\n                                    [\n                                      _c(\"el-input\", {\n                                        attrs: {\n                                          placeholder: \"请输入备注\",\n                                          type: \"textarea\",\n                                          rows: 3,\n                                          disabled: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.dataForm.remark,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.dataForm,\n                                              \"remark\",\n                                              typeof $$v === \"string\"\n                                                ? $$v.trim()\n                                                : $$v\n                                            )\n                                          },\n                                          expression: \"dataForm.remark\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\"VendorProp\", {\n                        ref: \"vendor\",\n                        attrs: { singleChoice: true },\n                        on: { callData: _vm.vendorSelect },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"d-card-item\",\n                    {\n                      attrs: {\n                        label: \"物料明细\",\n                        name: \"sampleItemEntityList\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"DIAN-flex-main\" },\n                        [\n                          _c(\"div\", { staticClass: \"table-toolbars\" }),\n                          _c(\n                            \"d-table\",\n                            {\n                              ref: \"sampleItemEntityList\",\n                              attrs: {\n                                data: _vm.dataForm.sampleItemEntityList,\n                                size: \"mini\",\n                                hasC: \"\",\n                              },\n                              on: {\n                                \"selection-change\": _vm.handleSelectionChange,\n                              },\n                            },\n                            [\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"purName\",\n                                  label: \"采购员\",\n                                  align: \"center\",\n                                  width: \"120\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsCode\",\n                                  label: \"物料编码\",\n                                  align: \"center\",\n                                  width: \"120\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsName\",\n                                  label: \"物料名称\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                  \"show-overflow-tooltip\": \"\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsModel\",\n                                  label: \"规格型号\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                  \"show-overflow-tooltip\": \"\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"demandQty\",\n                                  label: \"需求数量\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"demandDate\",\n                                  label: \"需求日期\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            _vm._s(\n                                              _vm._f(\"date\")(\n                                                scope.row.demandDate\n                                              )\n                                            )\n                                          ),\n                                        ]),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"replyQuantity\",\n                                  label: \"回复数量\",\n                                  align: \"center\",\n                                  width: \"180\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          attrs: {\n                                            controls: false,\n                                            precision: 3,\n                                            clearable: \"\",\n                                            disabled:\n                                              _vm.dataForm.sampleStat > 3 ||\n                                              scope.row.replyState === 2,\n                                          },\n                                          on: {\n                                            change: function (val) {\n                                              return _vm.handleReplyQuantityChange(\n                                                val,\n                                                scope.row\n                                              )\n                                            },\n                                          },\n                                          model: {\n                                            value: scope.row.replyQuantity,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"replyQuantity\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.replyQuantity\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"replyDeliveryDate\",\n                                  label: \"回复交期\",\n                                  align: \"center\",\n                                  width: \"180\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-date-picker\", {\n                                          attrs: {\n                                            type: \"datetime\",\n                                            \"value-format\": \"yyyy-MM-dd\",\n                                            format: \"yyyy-MM-dd\",\n                                            editable: false,\n                                            disabled:\n                                              _vm.dataForm.sampleStat > 3 ||\n                                              scope.row.replyState === 2,\n                                          },\n                                          model: {\n                                            value: scope.row.replyDeliveryDate,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"replyDeliveryDate\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.replyDeliveryDate\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"replyState\",\n                                  label: \"答交状态\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            _vm._s(\n                                              _vm._f(\"commonEnumsTurn\")(\n                                                scope.row.replyState,\n                                                \"base.ReplyStateEnum\"\n                                              )\n                                            )\n                                          ),\n                                        ]),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsNum\",\n                                  label: \"送样数量\",\n                                  align: \"center\",\n                                  width: \"185\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          attrs: {\n                                            controls: false,\n                                            precision: 3,\n                                            clearable: \"\",\n                                            disabled:\n                                              _vm.dataForm.sampleStat > 3,\n                                          },\n                                          model: {\n                                            value: scope.row.goodsNum,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"goodsNum\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression: \"scope.row.goodsNum\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"caseDate\",\n                                  label: \"送样日期\",\n                                  align: \"center\",\n                                  width: \"180\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-date-picker\", {\n                                          attrs: {\n                                            type: \"datetime\",\n                                            placeholder: \"<系统自动生成>\",\n                                            \"value-format\": \"yyyy-MM-dd\",\n                                            format: \"yyyy-MM-dd\",\n                                            editable: false,\n                                            disabled: \"\",\n                                          },\n                                          model: {\n                                            value: scope.row.caseDate,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"caseDate\",\n                                                typeof $$v === \"string\"\n                                                  ? $$v.trim()\n                                                  : $$v\n                                              )\n                                            },\n                                            expression: \"scope.row.caseDate\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"caseStat\",\n                                  label: \"结案状态\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            _vm._s(\n                                              _vm._f(\"commonEnumsTurn\")(\n                                                scope.row.caseStat,\n                                                \"base.SampleEnums\"\n                                              )\n                                            )\n                                          ),\n                                        ]),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"quaFilePath\",\n                                  label: \"资质文件\",\n                                  align: \"center\",\n                                  \"show-overflow-tooltip\": \"\",\n                                  width: \"180\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\n                                          \"el-button\",\n                                          {\n                                            directives: [\n                                              {\n                                                name: \"show\",\n                                                rawName: \"v-show\",\n                                                value: scope.row.quaFilePath,\n                                                expression:\n                                                  \"scope.row.quaFilePath\",\n                                              },\n                                            ],\n                                            attrs: { type: \"text\" },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.lookFile(\n                                                  scope.row.quaFilePath\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                _vm._s(scope.row.vendorFileName)\n                                              ),\n                                            ]),\n                                          ]\n                                        ),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"vendorRemark\",\n                                  label: \"供方说明\",\n                                  align: \"center\",\n                                  \"show-overflow-tooltip\": \"\",\n                                  width: \"200\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input\", {\n                                          directives: [\n                                            {\n                                              name: \"show\",\n                                              rawName: \"v-show\",\n                                              value:\n                                                _vm.dataForm.sampleStat == 3,\n                                              expression:\n                                                \"dataForm.sampleStat == 3\",\n                                            },\n                                          ],\n                                          attrs: { clearable: \"\" },\n                                          model: {\n                                            value: scope.row.vendorRemark,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"vendorRemark\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.vendorRemark\",\n                                          },\n                                        }),\n                                        _c(\n                                          \"span\",\n                                          {\n                                            directives: [\n                                              {\n                                                name: \"show\",\n                                                rawName: \"v-show\",\n                                                value:\n                                                  _vm.dataForm.sampleStat != 3,\n                                                expression:\n                                                  \"dataForm.sampleStat != 3\",\n                                              },\n                                            ],\n                                          },\n                                          [\n                                            _vm._v(\n                                              _vm._s(scope.row.vendorRemark)\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  label: \"操作\",\n                                  fixed: \"right\",\n                                  align: \"center\",\n                                  width: \"180\",\n                                },\n                                scopedSlots: _vm._u([\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\n                                          \"el-button\",\n                                          {\n                                            attrs: {\n                                              size: \"mini\",\n                                              type: \"text\",\n                                            },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.uploadFlie(\n                                                  scope.row,\n                                                  scope.$index\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \"\\n                    上传资质文件\\n                  \"\n                                            ),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"el-button\",\n                                          {\n                                            attrs: {\n                                              type: \"text\",\n                                              size: \"mini\",\n                                            },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.handleDrawing(\n                                                  scope.row.goodsErpCode\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [_vm._v(\"查看图纸\")]\n                                        ),\n                                      ]\n                                    },\n                                  },\n                                ]),\n                              }),\n                            ],\n                            1\n                          ),\n                          _c(\"GoodsPopup\", {\n                            ref: \"goods\",\n                            attrs: { singleChoice: false },\n                            on: { callData: _vm.getSelectGoods },\n                          }),\n                          _c(\"d-file-upLoad\", {\n                            ref: \"fileUpload\",\n                            attrs: { multiple: true },\n                            on: { fileUpload: _vm.upload },\n                          }),\n                        ],\n                        1\n                      ),\n                    ]\n                  ),\n                  _c(\n                    \"d-card-item\",\n                    { attrs: { label: \"附件明细\", name: \"doc\" } },\n                    [\n                      _c(\n                        \"el-tabs\",\n                        { attrs: { \"active-name\": _vm.docActiveName } },\n                        [\n                          _c(\n                            \"el-tab-pane\",\n                            {\n                              attrs: {\n                                label: \"采购方相关文档\",\n                                name: \"tenant\",\n                              },\n                            },\n                            [\n                              _c(\"d-doc-table-list\", {\n                                ref: \"docTableList\",\n                                attrs: {\n                                  tableName: \"base_sample\",\n                                  headId: _vm.dataForm.id,\n                                  lineId: 1,\n                                  tenantId: _vm.dataForm.tenantId,\n                                  disabled: true,\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"el-tab-pane\",\n                            {\n                              attrs: {\n                                label: \"供应方相关文档\",\n                                name: \"vendor\",\n                              },\n                            },\n                            [\n                              _c(\"d-doc-table-list\", {\n                                ref: \"docTableList\",\n                                attrs: {\n                                  tableName: \"base_sample\",\n                                  headId: _vm.dataForm.id,\n                                  lineId: 2,\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}