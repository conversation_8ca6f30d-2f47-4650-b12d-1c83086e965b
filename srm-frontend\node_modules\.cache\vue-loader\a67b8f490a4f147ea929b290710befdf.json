{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue?vue&type=template&id=93d7bb56&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\index.vue", "mtime": 1754291216892}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n  <div class=\"DIAN-common-layout\">\n    <div class=\"DIAN-common-layout-center\">\n\n      <!-- 搜索框 -->\n      <el-row class=\"DIAN-common-search-box\" :gutter=\"24\">\n        <el-form @submit.native.prevent>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.purNo\" placeholder=\"请输入采购订单号\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-select v-model=\"queryParam.itemStat\" placeholder=\"请选择单据状态\" clearable>\n                <el-option\n                  :key=\"item.key\"\n                  :label=\"item.value\"\n                  :value=\"parseInt(item.key)\"\n                  v-for=\"item in orderStatOptions\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-input v-model.trim=\"queryParam.goods\" placeholder=\"请输入物料编码/名称/型号\" clearable/>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.purName\" placeholder=\"请输入采购员名称\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <template v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item>\n                <el-date-picker\n                  v-model=\"queryParam.orderDate\"\n                  type=\"daterange\"\n                  placeholder=\"请输入订单日期\"\n                  range-separator=\"至\"\n                  start-placeholder=\"（订单）开始日期\"\n                  end-placeholder=\"（订单）结束日期\"\n                  clearable>\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </template>\n<!--          <el-col :span=\"5\">-->\n<!--            <el-form-item>-->\n<!--              <el-select v-model=\"queryParam.bsart\" placeholder=\"请选择采购类型\" clearable>-->\n<!--                <el-option-->\n<!--                  v-for=\"item in bsartTypeOptions\"-->\n<!--                  :key=\"item\"-->\n<!--                  :label=\"item.value\"-->\n<!--                  :value=\"item.key\"-->\n<!--                />-->\n<!--              </el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n\n          <el-col :span=\"6\">\n            <el-form-item>\n               <!-- 查询按钮 -->\n              <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">\n                {{ $t('common.search') }}\n              </el-button>\n              <!-- 重置按钮 -->\n              <el-button icon=\"el-icon-refresh-right\" @click=\"reset()\">\n                {{ $t('common.reset') }}\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\"\n                         v-if=\"!showAll\">展开\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>\n                收起\n              </el-button>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <!-- body -->\n      <div class=\"DIAN-common-layout-main DIAN-flex-main\">\n        <!-- 表头工具栏 -->\n        <div class=\"DIAN-common-head\">\n          <div>\n          </div>\n          <div class=\"DIAN-common-head-right\">\n            <el-button type=\"primary\"\n                       @click=\"oneClickBatchConfirm()\"\n                       :loading=\"btnLoading\" >\n              批量确认\n            </el-button>\n            <el-button size=\"small\"\n                       type=\"primary\"\n                       @click=\"openCreateFrom()\"\n                       icon=\"el-icon-plus\"\n                       v-has-per=\"'dm:Delivery:save'\">\n              创建送货单\n            </el-button>\n            <el-tooltip effect=\"dark\" :content=\"$t('common.refresh')\" placement=\"top\">\n              <el-link icon=\"icon-ym icon-ym-Refresh DIAN-common-head-icon\" :underline=\"false\" @click=\"search()\" />\n            </el-tooltip>\n            <d-screen-full/>\n          </div>\n        </div>\n\n        <!-- 表格 -->\n        <d-table ref=\"listTable\" v-loading=\"listLoading\" :data=\"list\" hasC :hasNO=\"false\" selection @selection-change=\"handleSelectionChange\">\n          <el-table-column prop=\"purNo\" label=\"采购订单号\" align=\"center\" show-overflow-tooltip width=\"130\" fixed=\"left\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.purNo + '/' + scope.row.seq }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"goodsErpCode\" label=\"物料编码\" show-tooltip-when-overflow width=\"130\" fixed=\"left\"/>\n          <el-table-column prop=\"goodsName\" label=\"物料名称\" show-tooltip-when-overflow width=\"120\" fixed=\"left\"/>\n          <el-table-column prop=\"goodsModel\" label=\"规格型号\" show-tooltip-when-overflow width=\"180\" fixed=\"left\"/>\n          <el-table-column prop=\"purName\" label=\"采购员\" align=\"center\" show-overflow-tooltip width=\"90\"/>\n          <el-table-column prop=\"orderType\" label=\"订单类型\" align=\"center\" show-overflow-tooltip width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.orderType | commonEnumsTurn(\"common.JinDieOrderTypeEnum\")}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"orderDate\" label=\"订单日期\" align=\"center\" show-overflow-tooltip width=\"150\">\n            <template slot-scope=\"scope\">\n              <span>{{ $dian.dateFormat(scope.row.orderDate, 'YYYY-MM-DD') }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"publishDate\" label=\"发布时间\" align=\"center\" show-overflow-tooltip width=\"150\"/>\n          <el-table-column prop=\"totalAmount\" label=\"含税总金额\" align=\"center\" show-overflow-tooltip width=\"150\" v-if=\"$dian.hasPerBtnP('order:vendor:lookPrice')\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.gstPrice * scope.row.orderNum}}</span>\n            </template>\n          </el-table-column>\n          <!-- 明细行数据 -->\n          <el-table-column prop=\"itemStat\" label=\"单据状态\" width=\"90\">\n            <template slot-scope=\"scope\">\n              <span>{{ scope.row.itemStat | commonEnumsTurn(\"order.PurlineStatEnum\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"isClose\" label=\"是否关闭\" show-tooltip-when-overflow width=\"100\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.isClose === 1\">是</span>\n              <span v-else>否</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"erpChangeType\" label=\"变更类型\" show-tooltip-when-overflow width=\"100\"/>\n          <el-table-column prop=\"deliveryStatus\" label=\"送货状态\" width=\"100\"/>\n          <el-table-column prop=\"orderNum\" label=\"订单数量\" width=\"100\"/>\n          <el-table-column prop=\"makeNum\" label=\"已制单数量\" width=\"100\"/>\n          <el-table-column prop=\"unMakeNum\" label=\"未制单数量\" width=\"100\"/>\n          <el-table-column prop=\"fixNum\" label=\"已送数量\" width=\"100\"/>\n          <el-table-column prop=\"waitNum\" label=\"待送数量\" width=\"100\"/>\n          <el-table-column prop=\"receiveNum\" label=\"暂收数量\" width=\"100\"/>\n          <el-table-column prop=\"refundNum\" label=\"暂退补料数量\" width=\"100\"/>\n          <el-table-column prop=\"refDedNum\" label=\"暂退扣款数量\" width=\"100\"/>\n          <el-table-column prop=\"erpMasterNum\" label=\"入库数量\" width=\"100\"/>\n          <el-table-column prop=\"erpRejectNum\" label=\"退货补料数量\" width=\"100\"/>\n          <el-table-column prop=\"retDedNum\" label=\"退货扣款数量\" width=\"100\"/>\n          <el-table-column prop=\"uomName\" label=\"单位\" width=\"100\"/>\n          <el-table-column prop=\"rateName\" label=\"税率\" width=\"100\"/>\n          <el-table-column prop=\"warehouseName\" label=\"仓库\" width=\"100\"/>\n          <el-table-column prop=\"deliveryDate\" label=\"交货日期\" width=\"150\">\n          </el-table-column>\n          <el-table-column prop=\"taxPrice\" label=\"不含税单价\" width=\"100\" v-if=\"$dian.hasPerBtnP('order:pur:lookPrice')\"/>\n          <el-table-column prop=\"gstPrice\" label=\"含税单价\" width=\"100\" v-if=\"$dian.hasPerBtnP('order:pur:lookPrice')\"/>\n\n          <el-table-column prop=\"remark\" label=\"备注\" align=\"center\" show-overflow-tooltip min-width=\"180\"/>\n          <el-table-column label=\"操作\" width=\"120\" fixed=\"right\" align=\"center\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" size=\"mini\" @click=\"addEditOrderHandle(scope.row.id)\" v-has-per=\"'order:sale:info'\"> {{ $t('common.lookBtn')}} </el-button>\n              <el-button size=\"mini\" type=\"text\" @click=\"printBarCode(scope.row)\">条码打印</el-button>\n            </template>\n          </el-table-column>\n          <CodePrintTemplate v-if=\"templateVisible\" ref=\"PrintTemplate\"/> <!-- 条码打印模版 -->\n        </d-table>\n        <d-pagination :total=\"total\" :page.sync=\"queryParam.page\" :limit.sync=\"queryParam.limit\" @pagination=\"initData\"/>\n      </div>\n\n      <!-- FORM表单 -->\n      <Form ref=\"form\" v-show=\"formVisible\" @callRefreshList=\"closeForm\"></Form>\n      <deliveryForm ref=\"deliveryForm\" v-show=\"deliveryFormVisible\" @callRefreshList=\"closeForm\"></deliveryForm>\n    </div>\n  </div>\n", null]}