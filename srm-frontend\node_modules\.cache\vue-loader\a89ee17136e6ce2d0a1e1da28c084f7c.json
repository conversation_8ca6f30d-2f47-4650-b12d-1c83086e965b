{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue?vue&type=template&id=91b4a6cc&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\delivery\\vendor\\index.vue", "mtime": 1754042535665}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n  <div class=\"DIAN-common-layout\">\n    <div class=\"DIAN-common-layout-center\">\n      <!-- 搜索框 -->\n      <el-row class=\"DIAN-common-search-box\" :gutter=\"16\">\n        <el-form @submit.native.prevent>\n          <!-- <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.dept\" placeholder=\"采购组织编码/名称\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.deNo\" placeholder=\"送货单号\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.orderNo\" placeholder=\"采购订单号\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.goods\" placeholder=\"物料编码/名称/规格\" @keyup.enter.native=\"search()\" clearable />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n              <el-form-item>\n                <el-select v-model=\"queryParam.deStat\" placeholder=\"单据状态\" clearable>\n                  <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                             v-for=\"item in deStatOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          <template v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item>\n                <el-date-picker\n                  v-model=\"deliveryDate\"\n                  type=\"daterange\"\n                  placeholder=\"请选择送货日期\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </template>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <!-- 查询按钮 -->\n              <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">\n                {{ $t('common.search') }}\n              </el-button>\n              <!-- 重置按钮 -->\n              <el-button icon=\"el-icon-refresh-right\" @click=\"reset()\">\n                {{ $t('common.reset') }}\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\"\n                         v-if=\"!showAll\">展开\n              </el-button>\n              <el-button type=\"text\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>\n                收起\n              </el-button>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <!-- body -->\n      <div class=\"DIAN-common-layout-main DIAN-flex-main\">\n        <!-- 表头工具栏 -->\n        <div class=\"DIAN-common-head\">\n          <div>\n           <el-button-group >\n              <el-button size=\"small\" :type=\"buttonFrom.count1\" @click=\"changeCountsButton(1 , 'count1')\">全部</el-button>\n              <el-button size=\"small\" :type=\"buttonFrom.count2\" @click=\"changeCountsButton(2 , 'count2')\">已发出</el-button>\n              <el-button size=\"small\" :type=\"buttonFrom.count3\" @click=\"changeCountsButton(3 , 'count3')\">待发出</el-button>\n            </el-button-group>\n          </div>\n          <div class=\"DIAN-common-head-right\">\n            <el-button size=\"small\" type=\"primary\" @click=\"openInfoForm()\" icon=\"el-icon-plus\"\n                       v-has-per=\"'dm:Delivery:save'\">\n              创建送货单\n            </el-button>\n            <el-button size=\"small\" icon=\"el-icon-delete\" type=\"danger\" @click=\"delDelivery()\">\n              删除\n            </el-button>\n            <el-tooltip effect=\"dark\" :content=\"$t('common.refresh')\" placement=\"top\">\n              <el-link icon=\"icon-ym icon-ym-Refresh DIAN-common-head-icon\" :underline=\"false\" @click=\"search()\" />\n            </el-tooltip>\n            <d-screen-full/>\n          </div>\n        </div>\n\n        <!-- 表格 -->\n        <d-table ref=\"listTable\" v-loading=\"listLoading\" :data=\"list\" hasC @selection-change=\"handleSelectionChange\" show-summary>\n          <el-table-column prop=\"deptName\" label=\"采购组织\" show-overflow-tooltip width=\"150\" align=\"center\"/>\n          <el-table-column prop=\"deNo\" label=\"送货单号/序号\" align=\"center\" show-overflow-tooltip width=\"130\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.deNo + '/' + scope.row.seq}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"saleNo\" label=\"订单号/序号\" align=\"center\" show-overflow-tooltip width=\"145\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.saleNo + '/' + scope.row.purSeq}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"goodsErpCode\" label=\"ERP物料编码\" align=\"center\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column prop=\"goodsName\" label=\"物料名称\" align=\"center\" show-overflow-tooltip width=\"120\"/>\n          <el-table-column prop=\"goodsModel\" label=\"物料描述\" align=\"center\" show-overflow-tooltip width=\"180\"/>\n          <el-table-column prop=\"deStat\" label=\"单据状态\" align=\"center\" show-overflow-tooltip width=\"80\">\n            <template slot-scope=\"scope\">\n              <span>{{scope.row.deStat | commonEnumsTurn('dm.DeliveryDeStatEnum')}}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"auxUomName\" label=\"单位\" align=\"center\" show-overflow-tooltip width=\"90\"/>\n          <el-table-column prop=\"devNum\" label=\"送货数量\" align=\"center\" show-overflow-tooltip width=\"90\"/>\n          <el-table-column prop=\"temNum\" label=\"暂收数量\" align=\"center\" show-overflow-tooltip width=\"100\"/>\n<!--          <el-table-column prop=\"invNum\" label=\"验收入库数量\" align=\"center\" show-overflow-tooltip width=\"120\"/>-->\n<!--          <el-table-column prop=\"unInvNum\" label=\"未验收数量\" align=\"center\" show-overflow-tooltip width=\"120\"/>-->\n          <el-table-column prop=\"realityDeliveryDate\" label=\"实际送货日期\" align=\"center\" show-overflow-tooltip width=\"120\">\n            <template slot-scope=\"scope\">\n              <span>{{ $dian.dateFormat(scope.row.realityDeliveryDate, 'YYYY-MM-DD') }}</span>\n            </template>\n          </el-table-column>\n          <!-- <el-table-column prop=\"arrivalDate\" label=\"到货日期\" align=\"center\" show-overflow-tooltip width=\"120\">\n            <template slot-scope=\"scope\">\n              <span>{{ $dian.dateFormat(scope.row.arrivalDate, 'YYYY-MM-DD') }}</span>\n            </template>\n          </el-table-column> -->\n          <el-table-column prop=\"lineRemark\" label=\"行备注\" show-overflow-tooltip/>\n          <el-table-column label=\"操作\" fixed=\"right\" width=\"50\">\n            <template slot-scope=\"scope\">\n              <el-button size=\"mini\" type=\"text\" @click=\"openInfoForm(scope.row.id)\"\n                         v-has-per=\"'dm:deliveryItem:info'\">查看\n              </el-button>\n            </template>\n          </el-table-column>\n        </d-table>\n        <d-pagination :total=\"total\" :page.sync=\"queryParam.page\" :limit.sync=\"queryParam.limit\" @pagination=\"initData\"/>\n      </div>\n    </div>\n    <Form v-show=\"formVisible\" ref=\"form\" @callRefreshList=\"callDeliveryBoardList\"/>\n  </div>\n", null]}