{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\form.vue?vue&type=template&id=23c1b3f8&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\form.vue", "mtime": 1754288817332}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n<transition name=\"el-zoom-in-center\">\n  <div class=\"DIAN-preview-main nohead\">\n    <div class=\"DIAN-common-page-header\">\n      <el-page-header @back=\"goBack\" :content=\"isAdd?'编辑送样单':'新建送样单'\"/>\n      <div class=\"options\">\n        <el-button @click=\"goBack()\">{{ $t('common.cancelButton')}}</el-button>\n        <el-button type=\"primary\" @click=\"submit()\" :loading=\"btnLoading\" v-if=\"dataForm.demandClassType == 1\" v-has-per=\"'base:sample:update'\">保存</el-button>\n        <el-button type=\"primary\" :loading=\"btnLoading\"  @click=\"replySample()\" v-if=\"dataForm.demandClassType == 1\" v-has-per=\"'base:sample:replySample'\">答交</el-button>\n        <el-button type=\"primary\" :loading=\"btnLoading\"  @click=\"rejectSample()\" v-if=\"dataForm.demandClassType == 1\" v-has-per=\"'base:sample:rejectSample'\">拒绝</el-button>\n        <el-button type=\"primary\" :loading=\"btnLoading\"  @click=\"confirmSample()\" v-if=\"dataForm.demandClassType == 1\" v-has-per=\"'base:sample:confirmSample'\">确认送样</el-button>\n        <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"receiveMaterial\" v-has-per=\"'base:sample:confirmRecSample'\">确认收样</el-button>\n      </div>\n    </div>\n    <div class=\"app-container Document-container nohead\">\n      <el-form ref=\"dataForm\" :model=\"dataForm\" :rules=\"dataRule\" label-width=\"125px\" id=\"dataForm\">\n        <d-card ref=\"dCard\" :flow=\"false\" v-model=\"activeName\">\n        <d-card-item label=\"基础信息\" name=\"form\">\n          <div class=\"DIAN-flex-main\" v-loading=\"loading\">\n             <el-col>\n              <el-col :span=\"6\">\n                 <el-form-item label=\"PLM打样单号\">\n                   <el-input v-model.trim=\"dataForm.sourceNo\" placeholder=\"<系统自动生成>\" readonly :disabled=\"true\"></el-input>\n                 </el-form-item>\n               </el-col>\n               <el-col :span=\"6\">\n                 <el-form-item label=\"送样单号\">\n                   <el-input v-model.trim=\"dataForm.sampleNo\" placeholder=\"<系统自动生成>\" readonly :disabled=\"true\"></el-input>\n                 </el-form-item>\n               </el-col>\n               <el-col :span=\"6\">\n                 <el-form-item label=\"采购组织\" prop=\"deptId\">\n                   <el-select v-model=\"dataForm.deptId\" :disabled=\"true\">\n                     <el-option\n                       v-for=\"item in DeptNameDist\"\n                       :key=\"item.id\"\n                       :label=\"item.deptName\"\n                       :value=\"item.id\"\n                     />\n                   </el-select>\n                 </el-form-item>\n               </el-col>\n               <el-col :span=\"6\">\n                 <el-form-item label=\"供应商编码\" prop=\"vendorCode\">\n                   <el-input v-model.trim=\"dataForm.vendorCode\" readonly placeholder=\"请选择供应商\">\n                     <el-button slot=\"append\" icon=\"el-icon-search\"  @click=\"choiceVendor()\" :disabled=\"true\">\n                     </el-button>\n                   </el-input>\n                 </el-form-item>\n               </el-col>\n               <el-col :span=\"6\">\n                 <el-form-item label=\"供应商名称\" prop=\"vendorName\">\n                   <el-input v-model.trim=\"dataForm.vendorName\" placeholder=\"选择后自动带出\" disabled>\n                   </el-input>\n                 </el-form-item>\n               </el-col>\n               <!-- <el-col :span=\"6\">\n                 <el-form-item label=\"送样时间\" prop=\"sampleDate\">\n                   <el-date-picker v-model.trim=\"dataForm.sampleDate\" type=\"datetime\" placeholder=\"<送样时间>\"\n                                   value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" :disabled=\"true\">\n                   </el-date-picker>\n                 </el-form-item>\n               </el-col> -->\n               <el-col :span=\"6\">\n                 <el-form-item label=\"是否上传资质文件\" prop=\"sampleStat\">\n                   <el-select v-model.trim=\"dataForm.isNeedUpFile\" disabled>\n                     <el-option v-for=\"item in whetherOpts\" :key=\"item.key\"\n                                :label=\"item.value\" :value=\"parseInt(item.key)\">\n                     </el-option>\n                   </el-select>\n                 </el-form-item>\n               </el-col>\n                <!-- <el-col :span=\"6\">\n                  <el-form-item label=\"单据状态\" prop=\"sampleStat\">\n                    <el-select v-model.trim=\"dataForm.sampleStat\" disabled>\n                      <el-option v-for=\"item in statOptions\" :key=\"item.key\"\n                                 :label=\"item.value\" :value=\"parseInt(item.key)\">\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col> -->\n                <el-col :span=\"6\">\n                  <el-form-item label=\"创建人\" prop=\"creater\">\n                    <el-input v-model.trim=\"dataForm.creater\" placeholder=\"<系统自动生成>\" disabled>\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"创建时间\" prop=\"createDate\">\n                    <el-date-picker v-model.trim=\"dataForm.createDate\" type=\"datetime\" placeholder=\"<系统自动生成>\"\n                                    disabled value-format=\"yyyy-MM-dd HH:mm:ss\" format=\"yyyy-MM-dd HH:mm:ss\"\n                                    :editable=\"false\">\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"修改人\" prop=\"modifier\">\n                    <el-input v-model.trim=\"dataForm.modifier\" placeholder=\"<系统自动生成>\" disabled>\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"修改时间\" prop=\"modifyDate\">\n                    <el-date-picker v-model.trim=\"dataForm.modifyDate\" type=\"datetime\" placeholder=\"<系统自动生成>\"\n                                    disabled value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\"\n                                    :editable=\"false\">\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"单据类型\" prop=\"demandClassType\">\n                    <el-select v-model.trim=\"dataForm.demandClassType\" disabled>\n                      <el-option v-for=\"item in demandTypeOpts\" :key=\"item.key\"\n                                 :label=\"item.value\" :value=\"parseInt(item.key)\">\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"remark\">\n                    <el-input v-model.trim=\"dataForm.remark\" placeholder=\"请输入备注\" type=\"textarea\" :rows=\"3\" clearable/>\n                  </el-form-item>\n                </el-col>\n              </el-col>\n          </div>\n          <!--供应商选择弹窗-->\n          <VendorProp ref=\"vendor\" :singleChoice=\"true\" @callData=\"vendorSelect\"></VendorProp>\n        </d-card-item>\n        <d-card-item label=\"物料明细\" name=\"sampleItemEntityList\">\n          <div class=\"DIAN-flex-main\">\n            <!-- <div class=\"table-toolbars\">\n              <el-button type=\"primary\" plain size=\"mini\" icon=\"el-icon-plus\" :disabled=\"(dataForm.wfStatus)>0\"\n                         @click=\"choiceGoods()\">\n                {{ $t('common.addBtn')}}\n              </el-button>\n              <el-button type=\"danger\" plain size=\"mini\" icon=\"el-icon-minus\" :disabled=\"(dataForm.wfStatus)>0\"\n                         @click=\"delLine('sampleItemEntityList')\">\n                {{ $t('common.delBtn')}}\n              </el-button>\n            </div> -->\n            <d-table :data=\"dataForm.sampleItemEntityList\" size='mini' hasC ref=\"sampleItemEntityList\" @selection-change=\"handleSelectionChange\">\n              <!-- <el-table-column prop=\"itemStat\" label=\"检验状态\" align=\"center\" width=\"100\">\n                <template slot-scope=\"scope\">\n                  <span>{{scope.row.itemStat | commonEnumsTurn('base.SampleItemEnums')}}</span>\n                </template>\n              </el-table-column> -->\n              <el-table-column prop=\"purName\" label=\"采购员\" align=\"center\" width=\"120\"/>\n              <el-table-column prop=\"goodsCode\" label=\"物料编码\" align=\"center\" width=\"120\"/>\n              <el-table-column prop=\"goodsName\" label=\"物料名称\" align=\"center\" width=\"150\" show-overflow-tooltip/>\n              <el-table-column prop=\"goodsModel\" label=\"规格型号\" align=\"center\" width=\"150\" show-overflow-tooltip/>\n              <el-table-column prop=\"demandQty\" label=\"需求数量\" align=\"center\" width=\"100\"/>\n              <el-table-column prop=\"demandDate\" label=\"需求日期\" align=\"center\" width=\"150\">\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.demandDate | date }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"replyQuantity\" label=\"回复数量\" align=\"center\" width=\"180\">\n                <template slot-scope=\"scope\">\n                  <el-input-number :controls=\"false\" v-model.trim=\"scope.row.replyQuantity\" :precision=\"3\" clearable :disabled=\"dataForm.sampleStat > 3 || scope.row.replyState === 2\" @change=\"(val) => handleReplyQuantityChange(val, scope.row)\"/>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"replyDeliveryDate\" label=\"回复交期\" align=\"center\" width=\"180\">\n                <template slot-scope=\"scope\">\n                  <el-date-picker v-model.trim=\"scope.row.replyDeliveryDate\" type=\"datetime\"\n                                  value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\"\n                                  :editable=\"false\" :disabled=\"dataForm.sampleStat > 3 || scope.row.replyState === 2\">\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"replyState\" label=\"答交状态\" align=\"center\" width=\"150\">\n                <template slot-scope=\"scope\">\n                  <span>{{scope.row.replyState | commonEnumsTurn('base.ReplyStateEnum')}}</span>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"goodsNum\" label=\"送样数量\" align=\"center\" width=\"185\">\n                <template slot-scope=\"scope\">\n                  <el-input-number :controls=\"false\" v-model.trim=\"scope.row.goodsNum\" :precision=\"3\" clearable :disabled=\"dataForm.sampleStat > 3\"/>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"caseDate\" label=\"送样日期\" align=\"center\" width=\"180\">\n                <template slot-scope=\"scope\">\n                  <el-date-picker v-model.trim=\"scope.row.caseDate\" type=\"datetime\" placeholder=\"<系统自动生成>\"\n                                   value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\"\n                                  :editable=\"false\" disabled >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"caseStat\" label=\"结案状态\" align=\"center\" width=\"150\">\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.caseStat | commonEnumsTurn('base.SampleEnums') }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"quaFilePath\" label=\"资质文件\" show-overflow-tooltip width=\"150\">\n                <template slot-scope=\"scope\">\n                  <el-button type=\"text\" v-if=\"scope.row.quaFilePath\" @click=\"lookFile(scope.row.quaFilePath)\">\n                    <span>{{scope.row.tenantFileName}}</span>\n                  </el-button>\n                </template>\n              </el-table-column>\n              <!-- <el-table-column prop=\"remark\" label=\"不合格描述\" align=\"center\" show-overflow-tooltip width=\"200\" /> -->\n              <el-table-column prop=\"vendorRemark\" label=\"供方说明\" show-overflow-tooltip width=\"150\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.vendorRemark\" clearable v-show=\"dataForm.sampleStat == 3\">\n                  </el-input>\n                  <span v-show=\"dataForm.sampleStat != 3\">{{scope.row.vendorRemark}}</span>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" width=\"180\">\n                <template slot-scope=\"scope\">\n                  <el-button size=\"mini\" type=\"text\" @click=\"uploadFlie(scope.row,scope.$index)\">\n                    上传资质文件\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click=\"handleDrawing(scope.row.goodsErpCode)\">查看图纸</el-button>\n                </template>\n              </el-table-column>\n            </d-table>\n            <!--选择产品信息  singleChoice是否为单选 true为单选 false为多选-->\n            <GoodsPopup ref=\"goods\" :singleChoice=\"false\" @callData=\"getSelectGoods\"></GoodsPopup>\n            <d-file-upLoad :multiple=\"true\" ref=\"fileUpload\" @fileUpload=\"upload\"></d-file-upLoad>\n            <LookDescribe ref=\"lookDescribe\"></LookDescribe>\n          </div>\n        </d-card-item>\n        <d-card-item label=\"附件明细\" name=\"doc\">\n          <el-tabs :active-name=\"docActiveName\">\n            <el-tab-pane label=\"采购方相关文档\" name=\"tenant\">\n              <d-doc-table-list ref=\"docTableList\" tableName=\"base_sample\" :headId=\"dataForm.id\" :lineId=\"1\"/>\n            </el-tab-pane>\n            <el-tab-pane label=\"供应方相关文档\" name=\"vendor\">\n              <d-doc-table-list ref=\"docTableList\" tableName=\"base_sample\" :headId=\"dataForm.id\" :lineId=\"2\" :disabled=\"true\"/>\n            </el-tab-pane>\n          </el-tabs>\n        </d-card-item>\n        </d-card>\n      </el-form>\n    </div>\n  </div>\n</transition>\n", null]}