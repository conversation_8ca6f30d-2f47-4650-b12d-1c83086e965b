{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\order\\produce\\vendor\\codePrintTemplate.vue", "mtime": 1754292527998}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { hiprint,defaultElementTypeProvider} from 'vue-plugin-hiprint'\r\nimport {MessageBox} from 'element-ui'\r\nlet hiprintTemplate;\r\n//业务导入\r\nimport { getPrintTemplateList } from '@/api/base/printTemplate'\r\nimport { queryGoodsBigBagAndSmallBarCodeListV2 } from '@/api/base/goodsBarCode'\r\nimport { generateBarcodeByPurOrder } from '@/api/order/sale'\r\n\r\nexport default {\r\n  name: \"printDesign\",\r\n  props: {\r\n    title: {type: String, default: '打印条码'},\r\n  },\r\n  data() {\r\n    return {\r\n      visible:false,\r\n      loading:false,\r\n      btnLoading:false,\r\n      template:'',\r\n      templateId:1,\r\n      templateList:[],\r\n      packagingNum:'',\r\n      numberOfBoxes:null,\r\n      countless:null,\r\n      devSumNum:null,\r\n      goodsCode:null,\r\n      goodsName:null,\r\n      dataForm: {\r\n        id: null,\r\n        tenantPId: 0,\r\n        templateName: '',\r\n        sceneId: null,\r\n        sceneCode: '',\r\n        sceneName: '',\r\n        remark: '',\r\n        printJson: '',\r\n        devSumNum: '',\r\n      },\r\n      deLineData: {\r\n\r\n      },\r\n      printData:[],\r\n      curPaper: {\r\n        type: 'A4',\r\n        width: 210,\r\n        height: 296.6\r\n      },\r\n      isBatchPrint:true,\r\n      paperTypes: {\r\n        'A3': {\r\n          width: 420,\r\n          height: 296.6\r\n        },\r\n        'A4': {\r\n          width: 210,\r\n          height: 296.6\r\n        },\r\n        'A5': {\r\n          width: 210,\r\n          height: 147.6\r\n        },\r\n        'B3': {\r\n          width: 500,\r\n          height: 352.6\r\n        },\r\n        'B4': {\r\n          width: 250,\r\n          height: 352.6\r\n        },\r\n        'B5': {\r\n          width: 250,\r\n          height: 175.6\r\n        }\r\n      },\r\n      scaleMax: 5,\r\n      scaleMin: 0.5,\r\n      paperWidth: '210',\r\n      paperHeight: '296.6',\r\n      dataRule: {\r\n        goodsCode: [\r\n          {required: true, message: '请选择物料', trigger: 'blur'}\r\n        ],\r\n        productDate: [\r\n          {required: true, message: '请选择生产日期', trigger: 'blur'}\r\n        ],\r\n        bigPackStandardNum: [\r\n          {required: true, message: '请输入条码大包数量', trigger: 'blur'}\r\n        ],\r\n        smallPackStandardNum: [\r\n          {required: true, message: '请输入条码小包数量', trigger: 'blur'}\r\n        ],\r\n        devNum: [\r\n          {required: true, message: '请输入送货数量', trigger: 'blur'}\r\n        ]\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    curPaperType() {\r\n      let type = 'other'\r\n      let types = this.paperTypes\r\n      for (const key in types) {\r\n        let item = types[key]\r\n        let {width, height} = this.curPaper\r\n        if (item.width === width && item.height === height) {\r\n          type = key\r\n        }\r\n      }\r\n      return type\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  watch: {\r\n    'deLineData.bigPackStandardNum': {\r\n      handler(newVal) {\r\n        // 将 newVal 强制转为数值类型\r\n        const numericNewVal = parseInt(newVal, 10);\r\n\r\n        // 校验 devNum 是否为有效正整数\r\n        const devNum = this.deLineData.devNum;\r\n        if (typeof devNum !== 'number' || devNum <= 0 || !Number.isInteger(devNum)) {\r\n          this.deLineData.bigPackLabelNum = 0;\r\n          this.deLineData.bigPackMantissa = 0;\r\n          return;\r\n        }\r\n\r\n        // 校验 newVal 是否为有效除数（已转为数值）\r\n        if (isNaN(numericNewVal) || numericNewVal <= 0) {\r\n          this.deLineData.bigPackStandardNum = devNum;\r\n        }\r\n\r\n        // 计算大包条码张数和尾数\r\n        this.deLineData.bigPackLabelNum = Math.floor(devNum / numericNewVal);\r\n        this.deLineData.bigPackMantissa = devNum % numericNewVal;\r\n      }\r\n    },\r\n    'deLineData.smallPackStandardNum': {\r\n      deep: true,\r\n      handler(newVal) {\r\n        // 将 newVal 强制转为数值类型\r\n        const numericNewVal = parseInt(newVal, 10);\r\n\r\n        // 校验 devNum 是否为有效正整数\r\n        const devNum = this.deLineData.devNum;\r\n        if (typeof devNum !== 'number' || devNum <= 0 || !Number.isInteger(devNum)) {\r\n          this.deLineData.smallPackLabelNum = 0;\r\n          this.deLineData.smallPackMantissa = 0;\r\n          return;\r\n        }\r\n\r\n        // 校验 newVal 是否为有效除数（已转为数值）\r\n        if (isNaN(numericNewVal) || numericNewVal <= 0) {\r\n          this.deLineData.smallPackStandardNum = devNum;\r\n        }\r\n\r\n        // 计算大包条码张数和尾数\r\n        this.deLineData.smallPackLabelNum = Math.floor(devNum / numericNewVal);\r\n        this.deLineData.smallPackMantissa = devNum % numericNewVal;\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    initData (params) {\r\n      this.deLineData = this.$options.data().deLineData;\r\n      // 使用 JSON 深拷贝避免引用污染\r\n      this.deLineData = JSON.parse(JSON.stringify(params));\r\n\r\n    },\r\n    async init(sceneCode) {\r\n      // $('#hiprint-printTemplate').empty()\r\n      this.loading = true;\r\n      //查场景数据\r\n      getPrintTemplateList({sceneCode:sceneCode}).then((res) => {\r\n        this.templateList=res.page.list;\r\n        //查模板数据\r\n        // getPrintTemplateInfo(id).then((res) => {\r\n        this.dataForm = res.page.list[res.page.list.length-1];\r\n        this.templateId= this.dataForm.id;\r\n        this.template = JSON.parse(this.dataForm.printJson.split(\"\").length > 0 ? this.dataForm.printJson : '{}');\r\n        let newVar = this;\r\n        newVar.hiprint = function () {\r\n          hiprint.init({\r\n            providers: [new defaultElementTypeProvider()]\r\n          });\r\n          hiprintTemplate = new hiprint.PrintTemplate({\r\n            template: this.template, // 模板json\r\n            settingContainer: '#PrintElementOptionSetting', // 元素参数容器\r\n            paginationContainer: '.hiprint-printPagination' // 多面板的容器， 实现多面板， 需要在添加一个 <div class=\"hiprint-printPagination\"/>\r\n          });\r\n          hiprintTemplate.design('#hiprint-printTemplate')\r\n        }\r\n        this.hiprint();\r\n        this.visible = true;\r\n      });\r\n\r\n    },\r\n    getTemplate(data){\r\n      this.dataForm = data;\r\n      this.templateId= this.dataForm.id;\r\n      this.$forceUpdate()\r\n      this.template = JSON.parse(this.dataForm.printJson.split(\"\").length > 0 ? this.dataForm.printJson : '{}');\r\n      let newVar = this;\r\n      newVar.hiprint = function () {\r\n        hiprint.init({\r\n          providers: [new defaultElementTypeProvider()]\r\n        });\r\n        hiprintTemplate = new hiprint.PrintTemplate({\r\n          template: this.template, // 模板json\r\n          settingContainer: '#PrintElementOptionSetting', // 元素参数容器\r\n          paginationContainer: '.hiprint-printPagination' // 多面板的容器， 实现多面板， 需要在添加一个 <div class=\"hiprint-printPagination\"/>\r\n        });\r\n        // var panel = hiprintTemplate.addPrintPanel(this.template);\r\n        hiprintTemplate.design('#hiprint-printTemplate')\r\n      }\r\n      this.hiprint();\r\n    },\r\n    async print(type){\r\n      this.printData = []\r\n      await queryGoodsBigBagAndSmallBarCodeListV2({sourceId: this.deLineData.sourceId,goodsId: this.deLineData.goodsId,sourceType:1,barType: type }).then(res => {\r\n        this.printData = res.data;\r\n      }).catch(err => {\r\n        console.log(err)\r\n      })\r\n      if(!Array.isArray(this.printData) || this.printData.length === 0) {\r\n        this.$message.error('请选择打印数据');\r\n        return;\r\n      }\r\n      console.log('数据',this.printData)\r\n      if(this.isBatchPrint){\r\n        var html = \"\";\r\n        for(var i=0;i<this.printData.length;i++){\r\n          html+=hiprintTemplate.getHtml(this.printData[i])[0].innerHTML;\r\n        }\r\n        var dd=hiprintTemplate.getHtml(this.printData[0]);\r\n        dd[0].innerHTML=html;\r\n        dd.hiwprint();\r\n      }else{\r\n        hiprintTemplate.print(this.printData);\r\n      }\r\n      if (window.hiwebSocket.opened) {\r\n        const printerList = hiprintTemplate.getPrinterList();\r\n        console.log(printerList) // 打印机列表this.tasksPrint()return}this.$message.error('客户端未连接,无法直接打印')\r\n      }\r\n    },\r\n    // 生成条码\r\n    generateBarcode(){\r\n      debugger\r\n      this.$refs['deLineData'].validate((valid) => {\r\n        if (valid){\r\n          if (this.deLineData.bigPackStandardNum < this.deLineData.smallPackStandardNum){\r\n            this.$message.error('大包条码数量不能小于小包条码数量! 请重新调整大包条码数量');\r\n            return\r\n          }\r\n          if (this.deLineData.smallPackStandardNum > this.deLineData.bigPackStandardNum){\r\n            this.$message.error('小包条码数量不能大于大包条码数量! 请重新调整小包条码数量');\r\n            return\r\n          }\r\n          if (this.deLineData.bigPackStandardNum > this.deLineData.devNum){\r\n            this.$message.error('大包条码数量不能大于订单物料数量! 请重新调整大包条码数量');\r\n            return\r\n          }\r\n          if (this.deLineData.smallPackStandardNum > this.deLineData.devNum){\r\n            this.$message.error('小包条码数量不能大于订单物料数量! 请重新调整小包条码数量');\r\n            return\r\n          }\r\n          generateBarcodeByPurOrder(this.deLineData).then((res) => {\r\n            this.$message.success('条码生成成功');\r\n          }).catch((err) => {\r\n            this.$message.error('条码生成失败');\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}