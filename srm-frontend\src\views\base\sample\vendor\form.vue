<template>
  <transition name="el-zoom-in-center">
    <div class="DIAN-preview-main nohead">
      <div class="DIAN-common-page-header">
        <el-page-header @back="goBack" :content="isAdd?'编辑送样单':'新建送样单'"/>
        <div class="options">
          <el-button type="primary" @click="submit()" :loading="btnLoading" v-has-per="'base:sample:update'">保存</el-button>
          <el-button type="primary" :loading="btnLoading"  @click="replySample()" v-has-per="'base:sample:replySample'">答交</el-button>
          <el-button type="primary" @click="rejectSample()" :loading="btnLoading" v-has-per="'base:sample:rejectSample'">拒绝</el-button>
          <!-- <el-button type="primary" @click="acceptSample()" :loading="btnLoading">确认需要送样</el-button> -->
          <el-button type="primary" @click="confirmSample()" :loading="btnLoading" v-has-per="'base:sample:confirmSample'">确认送样</el-button>
          <el-button @click="goBack()">{{ $t('common.cancelButton')}}</el-button>
        </div>
      </div>
      <div class="app-container Document-container nohead">
        <el-form ref="dataForm" :model="dataForm" :rules="dataRule" label-width="125px" id="dataForm">
          <d-card ref="dCard" :flow="false" v-model="activeName">
          <d-card-item label="基础信息" name="form">
            <div class="DIAN-flex-main" v-loading="loading">
              <el-row>
                <el-col :span="6">
                   <el-form-item label="PLM打样单号">
                     <el-input v-model.trim="dataForm.sourceNo" placeholder="<系统自动生成>" readonly :disabled="true"></el-input>
                   </el-form-item>
                 </el-col>
                <el-col :span="6">
                  <el-form-item label="送样单号">
                    <el-input v-model.trim="dataForm.sampleNo" placeholder="<系统自动生成>" readonly :disabled="true"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户名称" prop="deptName">
                    <el-input v-model.trim="dataForm.deptName" readonly placeholder="客户名称" disabled>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="供应商编码" prop="vendorCode">
                    <el-input v-model.trim="dataForm.vendorCode" readonly placeholder="供应商编码" disabled>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="供应商名称" prop="vendorName">
                    <el-input v-model.trim="dataForm.vendorName" placeholder="供应商名称" readonly disabled>
                    </el-input>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
                  <el-form-item label="送样时间" prop="sampleDate">
                    <el-date-picker v-model.trim="dataForm.sampleDate" type="datetime" placeholder="<送样时间>"
                                    value-format="yyyy-MM-dd" format="yyyy-MM-dd" :disabled="true">
                    </el-date-picker>
                  </el-form-item>
                </el-col> -->
                <el-col :span="6">
                  <el-form-item label="是否上传资质文件" prop="sampleStat">
                    <el-select v-model.trim="dataForm.isNeedUpFile" disabled>
                      <el-option v-for="item in whetherOpts" :key="item.key"
                                 :label="item.value" :value="parseInt(item.key)">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
                  <el-form-item label="单据状态" prop="sampleStat">
                    <el-select v-model.trim="dataForm.sampleStat" disabled>
                      <el-option v-for="item in statOptions" :key="item.key"
                                 :label="item.value" :value="parseInt(item.key)">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="6">
                  <el-form-item label="创建人" prop="creater">
                    <el-input v-model.trim="dataForm.creater" placeholder="<系统自动生成>" disabled>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="创建时间" prop="createDate">
                    <el-date-picker v-model.trim="dataForm.createDate" type="datetime" placeholder="<系统自动生成>"
                                    disabled value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                                    :editable="false">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="修改人" prop="modifier">
                    <el-input v-model.trim="dataForm.modifier" placeholder="<系统自动生成>" disabled>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="修改时间" prop="modifyDate">
                    <el-date-picker v-model.trim="dataForm.modifyDate" type="datetime" placeholder="<系统自动生成>"
                                    disabled value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                                    :editable="false">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="单据类型" prop="demandClassType">
                      <el-select v-model.trim="dataForm.demandClassType" disabled>
                        <el-option v-for="item in demandTypeOpts" :key="item.key"
                                   :label="item.value" :value="parseInt(item.key)">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model.trim="dataForm.remark" placeholder="请输入备注" type="textarea" :rows="3" disabled/>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <!--供应商选择弹窗-->
            <VendorProp ref="vendor" :singleChoice="true" @callData="vendorSelect"></VendorProp>
          </d-card-item>
          <d-card-item label="物料明细" name="sampleItemEntityList">
            <div class="DIAN-flex-main">
              <div class="table-toolbars">
              </div>
              <d-table :data="dataForm.sampleItemEntityList" size='mini' hasC ref="sampleItemEntityList" @selection-change="handleSelectionChange">
                <!-- <el-table-column prop="itemStat" label="检验状态" align="center" width="100" >
                  <template slot-scope="scope">
                    <span>{{scope.row.itemStat | commonEnumsTurn('base.SampleItemEnums')}}</span>
                  </template>
                </el-table-column> -->
                <el-table-column prop="purName" label="采购员" align="center" width="120"/>
                <el-table-column prop="goodsCode" label="物料编码" align="center" width="120"/>
                <el-table-column prop="goodsName" label="物料名称" align="center" width="150" show-overflow-tooltip/>
                <el-table-column prop="goodsModel" label="规格型号" align="center" width="150" show-overflow-tooltip/>
                <el-table-column prop="demandQty" label="需求数量" align="center" width="150"/>
                <el-table-column prop="demandDate" label="需求日期" align="center" width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row.demandDate | date }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="replyQuantity" label="回复数量" align="center" width="180">
                  <template slot-scope="scope">
                    <el-input-number :controls="false" v-model.trim="scope.row.replyQuantity" :precision="3" clearable :disabled="dataForm.sampleStat > 3 || scope.row.replyState === 2" @change="(val) => handleReplyQuantityChange(val, scope.row)"/>
                  </template>
                </el-table-column>
                <el-table-column prop="replyDeliveryDate" label="回复交期" align="center" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model.trim="scope.row.replyDeliveryDate" type="datetime"
                                    value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                                    :editable="false" :disabled="dataForm.sampleStat > 3 || scope.row.replyState === 2">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="replyState" label="答交状态" align="center" width="150">
                  <template slot-scope="scope">
                    <span>{{scope.row.replyState | commonEnumsTurn('base.ReplyStateEnum')}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="goodsNum" label="送样数量" align="center" width="185">
                  <template slot-scope="scope">
                    <el-input-number :controls="false" v-model.trim="scope.row.goodsNum" :precision="3" clearable :disabled="dataForm.sampleStat > 3"/>
                  </template>
                </el-table-column>
                <el-table-column prop="caseDate" label="送样日期" align="center" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model.trim="scope.row.caseDate" type="datetime" placeholder="<系统自动生成>"
                                    value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                                    :editable="false" disabled>
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="caseStat" label="结案状态" align="center" width="150">
                  <template slot-scope="scope">

                    <span>{{ scope.row.caseStat | commonEnumsTurn('base.SampleEnums') }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="quaFilePath" label="资质文件" align="center" show-overflow-tooltip width="180">
                  <template slot-scope="scope">
                    <el-button type="text" v-show="scope.row.quaFilePath" @click="lookFile(scope.row.quaFilePath)">
                      <span>{{scope.row.vendorFileName}}</span>
                    </el-button>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="remark" label="不合格描述" align="center" show-overflow-tooltip width="200" /> -->
                <el-table-column prop="vendorRemark" label="供方说明" align="center" show-overflow-tooltip width="200">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.vendorRemark" clearable v-show="dataForm.sampleStat == 3">
                    </el-input>
                    <span v-show="dataForm.sampleStat != 3">{{scope.row.vendorRemark}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" align="center" width="180">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="uploadFlie(scope.row,scope.$index)">
                      上传资质文件
                    </el-button>
                    <el-button type="text" size="mini" @click="handleDrawing(scope.row.goodsErpCode)">查看图纸</el-button>
                  </template>
                </el-table-column>
              </d-table>
              <!--选择产品信息  singleChoice是否为单选 true为单选 false为多选-->
              <GoodsPopup ref="goods" :singleChoice="false" @callData="getSelectGoods"></GoodsPopup>
              <d-file-upLoad :multiple="true" ref="fileUpload" @fileUpload="upload"></d-file-upLoad>
            </div>
          </d-card-item>
          <d-card-item label="附件明细" name="doc">
            <el-tabs :active-name="docActiveName">
              <el-tab-pane label="采购方相关文档" name="tenant">
                <d-doc-table-list ref="docTableList" tableName="base_sample" :headId="dataForm.id" :lineId="1" :tenantId="dataForm.tenantId" :disabled="true"/>
              </el-tab-pane>
              <el-tab-pane label="供应方相关文档" name="vendor">
                <d-doc-table-list ref="docTableList" tableName="base_sample" :headId="dataForm.id" :lineId="2"/>
              </el-tab-pane>
            </el-tabs>
          </d-card-item>
          </d-card>
        </el-form>
      </div>
    </div>
  </transition>
</template>

<script>
import { confirmSample,getSampleInfo,updateSample,acceptSample,rejectSample,replySample,createSample } from '@/api/base/sample'
import {getClassInfoByCode} from '@/api/base/classManage'
import store from '@/store'
import {handleGoodsDrawing} from '@/api/base/goods'
import GoodsPopup from "@/views/popup/base/goods/goodsPopup";
import VendorProp from '@/views/popup/base/vendor/vendor'
export default {
  components: {
    GoodsPopup,
    VendorProp,
  },
  data() {
    return {
      isUpload:true,
      isAdd:true,
      dataForm: {
        id: 0,//送样通知单主表id
        tenantId:'',
        tenantName:'',
        sampleNo: '',//送样单号
        vendorId:'',//供应商id
        vendorCode:'',//供应商编码
        vendorErpCode:'',//供应商Erp编码
        vendorName:'',//供应商名称
        deptId:'',//机构id
        deptCode:'',//机构编码
        deptName:'',//机构名称
        sampleDate:'',//要求送样日期
        sampleStat: 1,//单据状态 1-待审核;2-待送样;3-待质检;4-质检退回;5-已审核;9-已作废
        isNeedUpFile: null,
        remark: '',//备注
        sampleItemEntityList: [],//送样单明细数据数组
      },
      selectedMaterialItems: [], // 选中的物料明细行(多选)
      index:'',
      docActiveName: 'tenant', // doc文档选项卡
      btnLoading: false,//控制按钮是否正在加载条件
      loading: false,//控制页面是否正在加载条件
      activeName: 'form',
      statOptions: store.getters.commonEnums['base.SampleEnums'],//送样单单据状态枚举类
      whetherOpts: store.getters.commonEnums['comm.ValidEnum'],//送样单单据状态枚举类
      demandTypeOpts: store.getters.commonEnums['base.DemandClassTypeEnum'],
      dataRule: {
        isNeedUpFile:[
          {required: true, message: '是否需要上传资质文件不能为空', trigger: 'input'},
        ],
        vendorCode:[
          {required: true, message: '供应商编码不能为空', trigger: 'input'},
        ],
        vendorName:[
          {required: true, message: '供应商名称不能为空', trigger: 'input'},
        ],
      },
    }
  },
  methods: {
    //新增页面或查看详情
    init(id) {
      if (this.$refs.dataForm);
      this.docActiveName = 'vendor';
      this.$refs.dataForm.resetFields();
      this.activeName='form';
      this.dataForm = this.$options.data().dataForm;
      this.isAdd=id?true:false;
      this.dataForm.id = this.isAdd ? id : new Date().getTime();
      this.$nextTick(() => {
        if (id) {
          this.loading = true;
          getSampleInfo(id).then(res => {
            this.dataForm = res.data;
            if (this.dataForm.sampleItemEntityList && this.dataForm.sampleItemEntityList.length > 0) {
              this.dataForm.sampleItemEntityList.forEach(item => {
                if (item.replyQuantity && (!item.goodsNum || item.goodsNum === 0)) {
                  item.goodsNum = item.replyQuantity;
                }
              });
            }
            this.loading = false;
          }).catch(res => {
            this.loading = false;
          })
        }
      })
    },
    //保存
    submit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          //明细行数据校验
          const isLineValid = this.lineCheck();
          if(!isLineValid){
            this.btnLoading = false;
            return;
          }
          const formMethod = this.isAdd ? updateSample : createSample;
          formMethod(this.dataForm).then((res) => {
            this.isAdd = true;
            this.$message({
              message: "保存成功",
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.btnLoading = false
                if(res.data){
                  this.loading = true;
                  getSampleInfo(res.data).then(res => {
                    this.dataForm = res.data;
                    this.loading = false;
                  })
                }
              }
            })
          }).catch((res) => {
            this.btnLoading = false
          })
        }
      })
    },
    //返回列表
    goBack() {
      this.$emit('callRefreshList');
    },
    //打开供应商弹窗
    choiceVendor() {
      this.$nextTick(() => {
        this.$refs.vendor.init();
      })
    },
    //获取已选择的供应商赋值
    vendorSelect(data) {
      this.dataForm.vendorId = data[0].id;//供应商id
      this.dataForm.vendorCode = data[0].vendorCode;//供应商编码
      this.dataForm.vendorErpCode = data[0].vendorErpCode;//供应商Erp编码
      this.dataForm.vendorName = data[0].vendorName;//供应商名称
    },
    //打开物料选择弹窗
    choiceGoods(){
      if(!this.dataForm.vendorId){
        this.$message.error('供应商信息为空，请先选择供应商');
        return
      }
      this.$nextTick(() => {
        this.$refs.goods.init();
      })
    },
    //获取已选择的物料
    getSelectGoods(list) {
      //当送样明细数据数组长度大于0时，校验选择的数据是否有在需求申请明细数据中已存在，已存在的需要抛出错误信息
      if (this.dataForm.sampleItemEntityList.length > 0) {
        for (let i = 0; i < list.length; i++) {
          const e = list[i];
          for (let j = 0; j < this.dataForm.sampleItemEntityList.length; j++) {
            if (this.dataForm.sampleItemEntityList[j].goodsId == e.id) {
              this.$message.error('选择的产品编码' + e.goodsCode + '已存在，请勿重复添加');
              return
            }
          }
        }
      }
      this.addSampleItem(list);
    },
    //新增明细
    addSampleItem(list){
      if(list.length > 0){
        list.forEach((item,index) => {
          let sampleItem = {
            id:null,
            tenantId:'',
            sampleId:'',//送样单主表id
            goodsId:item.id,//物料id
            goodsErpCode:item.goodsErpCode,//ERP物料编码
            goodsCode:item.goodsCode,//物料编码
            goodsName:item.goodsName,//物料名称
            goodsModel:item.goodsModel,//物料规格
            goodsNum:item.goodsNum,//送样数量
            itemStat:1,//明细行状态 1-待审核;2-待送样;3-待质检
            vendorRemark:'',//供应商说明
            remark:'',//采购方说明
          }
          this.dataForm.sampleItemEntityList.push(sampleItem);
        })
      }
    },
    //删除单条产品明细
    handleDel(index, name, row) {
      this.dataForm[name].splice(index, 1);
    },
    //批量明细删除
    delLine(lineName) {
      this.$confirm('是否批量删除所选明细', '提示', {
        type: 'warning'
      }).then(() => {
        let rowThis = this;
        //获取已选择数据
        rowThis.$refs[lineName].$refs.DIANTable.selection.map((orw) => {
          //获取列表数据
          rowThis.dataForm[lineName].forEach((dataRow, index) => {
            //判断是否为同一行
            if (orw.__ob__.dep.id == dataRow.__ob__.dep.id) {
              //删除当前行
              rowThis.dataForm[lineName].splice(index, 1);
            }
          })
        })
        this.$message({
          type: 'success',
          message: '删除成功',
          duration: 1500,
        })
      }).catch(() => {
      })
    },
    //校验明细行
    lineCheck(){
      if (this.dataForm.sampleItemEntityList.length == 0){
        this.btnLoading = false;
        this.$message.error('请添加物料明细');
        return false;
      }
      // let lineList = this.dataForm.sampleItemEntityList;
      // for (let i = 0; i < lineList.length; i++) {
      //   const item = lineList[i];
      //   const idx = i + 1;
      //   if(!item.goodsNum){
      //      this.btnLoading = false;
      //      this.$message({type: 'error', message: '物料信息第'+idx+'行的送样数量为空'});
      //      throw new Error()
      //    }
      //   // 资质文件校验
      //   if (this.dataForm.isNeedUpFile === 1 && !item.vendorFilePath) {
      //     this.btnLoading = false;
      //     this.$message.error(`物料信息第${idx}行的资质文件为空，请上传`);
      //     return false; // 校验失败，返回false
      //   }
      // }
      return true; // 校验通过，返回true
    },
    // 确认送样
    confirmSample(){
      // 检查是否选择了物料明细行
      if(this.selectedMaterialItems.length === 0) {
        this.$message.error("请选择要确认送样的物料明细行需求");
        return;
      }

      // 检查选中的物料明细行状态
      const invalidItems = this.selectedMaterialItems.filter(item => item.replyState != 2);
      if(invalidItems.length > 0) {
        this.$message.error('选中的物料明细中有未答交的需求，无法确认送样');
        return;
      }

      // 检查送样数量是否大于0
      const noQuantityItems = this.selectedMaterialItems.filter(item => !item.goodsNum || item.goodsNum <= 0);
      if(noQuantityItems.length > 0) {
        this.$message.error('选中的物料明细中有送样数量为空或小于等于0的记录，请先填写送样数量');
        return;
      }

      // 检查是否有已确认送样的明细行 (SampleEnums.STAT4 = 4 待收样)
      const confirmedItems = this.selectedMaterialItems.filter(item => item.caseStat == 4);
      if(confirmedItems.length > 0) {
        this.$message.error('选中的物料明细中有已确认送样的需求，请勿重复确认');
        return;
      }

      // 检查是否有已拒绝的明细行 (SampleEnums.STAT11 = 11 已拒绝)
      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseStat == 11);
      if(rejectedItems.length > 0) {
        this.$message.error('选中的物料明细中有已拒绝的需求，无法确认送样');
        return;
      }

      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          const isLineValid = this.lineCheck();
          if(!isLineValid){
            this.btnLoading = false;
            return;
          }
          const formMethod = this.isAdd ? updateSample : createSample;
          formMethod(this.dataForm).then((res) => {
            if(res.data){
              this.dataForm.id = res.data;

              // 构建批量确认送样的参数
              const ids = this.selectedMaterialItems.map(item => item.id);
              console.log("批量确认送样 =======> ", ids);

              confirmSample(ids).then(res => {
                this.$message({
                  message: `成功确认${ids.length}个物料明细送样`,
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.btnLoading = false;
                    this.selectedMaterialItems = []; // 重置选中项
                    if(res.data){
                      this.loading = true;
                      getSampleInfo(this.dataForm.id).then(res => {
                        this.dataForm = res.data;
                        this.loading = false;
                      })
                    }
                  }
                })
              }).catch(err => {
                this.btnLoading = false
              })
            } else {
              this.btnLoading = false;
            }
          }).catch((err) => {
            this.btnLoading = false
          })
        }
      })
    },
    //上传文件
    uploadFlie(rowData,index){
      this.index = index;
      this.$refs.fileUpload.init("base_sample_item",index,0);
    },
    //获取上传文件后返回的文件路径
    upload(res){
      // this.dataForm.sampleItemEntityList[this.index].vendorFileName = res.data.name;
      // this.dataForm.sampleItemEntityList[this.index].vendorFilePath = res.data.src;
      this.dataForm.sampleItemEntityList[this.index].quaFileName = res.data.name;
      this.dataForm.sampleItemEntityList[this.index].quaFilePath = res.data.src;
    },
    //查看文件
    lookFile(url){
      window.open(url);
    },
    //跳转图纸网址
    handleDrawing(val){
      const params={
        goodsErpCode:val
      }
      handleGoodsDrawing(params).then(res =>{
        window.open(res.data);
      }).catch((res) => {})
    },
    //确认需要送样
    acceptSample(){
      this.btnLoading = true
      acceptSample(this.dataForm).then(res => {
        this.$message({
          message: "确认成功",
          type: 'success',
          duration: 1500,
          onClose: () => {
            this.btnLoading = false
            if(res.data){
              this.loading = true;
              getSampleInfo(res.data).then(res => {
                this.dataForm = res.data;
                this.loading = false;
              })
            }
          }
        })
      }).catch(res => {
        this.btnLoading = false
      })
    },
    //拒绝该送样通知单
    rejectSample(){
      if(this.selectedMaterialItems.length === 0) {
        this.$message.error("请选择要拒绝的物料明细行需求");
        return;
      }
      
      // 检查是否有已拒绝或已答交的明细行
      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseState == 11);
      if(rejectedItems.length > 0) {
        this.$message.error('选中的物料明细中有已拒绝的需求，请勿重复拒绝');
        return;
      }
      
      const repliedItems = this.selectedMaterialItems.filter(item => item.replyState == 2);
      if(repliedItems.length > 0) {
        this.$message.error('选中的物料明细中有已答交的需求，不允许拒绝');
        return;
      }
      
      this.$confirm('确认拒绝选中的物料明细行需求?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.btnLoading = true;
        
        // 构建批量拒绝的参数
        const ids = this.selectedMaterialItems.map(item => item.id);
        console.log("批量拒绝送样 =======> ", ids);
        
        rejectSample(ids).then(res => {
          this.$message({
            message: `成功拒绝${ids.length}个物料明细`,
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.btnLoading = false;
              this.selectedMaterialItems = []; // 重置选中项
              if(res.data){
                this.loading = true;
                getSampleInfo(this.dataForm.id).then(res => {
                  this.dataForm = res.data;
                  this.loading = false;
                })
              }
            }
          })
        }).catch(res => {
          this.btnLoading = false;
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消拒绝'
        });
      });
    },
    // 答交送样
    replySample(){
      if(this.selectedMaterialItems.length === 0) {
        this.$message.error("请选择要答交的物料明细行需求");
        return;
      }

      // 检查是否上传了资质文件
      if(this.dataForm.isNeedUpFile === 1) {
        const noFileItems = this.selectedMaterialItems.filter(item => !item.quaFilePath);
        if(noFileItems.length > 0) {
          this.$message.error('选中的物料明细中有未上传资质文件的记录，请先上传资质文件');
          return;
        }
      }

      // 检查是否有已拒绝或已答交的明细行
      const rejectedItems = this.selectedMaterialItems.filter(item => item.caseState == 11);
      if(rejectedItems.length > 0) {
        this.$message.error('选中的物料明细中有已拒绝的需求，不允许答交');
        return;
      }

      // 检查回复数量是否为空或小于等于0
      const emptyReplyItems = this.selectedMaterialItems.filter(item => !item.replyQuantity || item.replyQuantity <= 0);
      if(emptyReplyItems.length > 0) {
        this.$message.error('选中的物料明细中有回复数量为空或小于等于0的记录，请先填写回复数量');
        return;
      }

      // 检查回复交期是否为空
      const emptyReplyDateItems = this.selectedMaterialItems.filter(item => !item.replyDeliveryDate);
      if(emptyReplyDateItems.length > 0) {
        this.$message.error('选中的物料明细中有回复交期为空的记录，请先填写回复交期');
        return;
      }
      
      const repliedItems = this.selectedMaterialItems.filter(item => item.replyState == 2);
      if(repliedItems.length > 0) {
        this.$message.error('选中的物料明细中有已答交的需求，请勿重复答交');
        return;
      }
      
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          const isLineValid = this.lineCheck();
          if(!isLineValid){
            this.btnLoading = false;
            return;
          }
          const formMethod = this.isAdd ? updateSample : createSample;
          formMethod(this.dataForm).then((res) => {
            if(res.data){
              this.dataForm.id = res.data;
              
              // 构建批量答交的参数
              const ids = this.selectedMaterialItems.map(item => item.id);    
              console.log("批量答交送样 =======> ", ids);           
              replySample(ids).then(res => {
                this.$message({
                  message: `成功答交${ids.length}个物料明细`,
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.btnLoading = false;
                    this.selectedMaterialItems = []; // 重置选中项
                    if(res.data){
                      this.loading = true;
                      getSampleInfo(this.dataForm.id).then(res => {
                        this.dataForm = res.data;
                        this.loading = false;
                      })
                    }
                  }
                })
              }).catch(res => {
                this.btnLoading = false;
              });
            } else {
              this.btnLoading = false;
            }
          }).catch((err) => {
            this.btnLoading = false
          })
        }
      })
    },
    handleSelectionChange(selection) {
      this.selectedMaterialItems = selection;
    },
    handleReplyQuantityChange(val, row) {
      if (val !== null && val !== undefined) {
        row.goodsNum = val;
      }
    }
  }
}
</script>
