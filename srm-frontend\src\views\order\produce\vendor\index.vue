<template>
  <div class="DIAN-common-layout">
    <div class="DIAN-common-layout-center">

      <!-- 搜索框 -->
      <el-row class="DIAN-common-search-box" :gutter="24">
        <el-form @submit.native.prevent>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.purNo" placeholder="请输入采购订单号" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-select v-model="queryParam.itemStat" placeholder="请选择单据状态" clearable>
                <el-option
                  :key="item.key"
                  :label="item.value"
                  :value="parseInt(item.key)"
                  v-for="item in orderStatOptions"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-input v-model.trim="queryParam.goods" placeholder="请输入物料编码/名称/型号" clearable/>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.purName" placeholder="请输入采购员名称" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <template v-if="showAll">
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="queryParam.orderDate"
                  type="daterange"
                  placeholder="请输入订单日期"
                  range-separator="至"
                  start-placeholder="（订单）开始日期"
                  end-placeholder="（订单）结束日期"
                  clearable>
                </el-date-picker>
              </el-form-item>
            </el-col>
          </template>
<!--          <el-col :span="5">-->
<!--            <el-form-item>-->
<!--              <el-select v-model="queryParam.bsart" placeholder="请选择采购类型" clearable>-->
<!--                <el-option-->
<!--                  v-for="item in bsartTypeOptions"-->
<!--                  :key="item"-->
<!--                  :label="item.value"-->
<!--                  :value="item.key"-->
<!--                />-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->

          <el-col :span="6">
            <el-form-item>
               <!-- 查询按钮 -->
              <el-button type="primary" icon="el-icon-search" @click="search()">
                {{ $t('common.search') }}
              </el-button>
              <!-- 重置按钮 -->
              <el-button icon="el-icon-refresh-right" @click="reset()">
                {{ $t('common.reset') }}
              </el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true"
                         v-if="!showAll">展开
              </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                收起
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <!-- body -->
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <!-- 表头工具栏 -->
        <div class="DIAN-common-head">
          <div>
          </div>
          <div class="DIAN-common-head-right">
            <el-button type="primary"
                       @click="oneClickBatchConfirm()"
                       :loading="btnLoading" >
              批量确认
            </el-button>
            <el-button size="small"
                       type="primary"
                       @click="openCreateFrom()"
                       icon="el-icon-plus"
                       v-has-per="'dm:Delivery:save'">
              创建送货单
            </el-button>
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false" @click="search()" />
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>

        <!-- 表格 -->
        <d-table ref="listTable" v-loading="listLoading" :data="list" hasC :hasNO="false" selection @selection-change="handleSelectionChange">
          <el-table-column prop="purNo" label="采购订单号" align="center" show-overflow-tooltip width="130" fixed="left">
            <template slot-scope="scope">
              {{ scope.row.purNo + '/' + scope.row.seq }}
            </template>
          </el-table-column>
          <el-table-column prop="goodsErpCode" label="物料编码" show-tooltip-when-overflow width="130" fixed="left"/>
          <el-table-column prop="goodsName" label="物料名称" show-tooltip-when-overflow width="120" fixed="left"/>
          <el-table-column prop="goodsModel" label="规格型号" show-tooltip-when-overflow width="180" fixed="left"/>
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="orderType" label="订单类型" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{scope.row.orderType | commonEnumsTurn("common.JinDieOrderTypeEnum")}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderDate" label="订单日期" align="center" show-overflow-tooltip width="150">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.orderDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" label="发布时间" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="totalAmount" label="含税总金额" align="center" show-overflow-tooltip width="150" v-if="$dian.hasPerBtnP('order:vendor:lookPrice')">
            <template slot-scope="scope">
              <span>{{scope.row.gstPrice * scope.row.orderNum}}</span>
            </template>
          </el-table-column>
          <!-- 明细行数据 -->
          <el-table-column prop="itemStat" label="单据状态" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.itemStat | commonEnumsTurn("order.PurlineStatEnum") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isClose" label="是否关闭" show-tooltip-when-overflow width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.isClose === 1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column prop="erpChangeType" label="变更类型" show-tooltip-when-overflow width="100"/>
          <el-table-column prop="deliveryStatus" label="送货状态" width="100"/>
          <el-table-column prop="orderNum" label="订单数量" width="100"/>
          <el-table-column prop="makeNum" label="已制单数量" width="100"/>
          <el-table-column prop="unMakeNum" label="未制单数量" width="100"/>
          <el-table-column prop="fixNum" label="已送数量" width="100"/>
          <el-table-column prop="waitNum" label="待送数量" width="100"/>
          <el-table-column prop="receiveNum" label="暂收数量" width="100"/>
          <el-table-column prop="refundNum" label="暂退补料数量" width="100"/>
          <el-table-column prop="refDedNum" label="暂退扣款数量" width="100"/>
          <el-table-column prop="erpMasterNum" label="入库数量" width="100"/>
          <el-table-column prop="erpRejectNum" label="退货补料数量" width="100"/>
          <el-table-column prop="retDedNum" label="退货扣款数量" width="100"/>
          <el-table-column prop="uomName" label="单位" width="100"/>
          <el-table-column prop="rateName" label="税率" width="100"/>
          <el-table-column prop="warehouseName" label="仓库" width="100"/>
          <el-table-column prop="deliveryDate" label="交货日期" width="150">
          </el-table-column>
          <el-table-column prop="taxPrice" label="不含税单价" width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>
          <el-table-column prop="gstPrice" label="含税单价" width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>

          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip min-width="180"/>
          <el-table-column label="操作" width="120" fixed="right" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="addEditOrderHandle(scope.row.id)" v-has-per="'order:sale:info'"> {{ $t('common.lookBtn')}} </el-button>
              <el-button size="mini" type="text" @click="printBarCode(scope.row)">条码打印</el-button>
            </template>
          </el-table-column>
          <CodePrintTemplate v-if="templateVisible" ref="PrintTemplate"/> <!-- 条码打印模版 -->
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>

      <!-- FORM表单 -->
      <Form ref="form" v-show="formVisible" @callRefreshList="closeForm"></Form>
      <deliveryForm ref="deliveryForm" v-show="deliveryFormVisible" @callRefreshList="closeForm"></deliveryForm>
    </div>
  </div>
</template>

<script>

import {dFlowMixin} from "@dian/dian-ui-vue";
import Form from './Form';
import store from "@/store";
import {getVendorSaleOrderList,batchConfirmOrder} from "@/api/order/sale";
import {getSubCompanyId} from "@/utils/auth";
import dian from "@/utils/dian";
import deliveryForm from '@/views/dm/deliveryBoard/vendor/form.vue';
import CodePrintTemplate from './codePrintTemplate'

export default {
  //加载底层公有组件
  mixins: [dFlowMixin],
  name: "order-produce-vendor",
  components: {
    Form,
    deliveryForm,
    CodePrintTemplate,
  },
  filters: {
    dateFormat(time) {
      if (!time) {
        return ''
      }
      let date = new Date(time)
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "-" + month + "-" + day;
    }
  },
  watch: {
    $route: {
      handler (to, from) {
        if (to.path === '/order/produce/vendor' && null != to.query.id) {
          this.addEditOrderHandle(to.query.id);
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型
      // orderStatOptions: store.getters.commonEnums['order.PurStatEnum'],
      orderStatOptions:[
        {key:1,value:'待确认'},
        {key:4,value:'已确认'},
      ],
      queryParam: {
        page: 1,
        limit: 20,
        keyword : '', // 订单号/客户名称
        orderDate : '',  // 订单日期
        dept : '',  // 订单日期
        bsart : '', // 采购类型
      },
      showAll: false,
      formVisible: false,
      deliveryFormVisible: false,
      templateVisible: false, // 是否显示模板弹窗
      listLoading: false,
      btnLoading: false,
      subCompanyInfo:getSubCompanyId()||{},//头部选择的采购组织信息
      list: [],
      total: 0,
      selectedDatas: [],
      selectedNum: 0
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.listLoading = true;
      let queryParams = { ...this.queryParam };
      if (this.queryParam.orderDate && this.queryParam.orderDate.length === 2) {
        try {
          const startDate = this.$dian.dateFormat(this.queryParam.orderDate[0], 'YYYY-MM-DD');
          const endDate = this.$dian.dateFormat(this.queryParam.orderDate[1], 'YYYY-MM-DD');
          queryParams.orderDate = startDate + " 至 " + endDate;
        } catch (error) {
          console.error('日期格式化错误:', error);
          queryParams.orderDate = '';
        }
      }
      let subCompanyInfoData = dian.storageGet('subCompanyInfo');
      if (subCompanyInfoData){
        queryParams.deptId = subCompanyInfoData.id;
      }
      getVendorSaleOrderList(queryParams).then(res => {
        this.total = res.page.totalCount;
        this.list = res.page.list;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      })
    },
    // 新增|编辑 项目报备
    addEditOrderHandle(id) {
      this.formVisible = true;
      this.$nextTick(() => {
        this.$refs.form.init(id);
      })
    },
    // 批量确认
    oneClickBatchConfirm(){
      if (this.selectedDatas.length === 0) {
        this.$message({
          message: '请至少选择一条记录',
          type: 'warning',
          duration: 1500
        });
        return;
      }
      // 检查是否有已确认的订单
      const confirmedOrders = this.selectedDatas.filter(item => item.stat === 5);
      if (confirmedOrders.length > 0) {
        const confirmedOrderNos = confirmedOrders.map(item => item.purNo).join('、');
        this.$message({
          message: `选中的订单中存在已确认的订单：${confirmedOrderNos}，请重新选择`,
          type: 'warning',
          duration: 3000
        });
        return;
      }
      this.$confirm('是否批量确认所选订单？确认后将无法撤销', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.btnLoading = true;
        const ids = this.selectedDatas.map(item => item.id);
        batchConfirmOrder(ids).then(res => {
          this.$message({
            message: '批量确认成功',
            type: 'success',
            duration: 1500
          });
          this.btnLoading = false;
          this.initData();
        }).catch(() => {
          this.btnLoading = false;
        })
      }).catch(() => {
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedDatas = selection.map(item => item)
      //获取所有选中项数组的长度
      this.selectedNum = selection.length
    },
    //打开创建送货单弹窗
    openCreateFrom(){
      //校验是否有选择数据
      if (this.selectedNum === 0) {
        this.deliveryFormVisible = true;
        this.$nextTick(() => {
          this.$refs.deliveryForm.init(null);
        })
      } else {
        //为表格多选选中的数组
        let list = this.selectedDatas;
        //校验需要创建送货单的数据
        this.judgeSelectCreateData(list);
        let tenantInfo = this.getCreateTenantData(list)
        let vendorInfo = this.getCreateVendorData(list);
        let seq = 0;
        list.forEach((item,index) => {
          seq = index+1;
          item.id = null;
          item.seq = seq;
          item.tempRowId = `${new Date().getTime()}_${Math.random().toString(36).slice(2, 10)}`;
        })
        list.sort((a,b)=>{
          if(a['goodsErpCode']!==b['goodsErpCode']){
            return a['goodsErpCode'].localeCompare(b['goodsErpCode']);
          }
        })
        let createData = {
          tenantId:tenantInfo.tenantId,//采购方id
          tenantName:tenantInfo.tenantName,//采购方名称
          vendorId:vendorInfo.vednorId,//供应商id
          vendorCode:vendorInfo.vendorCode,//供应商编码
          vendorName:vendorInfo.vendorName,//供应商名称
          deptId:vendorInfo.deptId,//机构id
          deptCode:vendorInfo.deptCode,//机构编码
          deptName:vendorInfo.deptName,//机构名称
          address:vendorInfo.address,//送货地址
          shippingAddress:vendorInfo.shippingAddress,//送货地址
          deliveryType:vendorInfo.deliveryType,//送货类型
          is:vendorInfo.is,
          documentDate:new Date(),//创建送货单日期（默认为当前时间）不可修改
          deliveryDate:new Date(),//实际送货时间（默认当前时间）可修改
          isEle:0,//是否物流
          logisticsName:'',//物流公司
          eleNo:'',//物流单号
          purchaserId:vendorInfo.purchaserId,//采购员id
          deliveryItemEntityList:list,//送货单创建明细数据
        }
        this.deliveryFormVisible = true;
        this.$nextTick(() => {
          this.$refs.deliveryForm.init(createData);
        })
      }
    },
    //校验需要创建送货单的数据
    judgeSelectCreateData(list){
      //从已选择数据根据采购方（客户）获取每一个租户id
      const tenantIds = list.map(value => value.tenantId);
      //去除重复的采购方id数据
      const tenantIdsSet = new Set(tenantIds);
      //去重后的采购方id数据
      let tenantIdList = [...tenantIdsSet];
      //若去重后的采购方id数据长度大于1时
      if(tenantIdList.length > 1){
        this.$message.error('只能选择同一个采购方（客户）的单据创建送货单');
        throw new Error()
      }

      //从已选择数据根据采购方（客户）获取每一个采购组织机构id
      const deptIds = list.map(value => value.deptId);
      //去除重复的采购组织id数据
      const deptIdsSet = new Set(deptIds);
      //去重后的采购方id数据
      let deptIdList = [...deptIdsSet];
      //若去重后的采购方id数据长度大于1时
      if(deptIdList.length > 1){
        this.$message.error('只能选择同一个采购组织（客户）的单据创建送货单');
        throw new Error()
      }

      //从已选择数据根据地址
      const address = list.map(value => value.shippingAddress);
      //去除重复的地址数据
      const addressSet = new Set(address);
      //去重后的地址数据
      let addressList = [...addressSet];
      // if(addressList.length > 1){
      //   this.$message.error('存在不同交货地址的单据不允许创建在同一张送货单');
      //   throw new Error()
      // }

      if (tenantIdList[0] === 24739){
        //从已选择数据根据送货单过滤获取每一条的订单类型
        const orderType = list.map(value => value.orderType);
        //去除重复的订单类型数据
        const orderTypeSet = new Set(orderType);
        //去重后的订单类型数据
        let orderTypeList = [...orderTypeSet];
        if(orderTypeList.length > 1){
          this.$message.error('存在不同订单类型的单据不允许创建在同一张送货单');
          throw new Error()
        }

        //从已选择数据根据送货单过滤获取每一条的订单业务类型
        const orderBusType = list.map(value => value.orderBusType);
        //去除重复的订单业务类型数据
        const orderBusTypeSet = new Set(orderBusType);
        //去重后的订单业务类型数据
        let orderBusTypeList = [...orderBusTypeSet];
        if(orderBusTypeList.length > 1){
          this.$message.error('存在不同订单业务类型的单据不允许创建在同一张送货单');
          throw new Error()
        }
      }

      //从已选择数据根据送货单过滤获取每一条的送货类型
      const deliveryType = list.map(value => value.deliveryType);
      //去除重复的送货类型数据
      const deliveryTypeSet = new Set(deliveryType);
      //去重后的送货类型数据
      let deliveryTypeList = [...deliveryTypeSet];
      if(deliveryTypeList.length > 1){
        this.$message.error('存在不同送货方式的单据不允许创建在同一张送货单');
        throw new Error()
      }

      //从已选择数据根据送货单过滤获取每一条的仓库
      const warehouseCode = list.map(value => value.warehouseCode);
      //去除重复的送货类型数据
      const warehouseCodeSet = new Set(warehouseCode);
      //去重后的送货类型数据
      let warehouseCodeList = [...warehouseCodeSet];
      if(warehouseCodeList.length > 1){
        this.$message.error('当前选择的数据中存在不同仓库地址，不允许创建在同一张送货单');
        throw new Error()
      }

      //从已选择数据根据采购员过滤获取每一条的送货类型
      const purId = list.map(value => value.purId);
      //去除重复的采购员数据
      const purIdSet = new Set(purId);
      //去重后的采购员数据
      let purIdList = [...purIdSet];
      // if(purIdList.length > 1){
      //   this.$message.error('存在两个或两个以上不同采购员的单据不允许创建在同一张送货单');
      //   throw new Error()
      // }

      let idx = 0;
      list.forEach((item,index) => {
        idx=index+1;
        // 当前日期小于最早可提前送货日期时
        let nowDate = new Date();
        let inAdvanceDeliveryDate = new Date(item.inAdvanceDeliveryDate);
        if(nowDate < inAdvanceDeliveryDate){
          this.$message.error('目前已选择的数据中存在当前日期小于最早可提前送货日期'+item.inAdvanceDeliveryDate+'，无法创建送货单');
          throw new Error()
        }
        //已选择数据中的可制单数量小于0/等于0
        if(parseFloat(item.canMakeNum) <= 0){
          this.$message.error('第'+idx+'行物料['+item+']的可制单数量小于等于0，无法创建送货单，请刷新页面');
          throw new Error()
        }
      })
    },
    //获取采购方（客户）基本信息
    getCreateTenantData(list){
      let tenantInfoData = {}
      //从已选择数据根据采购方（客户）获取每一个租户id
      const tenantIds = list.map(value => value.tenantId);
      //去除重复的采购方id数据
      const tenantIdsSet = new Set(tenantIds);
      //去重后的采购方id数据
      let tenantIdList = [...tenantIdsSet];
      tenantInfoData.tenantId = tenantIdList[0];

      //从已选择数据根据采购方（客户）获取每一个租户名称
      const tenantNames = list.map(value => value.tenantName);
      //去除重复的采购方名称数据
      const tenantNamesSet = new Set(tenantNames);
      //去重后的采购方名称数据
      let tenantNameList = [...tenantNamesSet];
      tenantInfoData.tenantName = tenantNameList[0];
      return tenantInfoData;
    },
    //获取当前供应商基本信息
    getCreateVendorData(list){
      let vendorData = {};
      //从已选择数据根据供应商id获取每一个供应商id
      const vednorIds = list.map(value => value.vendorId);
      //去除重复的供应商id数据
      const vednorIdsSet = new Set(vednorIds);
      //去重后的供应商id数据
      let vednorIdList = [...vednorIdsSet];
      vendorData.vednorId = vednorIdList[0];

      //从已选择数据根据机构id获取每一个机构id
      const deptIds = list.map(value => value.deptId);
      //去除重复的机构id数据
      const deptIdsSet = new Set(deptIds);
      //去重后的机构id数据
      let deptIdList = [...deptIds];
      vendorData.deptId = deptIdList[0];

      //从已选择数据根据机构编码获取每一个机构编码
      const deptCodes = list.map(value => value.deptCode);
      //去除重复的机构编码数据
      const deptCodesSet = new Set(deptCodes);
      //去重后的机构编码数据
      let deptCodeList = [...deptCodesSet];
      vendorData.deptCode = deptCodeList[0];

      //从已选择数据根据机构名称获取每一个机构名称
      const deptNames = list.map(value => value.deptName);
      //去除重复的机构名称数据
      const deptNamesSet = new Set(deptNames);
      //去重后的机构名称数据
      let deptNameList = [...deptNamesSet];
      vendorData.deptName = deptNameList[0];

      //从已选择数据根据地址过滤获取每一个地址
      const address = list.map(value => value.vendorDeliveryAddress);
      //去除重复的地址数据
      const addressSet = new Set(address);
      //去重后的地址数据
      let addressList = [...addressSet];
      vendorData.address = addressList[0];

      //从已选择数据根据地址过滤获取每一个地址
      const shippingAddress = list.map(value => value.shippingAddress);
      //去除重复的地址数据
      const shippingAddressSet = new Set(shippingAddress);
      //去重后的地址数据
      let shippingAddressList = [...shippingAddressSet];
      vendorData.shippingAddress = shippingAddressList[0];

      //从已选择数据根据送货单过滤获取每一条的送货类型
      const deliveryType = list.map(value => value.deliveryType);
      //去除重复的送货类型数据
      const deliveryTypeSet = new Set(deliveryType);
      //去重后的送货类型数据
      let deliveryTypeList = [...deliveryTypeSet];
      vendorData.deliveryType = deliveryTypeList[0];
      if(vendorData.deliveryType){
        vendorData.deliveryType = deliveryTypeList[0]
        if(vendorData.deliveryType == 2){
          vendorData.is = 1
        } else {
          vendorData.is = 0
        }
      } else {
        vendorData.deliveryType = 1
        vendorData.is = 0
      }

      //从已选择数据根据采购员过滤获取每一条的送货类型
      const purId = list.map(value => value.purId);
      //去除重复的采购员数据
      const purIdSet = new Set(purId);
      //去重后的采购员数据
      let purIdList = [...purIdSet];
      vendorData.purchaserId = purIdList[0];

      return vendorData;
    },
    // 搜索方法，并返回到第一页
    search() {
      this.queryParam.page = 1;
      this.initData();
    },
    // 重置方法
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.search();
    },
    // Form表单关闭时回调方法
    closeForm() {
      this.formVisible = false;
      this.deliveryFormVisible = false;
      this.initData();
    },
    // 打印条码
    printBarCode(data) {
      this.templateVisible = true
      let devNum = Number(data.orderNum) || 0

      // 从当前行数据中解构赋值获取所需信息
      const {
        tenant_id: tenantId,
        vendor_id: vendorId,
        vendor_code: vendorCode,
        vendor_name: vendorName,
        id: sourceId,
        pur_no: sourceNo,
        goods_id: goodsId,
        goods_code: goodsCode,
        goods_erp_code: goodsErpCode,
        goods_name: goodsName,
        goods_model: goodsModel,
        big_pack_standard_num: bigPackStandardNum,
        small_pack_standard_num: smallPackStandardNum,
        big_pack_label_num: bigPackLabelNum,
        small_pack_label_num: smallPackLabelNum,
        big_pack_mantissa: bigPackMantissa,
        small_pack_mantissa: smallPackMantissa
      } = data

      const printData = {
        tenantId,
        tenantPId: 0,
        vendorId,
        vendorCode,
        vendorName,
        sourceId,
        sourceNo,
        goodsId,
        goodsCode,
        goodsErpCode,
        goodsName,
        goodsModel,
        sourceType: 1,
        devNum,
        bigPackStandardNum,
        smallPackStandardNum,
        bigPackLabelNum,
        smallPackLabelNum,
        bigPackMantissa,
        smallPackMantissa
      }
      this.$nextTick(() => {
        this.$refs.PrintTemplate.isBatchPrint = false;
        this.$refs.PrintTemplate.initData(printData);
        this.$refs.PrintTemplate.init("goodsBarCodePrinting")
      })
    },
  }
}
</script>

<style scoped>

</style>
